/// شاشة إعدادات الرواتب
/// واجهة شاملة لإدارة مكونات الراتب والبدلات والاستقطاعات
library;

import 'package:flutter/material.dart';
import '../constants/revolutionary_design_colors.dart';
import '../models/hr_models.dart';
import '../services/salary_components_service.dart';
import '../services/logging_service.dart';
import '../widgets/loading_widget.dart';
import '../widgets/custom_error_widget.dart' as custom_widgets;

class SalarySettingsScreen extends StatefulWidget {
  const SalarySettingsScreen({super.key});

  @override
  State<SalarySettingsScreen> createState() => _SalarySettingsScreenState();
}

class _SalarySettingsScreenState extends State<SalarySettingsScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final SalaryComponentsService _salaryComponentsService =
      SalaryComponentsService();

  List<SalaryComponent> _allComponents = [];
  List<SalaryComponent> _allowances = [];
  List<SalaryComponent> _deductions = [];
  List<SalaryComponent> _bonuses = [];

  bool _isLoading = true;
  String? _error;
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final components = await _salaryComponentsService
          .getAllSalaryComponents();

      setState(() {
        _allComponents = components;
        _allowances = components.where((c) => c.type == 'allowance').toList();
        _deductions = components.where((c) => c.type == 'deduction').toList();
        _bonuses = components.where((c) => c.type == 'bonus').toList();
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });

      LoggingService.error(
        'خطأ في تحميل مكونات الراتب',
        category: 'SalarySettingsScreen',
        data: {'error': e.toString()},
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إعدادات الرواتب'),
        backgroundColor: RevolutionaryColors.damascusSky,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadData,
            tooltip: 'تحديث',
          ),
          PopupMenuButton<String>(
            icon: const Icon(Icons.more_vert),
            onSelected: (value) {
              switch (value) {
                case 'create_defaults':
                  _createDefaultComponents();
                  break;
                case 'export':
                  _exportComponents();
                  break;
                case 'import':
                  _importComponents();
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'create_defaults',
                child: Text('إنشاء المكونات الافتراضية'),
              ),
              const PopupMenuItem(
                value: 'export',
                child: Text('تصدير المكونات'),
              ),
              const PopupMenuItem(
                value: 'import',
                child: Text('استيراد المكونات'),
              ),
            ],
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          isScrollable: true,
          tabs: [
            Tab(
              icon: const Icon(Icons.list),
              text: 'الكل (${_allComponents.length})',
            ),
            Tab(
              icon: const Icon(Icons.add_circle),
              text: 'البدلات (${_allowances.length})',
            ),
            Tab(
              icon: const Icon(Icons.remove_circle),
              text: 'الاستقطاعات (${_deductions.length})',
            ),
            Tab(
              icon: const Icon(Icons.star),
              text: 'الحوافز (${_bonuses.length})',
            ),
          ],
        ),
      ),
      body: _buildBody(),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _showAddComponentDialog,
        backgroundColor: RevolutionaryColors.successGlow,
        icon: const Icon(Icons.add, color: Colors.white),
        label: const Text('إضافة مكون', style: TextStyle(color: Colors.white)),
      ),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const LoadingWidget();
    }

    if (_error != null) {
      return custom_widgets.ErrorWidget(message: _error!, onRetry: _loadData);
    }

    return TabBarView(
      controller: _tabController,
      children: [
        _buildComponentsList(_allComponents, 'جميع المكونات'),
        _buildComponentsList(_allowances, 'البدلات'),
        _buildComponentsList(_deductions, 'الاستقطاعات'),
        _buildComponentsList(_bonuses, 'الحوافز'),
      ],
    );
  }

  Widget _buildComponentsList(List<SalaryComponent> components, String title) {
    return Column(
      children: [
        // شريط البحث
        Container(
          padding: const EdgeInsets.all(16),
          child: TextField(
            decoration: InputDecoration(
              hintText: 'البحث في $title...',
              prefixIcon: const Icon(Icons.search),
              border: const OutlineInputBorder(),
            ),
            onChanged: (value) {
              setState(() {
                _searchQuery = value;
              });
            },
          ),
        ),
        // إحصائيات سريعة
        if (components.isNotEmpty) _buildQuickStats(components),
        // قائمة المكونات
        Expanded(child: _buildFilteredComponentsList(components)),
      ],
    );
  }

  Widget _buildQuickStats(List<SalaryComponent> components) {
    final activeCount = components.where((c) => c.isActive).length;
    final inactiveCount = components.length - activeCount;
    final percentageComponents = components.where((c) => c.isPercentage).length;
    final fixedComponents = components.length - percentageComponents;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: RevolutionaryColors.damascusSky.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: RevolutionaryColors.damascusSky.withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: _buildStatItem(
              'نشط',
              '$activeCount',
              RevolutionaryColors.successGlow,
            ),
          ),
          Expanded(
            child: _buildStatItem(
              'غير نشط',
              '$inactiveCount',
              RevolutionaryColors.errorCoral,
            ),
          ),
          Expanded(
            child: _buildStatItem(
              'نسبة مئوية',
              '$percentageComponents',
              RevolutionaryColors.warningAmber,
            ),
          ),
          Expanded(
            child: _buildStatItem(
              'مبلغ ثابت',
              '$fixedComponents',
              RevolutionaryColors.infoTurquoise,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value, Color color) {
    return Column(
      children: [
        Text(
          value,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: const TextStyle(fontSize: 12),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildFilteredComponentsList(List<SalaryComponent> components) {
    final filteredComponents = components.where((component) {
      if (_searchQuery.isEmpty) return true;
      final searchLower = _searchQuery.toLowerCase();
      return component.name.toLowerCase().contains(searchLower) ||
          component.code.toLowerCase().contains(searchLower) ||
          (component.description?.toLowerCase().contains(searchLower) ?? false);
    }).toList();

    if (filteredComponents.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              _getTypeIcon(
                components.isNotEmpty ? components.first.type : 'allowance',
              ),
              size: 64,
              color: Colors.grey,
            ),
            const SizedBox(height: 16),
            Text(
              _searchQuery.isEmpty ? 'لا توجد مكونات' : 'لا توجد نتائج للبحث',
              style: const TextStyle(fontSize: 18, color: Colors.grey),
            ),
            if (_searchQuery.isEmpty) ...[
              const SizedBox(height: 8),
              ElevatedButton.icon(
                onPressed: _showAddComponentDialog,
                icon: const Icon(Icons.add),
                label: const Text('إضافة مكون جديد'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: RevolutionaryColors.successGlow,
                ),
              ),
            ],
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: filteredComponents.length,
      itemBuilder: (context, index) {
        final component = filteredComponents[index];
        return _buildComponentCard(component);
      },
    );
  }

  Widget _buildComponentCard(SalaryComponent component) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ExpansionTile(
        leading: CircleAvatar(
          backgroundColor: _getTypeColor(component.type).withValues(alpha: 0.2),
          child: Icon(
            _getTypeIcon(component.type),
            color: _getTypeColor(component.type),
          ),
        ),
        title: Text(
          component.name,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Row(
          children: [
            Text(component.typeInArabic),
            const SizedBox(width: 8),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: component.isActive
                    ? RevolutionaryColors.successGlow.withValues(alpha: 0.2)
                    : Colors.grey.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                component.isActive ? 'نشط' : 'غير نشط',
                style: TextStyle(
                  fontSize: 10,
                  color: component.isActive
                      ? RevolutionaryColors.successGlow
                      : Colors.grey,
                ),
              ),
            ),
          ],
        ),
        trailing: PopupMenuButton<String>(
          onSelected: (value) {
            switch (value) {
              case 'edit':
                _showEditComponentDialog(component);
                break;
              case 'toggle':
                _toggleComponentStatus(component);
                break;
              case 'delete':
                _deleteComponent(component);
                break;
            }
          },
          itemBuilder: (context) => [
            const PopupMenuItem(value: 'edit', child: Text('تعديل')),
            PopupMenuItem(
              value: 'toggle',
              child: Text(component.isActive ? 'إلغاء التفعيل' : 'تفعيل'),
            ),
            const PopupMenuItem(
              value: 'delete',
              child: Text('حذف', style: TextStyle(color: Colors.red)),
            ),
          ],
        ),
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (component.description != null) ...[
                  Text(
                    component.description!,
                    style: const TextStyle(fontSize: 14),
                  ),
                  const SizedBox(height: 12),
                ],
                Row(
                  children: [
                    Expanded(
                      child: _buildComponentDetail('الرمز', component.code),
                    ),
                    Expanded(
                      child: _buildComponentDetail(
                        'المبلغ الافتراضي',
                        component.isPercentage
                            ? '${component.defaultAmount}%'
                            : component.defaultAmount.toStringAsFixed(0),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(
                      child: _buildComponentDetail(
                        'خاضع للضريبة',
                        component.isTaxable ? 'نعم' : 'لا',
                      ),
                    ),
                    Expanded(
                      child: _buildComponentDetail(
                        'نوع المبلغ',
                        component.isPercentage ? 'نسبة مئوية' : 'مبلغ ثابت',
                      ),
                    ),
                  ],
                ),
                if (component.isPercentage &&
                    component.percentageOf != null) ...[
                  const SizedBox(height: 8),
                  _buildComponentDetail(
                    'نسبة من',
                    component.percentageOf == 'basic_salary'
                        ? 'الراتب الأساسي'
                        : 'الراتب الإجمالي',
                  ),
                ],
                const SizedBox(height: 16),
                Row(
                  children: [
                    ElevatedButton.icon(
                      onPressed: () => _showEditComponentDialog(component),
                      icon: const Icon(Icons.edit),
                      label: const Text('تعديل'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: RevolutionaryColors.infoTurquoise,
                      ),
                    ),
                    const SizedBox(width: 12),
                    OutlinedButton.icon(
                      onPressed: () => _toggleComponentStatus(component),
                      icon: Icon(
                        component.isActive
                            ? Icons.visibility_off
                            : Icons.visibility,
                      ),
                      label: Text(
                        component.isActive ? 'إلغاء التفعيل' : 'تفعيل',
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildComponentDetail(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 2),
        Text(
          value,
          style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
        ),
      ],
    );
  }

  // دوال الأحداث والحوارات

  void _showAddComponentDialog() {
    _showComponentDialog();
  }

  void _showEditComponentDialog(SalaryComponent component) {
    _showComponentDialog(component: component);
  }

  void _showComponentDialog({SalaryComponent? component}) {
    final isEditing = component != null;
    final nameController = TextEditingController(text: component?.name ?? '');
    final codeController = TextEditingController(text: component?.code ?? '');
    final descriptionController = TextEditingController(
      text: component?.description ?? '',
    );
    final amountController = TextEditingController(
      text: component?.defaultAmount.toString() ?? '0',
    );

    String selectedType = component?.type ?? 'allowance';
    bool isPercentage = component?.isPercentage ?? false;
    bool isTaxable = component?.isTaxable ?? true;
    bool isActive = component?.isActive ?? true;
    String? percentageOf = component?.percentageOf;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          title: Text(isEditing ? 'تعديل مكون الراتب' : 'إضافة مكون راتب جديد'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  controller: nameController,
                  decoration: const InputDecoration(
                    labelText: 'اسم المكون *',
                    border: OutlineInputBorder(),
                  ),
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: codeController,
                  decoration: const InputDecoration(
                    labelText: 'رمز المكون *',
                    border: OutlineInputBorder(),
                    hintText: 'مثال: TRANS_ALLOW',
                  ),
                  textCapitalization: TextCapitalization.characters,
                ),
                const SizedBox(height: 16),
                DropdownButtonFormField<String>(
                  value: selectedType,
                  decoration: const InputDecoration(
                    labelText: 'نوع المكون *',
                    border: OutlineInputBorder(),
                  ),
                  items: const [
                    DropdownMenuItem(value: 'allowance', child: Text('بدل')),
                    DropdownMenuItem(
                      value: 'deduction',
                      child: Text('استقطاع'),
                    ),
                    DropdownMenuItem(value: 'bonus', child: Text('حافز')),
                  ],
                  onChanged: (value) {
                    setDialogState(() {
                      selectedType = value!;
                    });
                  },
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: descriptionController,
                  decoration: const InputDecoration(
                    labelText: 'الوصف',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 2,
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: TextField(
                        controller: amountController,
                        decoration: InputDecoration(
                          labelText: isPercentage
                              ? 'النسبة المئوية *'
                              : 'المبلغ *',
                          border: const OutlineInputBorder(),
                          suffixText: isPercentage ? '%' : 'ل.س',
                        ),
                        keyboardType: TextInputType.number,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Column(
                      children: [
                        const Text('نسبة مئوية'),
                        Switch(
                          value: isPercentage,
                          onChanged: (value) {
                            setDialogState(() {
                              isPercentage = value;
                              if (!value) percentageOf = null;
                            });
                          },
                        ),
                      ],
                    ),
                  ],
                ),
                if (isPercentage) ...[
                  const SizedBox(height: 16),
                  DropdownButtonFormField<String>(
                    value: percentageOf,
                    decoration: const InputDecoration(
                      labelText: 'نسبة من',
                      border: OutlineInputBorder(),
                    ),
                    items: const [
                      DropdownMenuItem(
                        value: 'basic_salary',
                        child: Text('الراتب الأساسي'),
                      ),
                      DropdownMenuItem(
                        value: 'gross_salary',
                        child: Text('الراتب الإجمالي'),
                      ),
                    ],
                    onChanged: (value) {
                      setDialogState(() {
                        percentageOf = value;
                      });
                    },
                  ),
                ],
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: CheckboxListTile(
                        title: const Text('خاضع للضريبة'),
                        value: isTaxable,
                        onChanged: (value) {
                          setDialogState(() {
                            isTaxable = value ?? true;
                          });
                        },
                      ),
                    ),
                    Expanded(
                      child: CheckboxListTile(
                        title: const Text('نشط'),
                        value: isActive,
                        onChanged: (value) {
                          setDialogState(() {
                            isActive = value ?? true;
                          });
                        },
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () async {
                if (nameController.text.trim().isEmpty ||
                    codeController.text.trim().isEmpty ||
                    amountController.text.trim().isEmpty) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('يرجى ملء جميع الحقول المطلوبة'),
                      backgroundColor: RevolutionaryColors.errorCoral,
                    ),
                  );
                  return;
                }

                // Store context references before any async operations
                final navigator = Navigator.of(context);
                final scaffoldMessenger = ScaffoldMessenger.of(context);

                try {
                  final newComponent = SalaryComponent(
                    id: component?.id,
                    name: nameController.text.trim(),
                    code: codeController.text.trim().toUpperCase(),
                    type: selectedType,
                    description: descriptionController.text.trim().isEmpty
                        ? null
                        : descriptionController.text.trim(),
                    defaultAmount: double.parse(amountController.text),
                    isPercentage: isPercentage,
                    percentageOf: percentageOf,
                    isTaxable: isTaxable,
                    isActive: isActive,
                    createdAt: component?.createdAt ?? DateTime.now(),
                    updatedAt: DateTime.now(),
                  );

                  if (isEditing) {
                    await _salaryComponentsService.updateSalaryComponent(
                      newComponent,
                    );
                  } else {
                    await _salaryComponentsService.createSalaryComponent(
                      newComponent,
                    );
                  }

                  if (mounted) {
                    navigator.pop();
                    await _loadData();

                    if (mounted) {
                      scaffoldMessenger.showSnackBar(
                        SnackBar(
                          content: Text(
                            isEditing
                                ? 'تم تحديث المكون بنجاح'
                                : 'تم إضافة المكون بنجاح',
                          ),
                          backgroundColor: RevolutionaryColors.successGlow,
                        ),
                      );
                    }
                  }
                } catch (e) {
                  if (mounted) {
                    scaffoldMessenger.showSnackBar(
                      SnackBar(
                        content: Text('خطأ في حفظ المكون: $e'),
                        backgroundColor: RevolutionaryColors.errorCoral,
                      ),
                    );
                  }
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: RevolutionaryColors.successGlow,
              ),
              child: Text(isEditing ? 'تحديث' : 'إضافة'),
            ),
          ],
        ),
      ),
    );
  }

  void _toggleComponentStatus(SalaryComponent component) async {
    try {
      await _salaryComponentsService.toggleComponentStatus(component.id!);
      await _loadData();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'تم ${component.isActive ? 'إلغاء تفعيل' : 'تفعيل'} المكون بنجاح',
            ),
            backgroundColor: RevolutionaryColors.successGlow,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تغيير حالة المكون: $e'),
            backgroundColor: RevolutionaryColors.errorCoral,
          ),
        );
      }
    }
  }

  void _deleteComponent(SalaryComponent component) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف مكون الراتب "${component.name}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              // Store context references before async operations
              final navigator = Navigator.of(context);
              final scaffoldMessenger = ScaffoldMessenger.of(context);

              navigator.pop();
              try {
                await _salaryComponentsService.deleteSalaryComponent(
                  component.id!,
                );
                await _loadData();

                if (mounted) {
                  scaffoldMessenger.showSnackBar(
                    const SnackBar(
                      content: Text('تم حذف المكون بنجاح'),
                      backgroundColor: RevolutionaryColors.successGlow,
                    ),
                  );
                }
              } catch (e) {
                if (mounted) {
                  scaffoldMessenger.showSnackBar(
                    SnackBar(
                      content: Text('خطأ في حذف المكون: $e'),
                      backgroundColor: RevolutionaryColors.errorCoral,
                    ),
                  );
                }
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: RevolutionaryColors.errorCoral,
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  void _createDefaultComponents() async {
    try {
      await _salaryComponentsService.createDefaultSalaryComponents();
      await _loadData();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إنشاء المكونات الافتراضية بنجاح'),
            backgroundColor: RevolutionaryColors.successGlow,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إنشاء المكونات الافتراضية: $e'),
            backgroundColor: RevolutionaryColors.errorCoral,
          ),
        );
      }
    }
  }

  void _exportComponents() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تصدير المكونات - قيد التطوير'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  void _importComponents() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('استيراد المكونات - قيد التطوير'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  // دوال مساعدة

  Color _getTypeColor(String type) {
    switch (type) {
      case 'allowance':
        return RevolutionaryColors.successGlow;
      case 'deduction':
        return RevolutionaryColors.errorCoral;
      case 'bonus':
        return RevolutionaryColors.warningAmber;
      default:
        return Colors.grey;
    }
  }

  IconData _getTypeIcon(String type) {
    switch (type) {
      case 'allowance':
        return Icons.add_circle;
      case 'deduction':
        return Icons.remove_circle;
      case 'bonus':
        return Icons.star;
      default:
        return Icons.help;
    }
  }
}
