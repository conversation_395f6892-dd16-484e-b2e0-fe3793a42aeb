/// خدمة نظام تصدير البيانات
/// توفر جميع العمليات المطلوبة لتصدير البيانات بصيغ متعددة
library;

import 'dart:convert';
import 'dart:io';
import 'package:path/path.dart' as path;
import '../database/database_helper.dart';
import '../services/logging_service.dart';
import '../services/audit_service.dart';
import '../services/notification_service.dart';
import '../exceptions/validation_exception.dart';
import '../models/export_models.dart';

class DataExportService {
  static final DataExportService _instance = DataExportService._internal();
  factory DataExportService() => _instance;
  DataExportService._internal();

  final DatabaseHelper _databaseHelper = DatabaseHelper();
  final NotificationService _notificationService = NotificationService();

  /// إنشاء طلب تصدير جديد
  Future<ExportRequest> createExportRequest({
    required String title,
    required String description,
    required String dataSource,
    required String exportFormat,
    required Map<String, dynamic> filters,
    required List<String> selectedFields,
    required Map<String, dynamic> formatOptions,
    required int requestedBy,
    required String requesterName,
    Map<String, dynamic> metadata = const {},
  }) async {
    try {
      // التحقق من صحة البيانات
      await _validateExportRequest(
        title: title,
        dataSource: dataSource,
        exportFormat: exportFormat,
        selectedFields: selectedFields,
      );

      final db = await _databaseHelper.database;
      final now = DateTime.now();

      final request = ExportRequest(
        title: title,
        description: description,
        dataSource: dataSource,
        exportFormat: exportFormat,
        filters: filters,
        selectedFields: selectedFields,
        formatOptions: formatOptions,
        requestedBy: requestedBy,
        requesterName: requesterName,
        status: ExportStatus.pending.value,
        metadata: metadata,
        createdAt: now,
        updatedAt: now,
      );

      final id = await db.insert('export_requests', request.toMap());

      final newRequest = request.copyWith(id: id);

      // تسجيل في سجل المراجعة
      await AuditService.log(
        action: 'CREATE',
        entityType: 'ExportRequest',
        entityId: id,
        description: 'إنشاء طلب تصدير جديد: $title',
        newValues: newRequest.toMap(),
      );

      LoggingService.info(
        'تم إنشاء طلب تصدير جديد بنجاح',
        category: 'DataExportService',
        data: {'id': id, 'title': title, 'format': exportFormat},
      );

      // بدء معالجة التصدير في الخلفية
      _processExportRequest(newRequest);

      return newRequest;
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء طلب التصدير',
        category: 'DataExportService',
        data: {'title': title, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// معالجة طلب التصدير
  Future<void> _processExportRequest(ExportRequest request) async {
    try {
      // تحديث حالة الطلب إلى "قيد المعالجة"
      await _updateExportRequestStatus(
        request.id!,
        ExportStatus.processing.value,
      );

      // جلب البيانات
      final data = await _fetchDataForExport(request);

      // إنشاء الملف
      final filePath = await _generateExportFile(request, data);

      // حساب حجم الملف
      final file = File(filePath);
      final fileSize = await file.length();

      // تحديث الطلب بالملف المكتمل
      await _updateExportRequestCompletion(request.id!, filePath, fileSize);

      // إرسال إشعار بالاكتمال
      await _sendExportCompletionNotification(request, filePath);

      LoggingService.info(
        'تم إكمال تصدير البيانات بنجاح',
        category: 'DataExportService',
        data: {
          'requestId': request.id,
          'filePath': filePath,
          'fileSize': fileSize,
        },
      );
    } catch (e) {
      // تحديث حالة الطلب إلى "فشل"
      await _updateExportRequestError(request.id!, e.toString());

      // إرسال إشعار بالفشل
      await _sendExportFailureNotification(request, e.toString());

      LoggingService.error(
        'خطأ في معالجة طلب التصدير',
        category: 'DataExportService',
        data: {'requestId': request.id, 'error': e.toString()},
      );
    }
  }

  /// تصدير البيانات مباشرة (بدون حفظ الطلب)
  Future<String> exportDataDirect({
    required String dataSource,
    required String exportFormat,
    required Map<String, dynamic> filters,
    required List<String> selectedFields,
    required ExportSettings settings,
    String? customFileName,
  }) async {
    try {
      // التحقق من صحة البيانات
      await _validateDirectExport(
        dataSource: dataSource,
        exportFormat: exportFormat,
        selectedFields: selectedFields,
      );

      // جلب البيانات
      final data = await _fetchDataDirect(dataSource, filters, selectedFields);

      // إنشاء الملف
      final filePath = await _generateDirectExportFile(
        dataSource: dataSource,
        exportFormat: exportFormat,
        data: data,
        settings: settings,
        customFileName: customFileName,
      );

      LoggingService.info(
        'تم تصدير البيانات مباشرة بنجاح',
        category: 'DataExportService',
        data: {
          'dataSource': dataSource,
          'format': exportFormat,
          'filePath': filePath,
        },
      );

      return filePath;
    } catch (e) {
      LoggingService.error(
        'خطأ في التصدير المباشر',
        category: 'DataExportService',
        data: {'dataSource': dataSource, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// إنشاء قالب تصدير
  Future<ExportTemplate> createExportTemplate({
    required String name,
    required String description,
    required String dataSource,
    required String exportFormat,
    required List<String> defaultFields,
    required Map<String, dynamic> defaultFilters,
    required Map<String, dynamic> formatSettings,
    required bool isPublic,
    required int createdBy,
    required String creatorName,
  }) async {
    try {
      // التحقق من صحة البيانات
      await _validateTemplateData(
        name: name,
        dataSource: dataSource,
        exportFormat: exportFormat,
        defaultFields: defaultFields,
      );

      final db = await _databaseHelper.database;
      final now = DateTime.now();

      final template = ExportTemplate(
        name: name,
        description: description,
        dataSource: dataSource,
        exportFormat: exportFormat,
        defaultFields: defaultFields,
        defaultFilters: defaultFilters,
        formatSettings: formatSettings,
        isPublic: isPublic,
        createdBy: createdBy,
        creatorName: creatorName,
        isActive: true,
        usageCount: 0,
        createdAt: now,
        updatedAt: now,
      );

      final id = await db.insert('export_templates', template.toMap());

      final newTemplate = template.copyWith(id: id);

      // تسجيل في سجل المراجعة
      await AuditService.log(
        action: 'CREATE',
        entityType: 'ExportTemplate',
        entityId: id,
        description: 'إنشاء قالب تصدير جديد: $name',
        newValues: newTemplate.toMap(),
      );

      LoggingService.info(
        'تم إنشاء قالب تصدير جديد بنجاح',
        category: 'DataExportService',
        data: {'id': id, 'name': name, 'dataSource': dataSource},
      );

      return newTemplate;
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء قالب التصدير',
        category: 'DataExportService',
        data: {'name': name, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// الحصول على طلبات التصدير للمستخدم
  Future<List<ExportRequest>> getExportRequestsForUser(int userId) async {
    try {
      final db = await _databaseHelper.database;
      final result = await db.query(
        'export_requests',
        where: 'requested_by = ?',
        whereArgs: [userId],
        orderBy: 'created_at DESC',
      );

      return result.map((map) => ExportRequest.fromMap(map)).toList();
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب طلبات التصدير للمستخدم',
        category: 'DataExportService',
        data: {'userId': userId, 'error': e.toString()},
      );
      return [];
    }
  }

  /// الحصول على قوالب التصدير المتاحة
  Future<List<ExportTemplate>> getAvailableTemplates({
    String? dataSource,
    int? userId,
  }) async {
    try {
      final db = await _databaseHelper.database;

      String whereClause = 'is_active = 1 AND (is_public = 1';
      List<dynamic> whereArgs = [];

      if (userId != null) {
        whereClause += ' OR created_by = ?';
        whereArgs.add(userId);
      }
      whereClause += ')';

      if (dataSource != null) {
        whereClause += ' AND data_source = ?';
        whereArgs.add(dataSource);
      }

      final result = await db.query(
        'export_templates',
        where: whereClause,
        whereArgs: whereArgs,
        orderBy: 'usage_count DESC, name ASC',
      );

      return result.map((map) => ExportTemplate.fromMap(map)).toList();
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب قوالب التصدير',
        category: 'DataExportService',
        data: {'dataSource': dataSource, 'error': e.toString()},
      );
      return [];
    }
  }

  /// الحصول على الحقول المتاحة لمصدر البيانات
  Future<List<Map<String, dynamic>>> getAvailableFields(
    String dataSource,
  ) async {
    try {
      switch (dataSource) {
        case 'employees':
          return [
            {
              'field': 'employee_number',
              'label': 'رقم الموظف',
              'type': 'string',
            },
            {'field': 'full_name', 'label': 'الاسم الكامل', 'type': 'string'},
            {'field': 'national_id', 'label': 'الرقم الوطني', 'type': 'string'},
            {'field': 'email', 'label': 'البريد الإلكتروني', 'type': 'string'},
            {'field': 'phone', 'label': 'رقم الهاتف', 'type': 'string'},
            {'field': 'department', 'label': 'القسم', 'type': 'string'},
            {'field': 'position', 'label': 'المنصب', 'type': 'string'},
            {'field': 'hire_date', 'label': 'تاريخ التوظيف', 'type': 'date'},
            {
              'field': 'basic_salary',
              'label': 'الراتب الأساسي',
              'type': 'number',
            },
            {'field': 'status', 'label': 'الحالة', 'type': 'string'},
          ];
        case 'payroll':
          return [
            {'field': 'employee_name', 'label': 'اسم الموظف', 'type': 'string'},
            {'field': 'month', 'label': 'الشهر', 'type': 'number'},
            {'field': 'year', 'label': 'السنة', 'type': 'number'},
            {
              'field': 'basic_salary',
              'label': 'الراتب الأساسي',
              'type': 'number',
            },
            {'field': 'allowances', 'label': 'البدلات', 'type': 'number'},
            {'field': 'deductions', 'label': 'الاستقطاعات', 'type': 'number'},
            {
              'field': 'gross_salary',
              'label': 'الراتب الإجمالي',
              'type': 'number',
            },
            {'field': 'net_salary', 'label': 'صافي الراتب', 'type': 'number'},
            {'field': 'working_days', 'label': 'أيام العمل', 'type': 'number'},
            {
              'field': 'actual_working_days',
              'label': 'أيام العمل الفعلية',
              'type': 'number',
            },
          ];
        case 'attendance':
          return [
            {'field': 'employee_name', 'label': 'اسم الموظف', 'type': 'string'},
            {'field': 'date', 'label': 'التاريخ', 'type': 'date'},
            {'field': 'check_in', 'label': 'وقت الدخول', 'type': 'time'},
            {'field': 'check_out', 'label': 'وقت الخروج', 'type': 'time'},
            {
              'field': 'working_hours',
              'label': 'ساعات العمل',
              'type': 'number',
            },
            {
              'field': 'overtime_hours',
              'label': 'ساعات إضافية',
              'type': 'number',
            },
            {'field': 'status', 'label': 'الحالة', 'type': 'string'},
          ];
        default:
          return [];
      }
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب الحقول المتاحة',
        category: 'DataExportService',
        data: {'dataSource': dataSource, 'error': e.toString()},
      );
      return [];
    }
  }

  /// حذف ملفات التصدير القديمة
  Future<void> cleanupOldExportFiles({int daysOld = 30}) async {
    try {
      final db = await _databaseHelper.database;
      final cutoffDate = DateTime.now().subtract(Duration(days: daysOld));

      final oldRequests = await db.query(
        'export_requests',
        where: 'status = ? AND completed_at < ?',
        whereArgs: [ExportStatus.completed.value, cutoffDate.toIso8601String()],
      );

      int deletedFiles = 0;
      for (final request in oldRequests) {
        final filePath = request['file_path'] as String?;
        if (filePath != null && File(filePath).existsSync()) {
          await File(filePath).delete();
          deletedFiles++;
        }
      }

      // حذف السجلات من قاعدة البيانات
      await db.delete(
        'export_requests',
        where: 'status = ? AND completed_at < ?',
        whereArgs: [ExportStatus.completed.value, cutoffDate.toIso8601String()],
      );

      LoggingService.info(
        'تم تنظيف ملفات التصدير القديمة',
        category: 'DataExportService',
        data: {'deletedFiles': deletedFiles, 'daysOld': daysOld},
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في تنظيف ملفات التصدير القديمة',
        category: 'DataExportService',
        data: {'error': e.toString()},
      );
    }
  }

  // ===== الطرق المساعدة =====

  /// التحقق من صحة طلب التصدير
  Future<void> _validateExportRequest({
    required String title,
    required String dataSource,
    required String exportFormat,
    required List<String> selectedFields,
  }) async {
    if (title.trim().isEmpty) {
      throw ValidationException('عنوان التصدير مطلوب');
    }

    if (title.length < 3) {
      throw ValidationException('عنوان التصدير يجب أن يكون 3 أحرف على الأقل');
    }

    final validDataSources = DataSource.values.map((e) => e.value).toList();
    if (!validDataSources.contains(dataSource)) {
      throw ValidationException('مصدر البيانات غير صحيح');
    }

    final validFormats = ExportFormat.values.map((e) => e.value).toList();
    if (!validFormats.contains(exportFormat)) {
      throw ValidationException('صيغة التصدير غير صحيحة');
    }

    if (selectedFields.isEmpty) {
      throw ValidationException('يجب اختيار حقل واحد على الأقل للتصدير');
    }
  }

  /// التحقق من صحة التصدير المباشر
  Future<void> _validateDirectExport({
    required String dataSource,
    required String exportFormat,
    required List<String> selectedFields,
  }) async {
    final validDataSources = DataSource.values.map((e) => e.value).toList();
    if (!validDataSources.contains(dataSource)) {
      throw ValidationException('مصدر البيانات غير صحيح');
    }

    final validFormats = ExportFormat.values.map((e) => e.value).toList();
    if (!validFormats.contains(exportFormat)) {
      throw ValidationException('صيغة التصدير غير صحيحة');
    }

    if (selectedFields.isEmpty) {
      throw ValidationException('يجب اختيار حقل واحد على الأقل للتصدير');
    }
  }

  /// التحقق من صحة بيانات القالب
  Future<void> _validateTemplateData({
    required String name,
    required String dataSource,
    required String exportFormat,
    required List<String> defaultFields,
  }) async {
    if (name.trim().isEmpty) {
      throw ValidationException('اسم القالب مطلوب');
    }

    if (name.length < 3) {
      throw ValidationException('اسم القالب يجب أن يكون 3 أحرف على الأقل');
    }

    final validDataSources = DataSource.values.map((e) => e.value).toList();
    if (!validDataSources.contains(dataSource)) {
      throw ValidationException('مصدر البيانات غير صحيح');
    }

    final validFormats = ExportFormat.values.map((e) => e.value).toList();
    if (!validFormats.contains(exportFormat)) {
      throw ValidationException('صيغة التصدير غير صحيحة');
    }

    if (defaultFields.isEmpty) {
      throw ValidationException('يجب تحديد حقل واحد على الأقل في القالب');
    }
  }

  /// جلب البيانات للتصدير
  Future<List<Map<String, dynamic>>> _fetchDataForExport(
    ExportRequest request,
  ) async {
    return await _fetchDataDirect(
      request.dataSource,
      request.filters,
      request.selectedFields,
    );
  }

  /// جلب البيانات مباشرة
  Future<List<Map<String, dynamic>>> _fetchDataDirect(
    String dataSource,
    Map<String, dynamic> filters,
    List<String> selectedFields,
  ) async {
    try {
      final db = await _databaseHelper.database;

      // بناء استعلام SQL
      final query = _buildSQLQuery(dataSource, filters, selectedFields);

      final result = await db.rawQuery(query['sql'], query['args']);

      return result;
    } catch (e) {
      throw ValidationException('خطأ في جلب البيانات: $e');
    }
  }

  /// بناء استعلام SQL
  Map<String, dynamic> _buildSQLQuery(
    String dataSource,
    Map<String, dynamic> filters,
    List<String> selectedFields,
  ) {
    String tableName;
    Map<String, String> fieldMapping = {};

    switch (dataSource) {
      case 'employees':
        tableName = 'employees';
        fieldMapping = {
          'employee_number': 'employee_number',
          'full_name': 'full_name',
          'national_id': 'national_id',
          'email': 'email',
          'phone': 'phone',
          'department': 'department',
          'position': 'position',
          'hire_date': 'hire_date',
          'basic_salary': 'basic_salary',
          'status': 'status',
        };
        break;
      case 'payroll':
        tableName = 'payroll_records';
        fieldMapping = {
          'employee_name': 'employee_name',
          'month': 'month',
          'year': 'year',
          'basic_salary': 'basic_salary',
          'allowances': 'allowances',
          'deductions': 'total_deductions',
          'gross_salary': 'gross_salary',
          'net_salary': 'net_salary',
          'working_days': 'working_days',
          'actual_working_days': 'actual_working_days',
        };
        break;
      default:
        throw ValidationException('مصدر البيانات غير مدعوم: $dataSource');
    }

    // بناء قائمة الحقول
    final mappedFields = selectedFields
        .where((field) => fieldMapping.containsKey(field))
        .map((field) => fieldMapping[field]!)
        .toList();

    if (mappedFields.isEmpty) {
      mappedFields.add('*');
    }

    String sql = 'SELECT ${mappedFields.join(', ')} FROM $tableName';
    List<dynamic> args = [];

    // إضافة الفلاتر
    if (filters.isNotEmpty) {
      final whereConditions = <String>[];

      filters.forEach((key, value) {
        if (fieldMapping.containsKey(key) && value != null) {
          final dbField = fieldMapping[key]!;
          if (value is String && value.isNotEmpty) {
            whereConditions.add('$dbField LIKE ?');
            args.add('%$value%');
          } else if (value is num) {
            whereConditions.add('$dbField = ?');
            args.add(value);
          } else if (value is DateTime) {
            whereConditions.add('DATE($dbField) = DATE(?)');
            args.add(value.toIso8601String());
          }
        }
      });

      if (whereConditions.isNotEmpty) {
        sql += ' WHERE ${whereConditions.join(' AND ')}';
      }
    }

    sql += ' ORDER BY created_at DESC';

    return {'sql': sql, 'args': args};
  }

  /// إنشاء ملف التصدير
  Future<String> _generateExportFile(
    ExportRequest request,
    List<Map<String, dynamic>> data,
  ) async {
    final settings = ExportSettings.fromMap(request.formatOptions);

    return await _generateDirectExportFile(
      dataSource: request.dataSource,
      exportFormat: request.exportFormat,
      data: data,
      settings: settings,
      customFileName: request.title,
    );
  }

  /// إنشاء ملف التصدير المباشر
  Future<String> _generateDirectExportFile({
    required String dataSource,
    required String exportFormat,
    required List<Map<String, dynamic>> data,
    required ExportSettings settings,
    String? customFileName,
  }) async {
    try {
      // إنشاء مجلد التصدير إذا لم يكن موجوداً
      final exportDir = Directory('exports');
      if (!await exportDir.exists()) {
        await exportDir.create(recursive: true);
      }

      // إنشاء اسم الملف
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final fileName = customFileName ?? '${dataSource}_export_$timestamp';
      final extension = _getFileExtension(exportFormat);
      final filePath = path.join(exportDir.path, '$fileName.$extension');

      // إنشاء الملف حسب الصيغة
      switch (exportFormat) {
        case 'csv':
          await _generateCSVFile(filePath, data, settings);
          break;
        case 'json':
          await _generateJSONFile(filePath, data, settings);
          break;
        case 'xml':
          await _generateXMLFile(filePath, data, settings);
          break;
        case 'excel':
          await _generateExcelFile(filePath, data, settings);
          break;
        case 'pdf':
          await _generatePDFFile(filePath, data, settings);
          break;
        default:
          throw ValidationException('صيغة التصدير غير مدعومة: $exportFormat');
      }

      return filePath;
    } catch (e) {
      throw ValidationException('خطأ في إنشاء ملف التصدير: $e');
    }
  }

  /// الحصول على امتداد الملف
  String _getFileExtension(String format) {
    switch (format) {
      case 'csv':
        return 'csv';
      case 'json':
        return 'json';
      case 'xml':
        return 'xml';
      case 'excel':
        return 'xlsx';
      case 'pdf':
        return 'pdf';
      default:
        return 'txt';
    }
  }

  /// إنشاء ملف CSV
  Future<void> _generateCSVFile(
    String filePath,
    List<Map<String, dynamic>> data,
    ExportSettings settings,
  ) async {
    final file = File(filePath);
    final sink = file.openWrite(
      encoding: Encoding.getByName(settings.encoding) ?? utf8,
    );

    try {
      if (data.isNotEmpty) {
        // كتابة العناوين
        if (settings.includeHeaders) {
          final headers = data.first.keys
              .map((key) => settings.columnHeaders[key] ?? key)
              .join(settings.delimiter);
          sink.writeln(headers);
        }

        // كتابة البيانات
        for (final row in data) {
          final values = row.values
              .map((value) => _formatValueForCSV(value, settings))
              .join(settings.delimiter);
          sink.writeln(values);
        }
      }
    } finally {
      await sink.close();
    }
  }

  /// إنشاء ملف JSON
  Future<void> _generateJSONFile(
    String filePath,
    List<Map<String, dynamic>> data,
    ExportSettings settings,
  ) async {
    final file = File(filePath);

    // تنسيق البيانات
    final formattedData = data.map((row) {
      final formattedRow = <String, dynamic>{};
      row.forEach((key, value) {
        final displayKey = settings.columnHeaders[key] ?? key;
        formattedRow[displayKey] = _formatValueForJSON(value, settings);
      });
      return formattedRow;
    }).toList();

    final jsonString = JsonEncoder.withIndent('  ').convert({
      'data': formattedData,
      'exportInfo': {
        'exportDate': DateTime.now().toIso8601String(),
        'totalRecords': data.length,
        'encoding': settings.encoding,
      },
    });

    await file.writeAsString(
      jsonString,
      encoding: Encoding.getByName(settings.encoding) ?? utf8,
    );
  }

  /// إنشاء ملف XML
  Future<void> _generateXMLFile(
    String filePath,
    List<Map<String, dynamic>> data,
    ExportSettings settings,
  ) async {
    final file = File(filePath);
    final sink = file.openWrite(
      encoding: Encoding.getByName(settings.encoding) ?? utf8,
    );

    try {
      // كتابة رأس XML
      sink.writeln('<?xml version="1.0" encoding="${settings.encoding}"?>');
      sink.writeln('<export>');
      sink.writeln('  <info>');
      sink.writeln(
        '    <exportDate>${DateTime.now().toIso8601String()}</exportDate>',
      );
      sink.writeln('    <totalRecords>${data.length}</totalRecords>');
      sink.writeln('  </info>');
      sink.writeln('  <data>');

      // كتابة البيانات
      for (final row in data) {
        sink.writeln('    <record>');
        row.forEach((key, value) {
          final displayKey = settings.columnHeaders[key] ?? key;
          final formattedValue = _formatValueForXML(value, settings);
          sink.writeln('      <$displayKey>$formattedValue</$displayKey>');
        });
        sink.writeln('    </record>');
      }

      sink.writeln('  </data>');
      sink.writeln('</export>');
    } finally {
      await sink.close();
    }
  }

  /// إنشاء ملف Excel (مبسط)
  Future<void> _generateExcelFile(
    String filePath,
    List<Map<String, dynamic>> data,
    ExportSettings settings,
  ) async {
    // هذا مثال مبسط - في التطبيق الحقيقي يجب استخدام مكتبة Excel
    await _generateCSVFile(
      filePath.replaceAll('.xlsx', '.csv'),
      data,
      settings,
    );
  }

  /// إنشاء ملف PDF (مبسط)
  Future<void> _generatePDFFile(
    String filePath,
    List<Map<String, dynamic>> data,
    ExportSettings settings,
  ) async {
    // هذا مثال مبسط - في التطبيق الحقيقي يجب استخدام مكتبة PDF
    final file = File(filePath.replaceAll('.pdf', '.txt'));
    final sink = file.openWrite(encoding: utf8);

    try {
      sink.writeln('تقرير البيانات');
      sink.writeln('تاريخ التصدير: ${DateTime.now()}');
      sink.writeln('عدد السجلات: ${data.length}');
      sink.writeln('=' * 50);

      for (final row in data) {
        row.forEach((key, value) {
          final displayKey = settings.columnHeaders[key] ?? key;
          sink.writeln('$displayKey: $value');
        });
        sink.writeln('-' * 30);
      }
    } finally {
      await sink.close();
    }
  }

  /// تنسيق القيمة للـ CSV
  String _formatValueForCSV(dynamic value, ExportSettings settings) {
    if (value == null) return '';

    if (value is DateTime) {
      return value.toString(); // يمكن تخصيص التنسيق
    } else if (value is num) {
      return value.toString();
    } else {
      final stringValue = value.toString();
      // إضافة علامات اقتباس إذا كانت القيمة تحتوي على الفاصل
      if (stringValue.contains(settings.delimiter) ||
          stringValue.contains('"')) {
        return '"${stringValue.replaceAll('"', '""')}"';
      }
      return stringValue;
    }
  }

  /// تنسيق القيمة للـ JSON
  dynamic _formatValueForJSON(dynamic value, ExportSettings settings) {
    if (value is DateTime) {
      return value.toIso8601String();
    }
    return value;
  }

  /// تنسيق القيمة للـ XML
  String _formatValueForXML(dynamic value, ExportSettings settings) {
    if (value == null) return '';

    final stringValue = value.toString();
    return stringValue
        .replaceAll('&', '&amp;')
        .replaceAll('<', '&lt;')
        .replaceAll('>', '&gt;')
        .replaceAll('"', '&quot;')
        .replaceAll("'", '&apos;');
  }

  /// تحديث حالة طلب التصدير
  Future<void> _updateExportRequestStatus(int requestId, String status) async {
    try {
      final db = await _databaseHelper.database;
      await db.update(
        'export_requests',
        {'status': status, 'updated_at': DateTime.now().toIso8601String()},
        where: 'id = ?',
        whereArgs: [requestId],
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في تحديث حالة طلب التصدير',
        category: 'DataExportService',
        data: {'requestId': requestId, 'status': status, 'error': e.toString()},
      );
    }
  }

  /// تحديث طلب التصدير عند الاكتمال
  Future<void> _updateExportRequestCompletion(
    int requestId,
    String filePath,
    int fileSize,
  ) async {
    try {
      final db = await _databaseHelper.database;
      await db.update(
        'export_requests',
        {
          'status': ExportStatus.completed.value,
          'file_path': filePath,
          'file_size': fileSize,
          'completed_at': DateTime.now().toIso8601String(),
          'updated_at': DateTime.now().toIso8601String(),
        },
        where: 'id = ?',
        whereArgs: [requestId],
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في تحديث طلب التصدير عند الاكتمال',
        category: 'DataExportService',
        data: {'requestId': requestId, 'error': e.toString()},
      );
    }
  }

  /// تحديث طلب التصدير عند الفشل
  Future<void> _updateExportRequestError(
    int requestId,
    String errorMessage,
  ) async {
    try {
      final db = await _databaseHelper.database;
      await db.update(
        'export_requests',
        {
          'status': ExportStatus.failed.value,
          'error_message': errorMessage,
          'updated_at': DateTime.now().toIso8601String(),
        },
        where: 'id = ?',
        whereArgs: [requestId],
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في تحديث طلب التصدير عند الفشل',
        category: 'DataExportService',
        data: {'requestId': requestId, 'error': e.toString()},
      );
    }
  }

  /// إرسال إشعار اكتمال التصدير
  Future<void> _sendExportCompletionNotification(
    ExportRequest request,
    String filePath,
  ) async {
    try {
      await _notificationService.sendNotification(
        userId: request.requestedBy,
        title: 'اكتمل تصدير البيانات',
        message: 'تم إكمال تصدير "${request.title}" بنجاح',
        type: 'export_completed',
        data: {
          'requestId': request.id,
          'filePath': filePath,
          'exportFormat': request.exportFormat,
        },
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في إرسال إشعار اكتمال التصدير',
        category: 'DataExportService',
        data: {'requestId': request.id, 'error': e.toString()},
      );
    }
  }

  /// إرسال إشعار فشل التصدير
  Future<void> _sendExportFailureNotification(
    ExportRequest request,
    String errorMessage,
  ) async {
    try {
      await _notificationService.sendNotification(
        userId: request.requestedBy,
        title: 'فشل في تصدير البيانات',
        message: 'فشل في تصدير "${request.title}": $errorMessage',
        type: 'export_failed',
        data: {'requestId': request.id, 'errorMessage': errorMessage},
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في إرسال إشعار فشل التصدير',
        category: 'DataExportService',
        data: {'requestId': request.id, 'error': e.toString()},
      );
    }
  }
}
