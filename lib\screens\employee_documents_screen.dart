/// شاشة إدارة وثائق الموظفين
/// توفر واجهة لإدارة وثائق الموظفين ورفع الملفات
library;

import 'package:flutter/material.dart';
import '../models/hr_models.dart';
import '../services/employee_document_service.dart';
import '../services/employee_service.dart';
import '../services/file_management_service.dart';
import '../services/logging_service.dart';
import '../services/auth_service.dart';
import '../widgets/loading_widget.dart';
import '../widgets/custom_error_widget.dart' as custom_widgets;
import '../constants/app_constants.dart';
import '../constants/revolutionary_design_colors.dart';

class EmployeeDocumentsScreen extends StatefulWidget {
  final int? employeeId;

  const EmployeeDocumentsScreen({super.key, this.employeeId});

  @override
  State<EmployeeDocumentsScreen> createState() =>
      _EmployeeDocumentsScreenState();
}

class _EmployeeDocumentsScreenState extends State<EmployeeDocumentsScreen>
    with TickerProviderStateMixin {
  final EmployeeDocumentService _documentService = EmployeeDocumentService();
  final EmployeeService _employeeService = EmployeeService();
  final FileManagementService _fileService = FileManagementService();

  late TabController _tabController;
  List<EmployeeDocument> _allDocuments = [];
  List<EmployeeDocument> _expiredDocuments = [];
  List<EmployeeDocument> _expiringSoonDocuments = [];
  List<Employee> _employees = [];
  Map<String, int> _statistics = {};
  Map<String, dynamic> _storageStats = {};
  bool _isLoading = true;
  String? _error;
  int? _selectedEmployeeId;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);
    _selectedEmployeeId = widget.employeeId;
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final employees = await _employeeService.getAllEmployees();
      final statistics = await _documentService.getDocumentStatistics();
      final expiredDocuments = await _documentService.getExpiredDocuments();
      final expiringSoonDocuments = await _documentService
          .getExpiringSoonDocuments();
      final storageStats = await _fileService.getStorageStatistics();

      List<EmployeeDocument> allDocuments = [];
      if (_selectedEmployeeId != null) {
        allDocuments = await _documentService.getEmployeeDocuments(
          _selectedEmployeeId!,
        );
      } else {
        // جلب وثائق جميع الموظفين
        for (final employee in employees) {
          final employeeDocuments = await _documentService.getEmployeeDocuments(
            employee.id!,
          );
          allDocuments.addAll(employeeDocuments);
        }
      }

      setState(() {
        _employees = employees;
        _allDocuments = allDocuments;
        _expiredDocuments = expiredDocuments;
        _expiringSoonDocuments = expiringSoonDocuments;
        _statistics = statistics;
        _storageStats = storageStats;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });

      LoggingService.error(
        'خطأ في تحميل وثائق الموظفين',
        category: 'EmployeeDocumentsScreen',
        data: {'error': e.toString()},
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          _selectedEmployeeId != null ? 'وثائق الموظف' : 'إدارة وثائق الموظفين',
        ),
        backgroundColor: Colors.purple[700],
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          isScrollable: true,
          tabs: [
            Tab(
              icon: const Icon(Icons.folder),
              text: 'جميع الوثائق',
              child: _allDocuments.isNotEmpty
                  ? Badge(
                      label: Text('${_allDocuments.length}'),
                      child: const Text('جميع الوثائق'),
                    )
                  : const Text('جميع الوثائق'),
            ),
            Tab(
              icon: const Icon(Icons.warning),
              text: 'منتهية الصلاحية',
              child: _expiredDocuments.isNotEmpty
                  ? Badge(
                      label: Text('${_expiredDocuments.length}'),
                      backgroundColor: Colors.red,
                      child: const Text('منتهية الصلاحية'),
                    )
                  : const Text('منتهية الصلاحية'),
            ),
            Tab(
              icon: const Icon(Icons.schedule),
              text: 'قريبة الانتهاء',
              child: _expiringSoonDocuments.isNotEmpty
                  ? Badge(
                      label: Text('${_expiringSoonDocuments.length}'),
                      backgroundColor: Colors.orange,
                      child: const Text('قريبة الانتهاء'),
                    )
                  : const Text('قريبة الانتهاء'),
            ),
            const Tab(icon: Icon(Icons.analytics), text: 'الإحصائيات'),
            const Tab(icon: Icon(Icons.storage), text: 'التخزين'),
          ],
        ),
        actions: [
          if (_selectedEmployeeId == null)
            PopupMenuButton<int?>(
              icon: const Icon(Icons.person),
              tooltip: 'تصفية حسب الموظف',
              onSelected: (employeeId) {
                setState(() {
                  _selectedEmployeeId = employeeId;
                });
                _loadData();
              },
              itemBuilder: (context) => [
                const PopupMenuItem<int?>(
                  value: null,
                  child: Text('جميع الموظفين'),
                ),
                ...(_employees.map(
                  (emp) => PopupMenuItem<int?>(
                    value: emp.id,
                    child: Text('${emp.firstName} ${emp.lastName}'),
                  ),
                )),
              ],
            ),
          IconButton(
            onPressed: _loadData,
            tooltip: 'تحديث',
            icon: const Icon(Icons.refresh),
          ),
        ],
      ),
      body: _buildBody(),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showAddDocumentDialog(),
        backgroundColor: Colors.purple[700],
        tooltip: 'إضافة وثيقة جديدة',
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const LoadingWidget();
    }

    if (_error != null) {
      return custom_widgets.ErrorWidget(message: _error!, onRetry: _loadData);
    }

    return TabBarView(
      controller: _tabController,
      children: [
        _buildAllDocumentsTab(),
        _buildExpiredDocumentsTab(),
        _buildExpiringSoonDocumentsTab(),
        _buildStatisticsTab(),
        _buildStorageTab(),
      ],
    );
  }

  Widget _buildAllDocumentsTab() {
    if (_allDocuments.isEmpty) {
      return _buildEmptyState('لا توجد وثائق');
    }

    return RefreshIndicator(
      onRefresh: _loadData,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _allDocuments.length,
        itemBuilder: (context, index) {
          final document = _allDocuments[index];
          return _buildDocumentCard(document);
        },
      ),
    );
  }

  Widget _buildExpiredDocumentsTab() {
    if (_expiredDocuments.isEmpty) {
      return _buildEmptyState('لا توجد وثائق منتهية الصلاحية');
    }

    return RefreshIndicator(
      onRefresh: _loadData,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _expiredDocuments.length,
        itemBuilder: (context, index) {
          final document = _expiredDocuments[index];
          return _buildDocumentCard(document, isExpired: true);
        },
      ),
    );
  }

  Widget _buildExpiringSoonDocumentsTab() {
    if (_expiringSoonDocuments.isEmpty) {
      return _buildEmptyState('لا توجد وثائق قريبة من انتهاء الصلاحية');
    }

    return RefreshIndicator(
      onRefresh: _loadData,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _expiringSoonDocuments.length,
        itemBuilder: (context, index) {
          final document = _expiringSoonDocuments[index];
          return _buildDocumentCard(document, isExpiringSoon: true);
        },
      ),
    );
  }

  Widget _buildStatisticsTab() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          _buildStatCard(
            'إجمالي الوثائق',
            _statistics['total'] ?? 0,
            Colors.blue,
          ),
          const SizedBox(height: 12),
          _buildStatCard(
            'الوثائق الصالحة',
            _statistics['valid'] ?? 0,
            Colors.green,
          ),
          const SizedBox(height: 12),
          _buildStatCard(
            'قريبة الانتهاء',
            _statistics['expiringSoon'] ?? 0,
            Colors.orange,
          ),
          const SizedBox(height: 12),
          _buildStatCard(
            'منتهية الصلاحية',
            _statistics['expired'] ?? 0,
            Colors.red,
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(String title, int count, Color color) {
    return Card(
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: color,
          child: Text(
            count.toString(),
            style: const TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        title: Text(title),
        subtitle: Text('$count وثيقة'),
      ),
    );
  }

  Widget _buildStorageTab() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'إحصائيات التخزين',
            style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  _buildStorageInfoRow(
                    'إجمالي الملفات',
                    '${_storageStats['totalFiles'] ?? 0}',
                    Icons.description,
                  ),
                  const Divider(),
                  _buildStorageInfoRow(
                    'حجم التخزين المستخدم',
                    '${_storageStats['totalSizeMB'] ?? '0.00'} ميجابايت',
                    Icons.storage,
                  ),
                  const Divider(),
                  _buildStorageInfoRow(
                    'متوسط حجم الملف',
                    _calculateAverageFileSize(),
                    Icons.analytics,
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),
          const Text(
            'أنواع الملفات',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          Expanded(child: _buildFileTypesChart()),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _cleanupOldFiles,
                  icon: const Icon(Icons.cleaning_services),
                  label: const Text('تنظيف الملفات القديمة'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: RevolutionaryColors.warningAmber,
                    foregroundColor: Colors.white,
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _refreshStorageStats,
                  icon: const Icon(Icons.refresh),
                  label: const Text('تحديث الإحصائيات'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: RevolutionaryColors.damascusSky,
                    foregroundColor: Colors.white,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStorageInfoRow(String label, String value, IconData icon) {
    return Row(
      children: [
        Icon(icon, color: RevolutionaryColors.damascusSky),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            label,
            style: const TextStyle(fontWeight: FontWeight.w600),
          ),
        ),
        Text(
          value,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: RevolutionaryColors.damascusSky,
          ),
        ),
      ],
    );
  }

  Widget _buildFileTypesChart() {
    final fileTypes = _storageStats['fileTypes'] as Map<String, dynamic>? ?? {};

    if (fileTypes.isEmpty) {
      return const Center(
        child: Text('لا توجد ملفات', style: TextStyle(color: Colors.grey)),
      );
    }

    return ListView.builder(
      itemCount: fileTypes.length,
      itemBuilder: (context, index) {
        final entry = fileTypes.entries.elementAt(index);
        final extension = entry.key;
        final count = entry.value;
        final percentage = (count / (_storageStats['totalFiles'] ?? 1) * 100);

        return Card(
          margin: const EdgeInsets.symmetric(vertical: 4),
          child: ListTile(
            leading: Icon(
              _getFileTypeIcon(extension),
              color: RevolutionaryColors.damascusSky,
            ),
            title: Text(extension.toUpperCase()),
            subtitle: Text('$count ملف'),
            trailing: Text(
              '${percentage.toStringAsFixed(1)}%',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                color: RevolutionaryColors.damascusSky,
              ),
            ),
          ),
        );
      },
    );
  }

  String _calculateAverageFileSize() {
    final totalFiles = _storageStats['totalFiles'] ?? 0;
    final totalSize = _storageStats['totalSize'] ?? 0;

    if (totalFiles == 0) return '0 بايت';

    final averageSize = totalSize / totalFiles;
    return _formatFileSize(averageSize.round());
  }

  IconData _getFileTypeIcon(String extension) {
    switch (extension.toLowerCase()) {
      case '.pdf':
        return Icons.picture_as_pdf;
      case '.doc':
      case '.docx':
        return Icons.description;
      case '.jpg':
      case '.jpeg':
      case '.png':
        return Icons.image;
      case '.txt':
        return Icons.text_snippet;
      default:
        return Icons.insert_drive_file;
    }
  }

  void _cleanupOldFiles() async {
    final scaffoldMessenger = ScaffoldMessenger.of(context);
    try {
      await _fileService.cleanupOldFiles();
      scaffoldMessenger.showSnackBar(
        const SnackBar(
          content: Text('تم تنظيف الملفات القديمة بنجاح'),
          backgroundColor: Colors.green,
        ),
      );
      _refreshStorageStats();
    } catch (e) {
      scaffoldMessenger.showSnackBar(
        SnackBar(
          content: Text('خطأ في تنظيف الملفات: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _refreshStorageStats() async {
    try {
      final storageStats = await _fileService.getStorageStatistics();
      setState(() {
        _storageStats = storageStats;
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحديث الإحصائيات: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes بايت';
    if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)} كيلوبايت';
    }
    return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} ميجابايت';
  }

  Widget _buildEmptyState(String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.description, size: 64, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text(
            message,
            style: TextStyle(fontSize: 18, color: Colors.grey[600]),
          ),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: () => _showAddDocumentDialog(),
            icon: const Icon(Icons.add),
            label: const Text('إضافة وثيقة جديدة'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.purple[700],
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDocumentCard(
    EmployeeDocument document, {
    bool isExpired = false,
    bool isExpiringSoon = false,
  }) {
    final employee = _employees.firstWhere(
      (emp) => emp.id == document.employeeId,
      orElse: () => Employee(
        id: 0,
        employeeNumber: '000',
        nationalId: '000000000',
        firstName: 'موظف',
        lastName: 'غير معروف',
        fullName: 'موظف غير معروف',
        email: '',
        phone: '',
        hireDate: DateTime.now(),
        basicSalary: 0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
    );

    Color? cardColor;
    if (isExpired) {
      cardColor = Colors.red[50];
    } else if (isExpiringSoon) {
      cardColor = Colors.orange[50];
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      color: cardColor,
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: isExpired
              ? Colors.red
              : isExpiringSoon
              ? Colors.orange
              : Colors.purple[700],
          child: Icon(
            _getDocumentIcon(document.documentType),
            color: Colors.white,
          ),
        ),
        title: Text(
          document.documentName,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('الموظف: ${employee.firstName} ${employee.lastName}'),
            Text('النوع: ${_getDocumentTypeText(document.documentType)}'),
            if (document.expiryDate != null)
              Text(
                'تاريخ الانتهاء: ${_formatDate(document.expiryDate!)}',
                style: TextStyle(
                  color: isExpired
                      ? Colors.red
                      : isExpiringSoon
                      ? Colors.orange
                      : null,
                  fontWeight: (isExpired || isExpiringSoon)
                      ? FontWeight.bold
                      : null,
                ),
              ),
            if (document.description != null &&
                document.description!.isNotEmpty)
              Text(
                document.description!,
                style: TextStyle(color: Colors.grey[600]),
              ),
          ],
        ),
        trailing: PopupMenuButton<String>(
          onSelected: (value) => _handleMenuAction(value, document),
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'view',
              child: ListTile(
                leading: Icon(Icons.visibility),
                title: Text('عرض'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'edit',
              child: ListTile(
                leading: Icon(Icons.edit),
                title: Text('تعديل'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'delete',
              child: ListTile(
                leading: Icon(Icons.delete, color: Colors.red),
                title: Text('حذف', style: TextStyle(color: Colors.red)),
                contentPadding: EdgeInsets.zero,
              ),
            ),
          ],
        ),
      ),
    );
  }

  IconData _getDocumentIcon(String documentType) {
    switch (documentType) {
      case AppConstants.documentTypeId:
        return Icons.badge;
      case AppConstants.documentTypePassport:
        return Icons.flight;
      case AppConstants.documentTypeCertificate:
        return Icons.school;
      case AppConstants.documentTypeContract:
        return Icons.description;
      default:
        return Icons.insert_drive_file;
    }
  }

  String _getDocumentTypeText(String documentType) {
    switch (documentType) {
      case AppConstants.documentTypeId:
        return 'هوية شخصية';
      case AppConstants.documentTypePassport:
        return 'جواز سفر';
      case AppConstants.documentTypeCertificate:
        return 'شهادة';
      case AppConstants.documentTypeContract:
        return 'عقد';
      default:
        return documentType;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  void _handleMenuAction(String action, EmployeeDocument document) {
    switch (action) {
      case 'view':
        _viewDocument(document);
        break;
      case 'edit':
        _showEditDocumentDialog(document);
        break;
      case 'delete':
        _deleteDocument(document);
        break;
    }
  }

  void _viewDocument(EmployeeDocument document) async {
    try {
      // التحقق من وجود الملف
      final fileExists = await _fileService.fileExists(document.filePath);
      if (!fileExists) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('الملف غير موجود أو تم حذفه'),
              backgroundColor: Colors.red,
            ),
          );
        }
        return;
      }

      // الحصول على معلومات الملف
      final fileInfo = await _fileService.getFileInfo(document.filePath);

      // عرض معلومات الملف في حوار
      if (mounted) {
        showDialog(
          context: context,
          builder: (context) => _DocumentViewDialog(
            document: document,
            fileInfo: fileInfo,
            onOpenFile: () => _openFile(document.filePath),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في عرض الوثيقة: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _openFile(String filePath) async {
    try {
      // في التطبيق الحقيقي، يمكن استخدام مكتبة مثل open_file لفتح الملف
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('فتح الملف: $filePath')));
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في فتح الملف: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _showAddDocumentDialog() {
    if (_employees.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('لا يوجد موظفين لإضافة وثائق لهم'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    showDialog(
      context: context,
      builder: (context) => _DocumentFormDialog(
        employees: _employees,
        selectedEmployeeId: _selectedEmployeeId,
        onSave:
            (
              employeeId,
              documentType,
              documentName,
              description,
              expiryDate,
              isRequired,
            ) async {
              final scaffoldMessenger = ScaffoldMessenger.of(context);
              try {
                // رفع الملف أولاً
                final filePath = await _fileService.uploadFile(
                  employeeId: employeeId,
                  documentType: documentType,
                  customFileName: documentName,
                );

                if (filePath == null) {
                  // المستخدم ألغى رفع الملف
                  return;
                }

                await _documentService.addEmployeeDocument(
                  employeeId: employeeId,
                  documentType: documentType,
                  documentName: documentName,
                  filePath: filePath,
                  description: description.isEmpty ? null : description,
                  expiryDate: expiryDate,
                  isRequired: isRequired,
                  uploadedBy: AuthService.currentUser?.id ?? 1,
                );

                if (mounted) {
                  scaffoldMessenger.showSnackBar(
                    const SnackBar(
                      content: Text('تم إضافة الوثيقة بنجاح'),
                      backgroundColor: Colors.green,
                    ),
                  );
                  _loadData();
                }
              } catch (e) {
                if (mounted) {
                  scaffoldMessenger.showSnackBar(
                    SnackBar(
                      content: Text('خطأ في إضافة الوثيقة: ${e.toString()}'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            },
      ),
    );
  }

  void _showEditDocumentDialog(EmployeeDocument document) {
    showDialog(
      context: context,
      builder: (context) => _DocumentFormDialog(
        document: document,
        employees: _employees,
        onSave:
            (
              employeeId,
              documentType,
              documentName,
              description,
              expiryDate,
              isRequired,
            ) async {
              final scaffoldMessenger = ScaffoldMessenger.of(context);
              try {
                final updatedDocument = document.copyWith(
                  employeeId: employeeId,
                  documentType: documentType,
                  documentName: documentName,
                  description: description.isEmpty ? null : description,
                  expiryDate: expiryDate,
                  isRequired: isRequired,
                );

                await _documentService.updateDocument(updatedDocument);

                if (mounted) {
                  scaffoldMessenger.showSnackBar(
                    const SnackBar(
                      content: Text('تم تحديث الوثيقة بنجاح'),
                      backgroundColor: Colors.green,
                    ),
                  );
                  _loadData();
                }
              } catch (e) {
                if (mounted) {
                  scaffoldMessenger.showSnackBar(
                    SnackBar(
                      content: Text('خطأ في تحديث الوثيقة: ${e.toString()}'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            },
      ),
    );
  }

  void _deleteDocument(EmployeeDocument document) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text(
          'هل أنت متأكد من حذف الوثيقة "${document.documentName}"؟',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              final navigator = Navigator.of(context);
              final scaffoldMessenger = ScaffoldMessenger.of(context);
              navigator.pop();

              try {
                await _documentService.deleteDocument(document.id!);

                if (mounted) {
                  scaffoldMessenger.showSnackBar(
                    const SnackBar(
                      content: Text('تم حذف الوثيقة بنجاح'),
                      backgroundColor: Colors.green,
                    ),
                  );
                  _loadData();
                }
              } catch (e) {
                if (mounted) {
                  scaffoldMessenger.showSnackBar(
                    SnackBar(
                      content: Text('خطأ في حذف الوثيقة: ${e.toString()}'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('حذف', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }
}

class _DocumentFormDialog extends StatefulWidget {
  final EmployeeDocument? document;
  final List<Employee> employees;
  final int? selectedEmployeeId;
  final Function(
    int employeeId,
    String documentType,
    String documentName,
    String description,
    DateTime? expiryDate,
    bool isRequired,
  )
  onSave;

  const _DocumentFormDialog({
    this.document,
    required this.employees,
    this.selectedEmployeeId,
    required this.onSave,
  });

  @override
  State<_DocumentFormDialog> createState() => _DocumentFormDialogState();
}

class _DocumentFormDialogState extends State<_DocumentFormDialog> {
  final _formKey = GlobalKey<FormState>();
  late final TextEditingController _documentNameController;
  late final TextEditingController _descriptionController;

  int? _selectedEmployeeId;
  String _selectedDocumentType = AppConstants.documentTypeId;
  DateTime? _expiryDate;
  bool _isRequired = false;

  @override
  void initState() {
    super.initState();
    _documentNameController = TextEditingController(
      text: widget.document?.documentName ?? '',
    );
    _descriptionController = TextEditingController(
      text: widget.document?.description ?? '',
    );
    _selectedEmployeeId =
        widget.document?.employeeId ?? widget.selectedEmployeeId;
    _selectedDocumentType =
        widget.document?.documentType ?? AppConstants.documentTypeId;
    _expiryDate = widget.document?.expiryDate;
    _isRequired = widget.document?.isRequired ?? false;
  }

  @override
  void dispose() {
    _documentNameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(
        widget.document == null ? 'إضافة وثيقة جديدة' : 'تعديل الوثيقة',
      ),
      content: SingleChildScrollView(
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              DropdownButtonFormField<int>(
                value: _selectedEmployeeId,
                decoration: const InputDecoration(
                  labelText: 'الموظف *',
                  border: OutlineInputBorder(),
                ),
                items: widget.employees.map((emp) {
                  return DropdownMenuItem<int>(
                    value: emp.id,
                    child: Text('${emp.firstName} ${emp.lastName}'),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedEmployeeId = value;
                  });
                },
                validator: (value) {
                  if (value == null) {
                    return 'الموظف مطلوب';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<String>(
                value: _selectedDocumentType,
                decoration: const InputDecoration(
                  labelText: 'نوع الوثيقة *',
                  border: OutlineInputBorder(),
                ),
                items: const [
                  DropdownMenuItem(
                    value: AppConstants.documentTypeId,
                    child: Text('هوية شخصية'),
                  ),
                  DropdownMenuItem(
                    value: AppConstants.documentTypePassport,
                    child: Text('جواز سفر'),
                  ),
                  DropdownMenuItem(
                    value: AppConstants.documentTypeCertificate,
                    child: Text('شهادة'),
                  ),
                  DropdownMenuItem(
                    value: AppConstants.documentTypeContract,
                    child: Text('عقد'),
                  ),
                ],
                onChanged: (value) {
                  setState(() {
                    _selectedDocumentType = value!;
                  });
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _documentNameController,
                decoration: const InputDecoration(
                  labelText: 'اسم الوثيقة *',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'اسم الوثيقة مطلوب';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                decoration: const InputDecoration(
                  labelText: 'تاريخ انتهاء الصلاحية',
                  border: OutlineInputBorder(),
                  suffixIcon: Icon(Icons.calendar_today),
                ),
                readOnly: true,
                onTap: () async {
                  final date = await showDatePicker(
                    context: context,
                    initialDate:
                        _expiryDate ??
                        DateTime.now().add(const Duration(days: 365)),
                    firstDate: DateTime.now(),
                    lastDate: DateTime.now().add(const Duration(days: 3650)),
                  );
                  if (date != null) {
                    setState(() {
                      _expiryDate = date;
                    });
                  }
                },
                controller: TextEditingController(
                  text: _expiryDate != null
                      ? '${_expiryDate!.day}/${_expiryDate!.month}/${_expiryDate!.year}'
                      : '',
                ),
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _descriptionController,
                decoration: const InputDecoration(
                  labelText: 'الوصف',
                  border: OutlineInputBorder(),
                ),
                maxLines: 2,
              ),
              const SizedBox(height: 16),
              CheckboxListTile(
                title: const Text('وثيقة مطلوبة'),
                value: _isRequired,
                onChanged: (value) {
                  setState(() {
                    _isRequired = value ?? false;
                  });
                },
                controlAffinity: ListTileControlAffinity.leading,
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: () {
            if (_formKey.currentState!.validate()) {
              widget.onSave(
                _selectedEmployeeId!,
                _selectedDocumentType,
                _documentNameController.text.trim(),
                _descriptionController.text.trim(),
                _expiryDate,
                _isRequired,
              );
              Navigator.of(context).pop();
            }
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.purple[700],
            foregroundColor: Colors.white,
          ),
          child: Text(widget.document == null ? 'إضافة' : 'تحديث'),
        ),
      ],
    );
  }
}

class _DocumentViewDialog extends StatelessWidget {
  final EmployeeDocument document;
  final Map<String, dynamic> fileInfo;
  final VoidCallback onOpenFile;

  const _DocumentViewDialog({
    required this.document,
    required this.fileInfo,
    required this.onOpenFile,
  });

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(document.documentName),
      content: SizedBox(
        width: MediaQuery.of(context).size.width * 0.8,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildInfoRow(
              'نوع الوثيقة',
              _getDocumentTypeText(document.documentType),
            ),
            _buildInfoRow('الوصف', document.description ?? 'لا يوجد'),
            _buildInfoRow(
              'تاريخ الانتهاء',
              document.expiryDate != null
                  ? '${document.expiryDate!.day}/${document.expiryDate!.month}/${document.expiryDate!.year}'
                  : 'لا ينتهي',
            ),
            _buildInfoRow('مطلوبة', document.isRequired ? 'نعم' : 'لا'),
            _buildInfoRow(
              'تاريخ الرفع',
              '${document.uploadedAt.day}/${document.uploadedAt.month}/${document.uploadedAt.year}',
            ),
            if (fileInfo['exists'] == true) ...[
              const Divider(),
              const Text(
                'معلومات الملف:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              _buildInfoRow('اسم الملف', fileInfo['name'] ?? 'غير معروف'),
              _buildInfoRow('نوع الملف', fileInfo['type'] ?? 'غير معروف'),
              _buildInfoRow(
                'حجم الملف',
                _formatFileSize(fileInfo['size'] ?? 0),
              ),
              _buildInfoRow(
                'تاريخ التعديل',
                fileInfo['modified'] != null
                    ? '${fileInfo['modified'].day}/${fileInfo['modified'].month}/${fileInfo['modified'].year}'
                    : 'غير معروف',
              ),
            ] else ...[
              const Divider(),
              const Text(
                'تحذير: الملف غير موجود!',
                style: TextStyle(
                  color: Colors.red,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('إغلاق'),
        ),
        if (fileInfo['exists'] == true)
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              onOpenFile();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: RevolutionaryColors.damascusSky,
              foregroundColor: Colors.white,
            ),
            child: const Text('فتح الملف'),
          ),
      ],
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.w600),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  String _getDocumentTypeText(String documentType) {
    switch (documentType) {
      case 'id_card':
        return 'بطاقة هوية';
      case 'passport':
        return 'جواز سفر';
      case 'certificate':
        return 'شهادة';
      case 'contract':
        return 'عقد';
      case 'medical':
        return 'تقرير طبي';
      case 'other':
        return 'أخرى';
      default:
        return documentType;
    }
  }

  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes بايت';
    if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)} كيلوبايت';
    }
    return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} ميجابايت';
  }
}
