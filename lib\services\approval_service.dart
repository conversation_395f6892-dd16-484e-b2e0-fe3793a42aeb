/// خدمة نظام الموافقات الإلكترونية المحسنة
/// توفر جميع العمليات المطلوبة لإدارة الموافقات وسير العمل
library;

import 'dart:convert';
import '../database/database_helper.dart';
import '../models/hr_models.dart';
import '../models/approval_models.dart';
import '../constants/app_constants.dart';
import '../services/logging_service.dart';
import '../services/audit_service.dart';
import '../services/notification_service.dart';
import '../exceptions/validation_exception.dart';

class ApprovalService {
  static final ApprovalService _instance = ApprovalService._internal();
  factory ApprovalService() => _instance;
  ApprovalService._internal();

  final DatabaseHelper _databaseHelper = DatabaseHelper();
  final NotificationService _notificationService = NotificationService();

  /// الحصول على جميع الموافقات
  Future<List<Approval>> getAllApprovals({
    int? employeeId,
    int? approverId,
    String? status,
    String? requestType,
    DateTime? fromDate,
    DateTime? toDate,
    String? priority,
  }) async {
    try {
      final db = await _databaseHelper.database;

      String whereClause = '1=1';
      List<dynamic> whereArgs = [];

      if (employeeId != null) {
        whereClause += ' AND employee_id = ?';
        whereArgs.add(employeeId);
      }

      if (approverId != null) {
        whereClause += ' AND approver_id = ?';
        whereArgs.add(approverId);
      }

      if (status != null && status.isNotEmpty) {
        whereClause += ' AND status = ?';
        whereArgs.add(status);
      }

      if (requestType != null && requestType.isNotEmpty) {
        whereClause += ' AND request_type = ?';
        whereArgs.add(requestType);
      }

      if (priority != null && priority.isNotEmpty) {
        whereClause += ' AND priority = ?';
        whereArgs.add(priority);
      }

      if (fromDate != null) {
        whereClause += ' AND requested_at >= ?';
        whereArgs.add(fromDate.toIso8601String());
      }

      if (toDate != null) {
        whereClause += ' AND requested_at <= ?';
        whereArgs.add(toDate.toIso8601String());
      }

      final result = await db.query(
        AppConstants.approvalsTable,
        where: whereClause,
        whereArgs: whereArgs.isNotEmpty ? whereArgs : null,
        orderBy: 'requested_at DESC',
      );

      return result.map((map) => Approval.fromMap(map)).toList();
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب الموافقات',
        category: 'ApprovalService',
        data: {'error': e.toString()},
      );
      return [];
    }
  }

  /// الحصول على موافقة بالمعرف
  Future<Approval?> getApprovalById(int id) async {
    try {
      final db = await _databaseHelper.database;

      final result = await db.query(
        AppConstants.approvalsTable,
        where: 'id = ?',
        whereArgs: [id],
      );

      if (result.isNotEmpty) {
        return Approval.fromMap(result.first);
      }
      return null;
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب الموافقة',
        category: 'ApprovalService',
        data: {'error': e.toString(), 'id': id},
      );
      return null;
    }
  }

  /// إنشاء طلب موافقة جديد
  Future<Approval> createApproval({
    required String requestType,
    required int requestId,
    required int employeeId,
    int? approverId,
    String priority = 'normal',
    required String title,
    String? description,
    Map<String, dynamic>? requestData,
  }) async {
    try {
      // التحقق من صحة البيانات
      await _validateApproval(
        requestType: requestType,
        employeeId: employeeId,
        title: title,
        priority: priority,
      );

      final db = await _databaseHelper.database;
      final now = DateTime.now();

      final approval = Approval(
        requestType: requestType,
        requestId: requestId,
        employeeId: employeeId,
        approverId: approverId,
        priority: priority,
        title: title,
        description: description,
        requestData: requestData != null ? jsonEncode(requestData) : null,
        requestedAt: now,
        createdAt: now,
        updatedAt: now,
      );

      final id = await db.insert(AppConstants.approvalsTable, approval.toMap());

      final newApproval = approval.copyWith(id: id);

      // تسجيل في سجل المراجعة
      await AuditService.log(
        action: 'CREATE',
        entityType: 'Approval',
        entityId: id,
        description: 'إنشاء طلب موافقة جديد: $title',
        newValues: newApproval.toMap(),
      );

      LoggingService.info(
        'تم إنشاء طلب موافقة جديد',
        category: 'ApprovalService',
        data: {
          'approvalId': id,
          'requestType': requestType,
          'employeeId': employeeId,
        },
      );

      return newApproval;
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء طلب الموافقة',
        category: 'ApprovalService',
        data: {'error': e.toString()},
      );
      rethrow;
    }
  }

  /// الموافقة على طلب
  Future<Approval> approveRequest({
    required int approvalId,
    required int approverId,
    String? approvalNotes,
  }) async {
    try {
      final approval = await getApprovalById(approvalId);
      if (approval == null) {
        throw ValidationException('طلب الموافقة غير موجود');
      }

      if (approval.status != 'pending') {
        throw ValidationException(
          'لا يمكن الموافقة على طلب تم التعامل معه مسبقاً',
        );
      }

      final db = await _databaseHelper.database;
      final now = DateTime.now();

      final updatedApproval = approval.copyWith(
        status: 'approved',
        approverId: approverId,
        approvalNotes: approvalNotes,
        approvedAt: now,
        updatedAt: now,
      );

      await db.update(
        AppConstants.approvalsTable,
        updatedApproval.toMap(),
        where: 'id = ?',
        whereArgs: [approvalId],
      );

      // تسجيل في سجل المراجعة
      await AuditService.log(
        action: 'APPROVE',
        entityType: 'Approval',
        entityId: approvalId,
        description: 'الموافقة على الطلب: ${approval.title}',
        oldValues: approval.toMap(),
        newValues: updatedApproval.toMap(),
      );

      LoggingService.info(
        'تم الموافقة على الطلب',
        category: 'ApprovalService',
        data: {
          'approvalId': approvalId,
          'approverId': approverId,
          'requestType': approval.requestType,
        },
      );

      return updatedApproval;
    } catch (e) {
      LoggingService.error(
        'خطأ في الموافقة على الطلب',
        category: 'ApprovalService',
        data: {'error': e.toString(), 'approvalId': approvalId},
      );
      rethrow;
    }
  }

  /// رفض طلب
  Future<Approval> rejectRequest({
    required int approvalId,
    required int approverId,
    required String rejectionReason,
  }) async {
    try {
      final approval = await getApprovalById(approvalId);
      if (approval == null) {
        throw ValidationException('طلب الموافقة غير موجود');
      }

      if (approval.status != 'pending') {
        throw ValidationException('لا يمكن رفض طلب تم التعامل معه مسبقاً');
      }

      if (rejectionReason.trim().isEmpty) {
        throw ValidationException('سبب الرفض مطلوب');
      }

      final db = await _databaseHelper.database;
      final now = DateTime.now();

      final updatedApproval = approval.copyWith(
        status: 'rejected',
        approverId: approverId,
        rejectionReason: rejectionReason,
        approvedAt: now,
        updatedAt: now,
      );

      await db.update(
        AppConstants.approvalsTable,
        updatedApproval.toMap(),
        where: 'id = ?',
        whereArgs: [approvalId],
      );

      // تسجيل في سجل المراجعة
      await AuditService.log(
        action: 'REJECT',
        entityType: 'Approval',
        entityId: approvalId,
        description: 'رفض الطلب: ${approval.title}',
        oldValues: approval.toMap(),
        newValues: updatedApproval.toMap(),
      );

      LoggingService.info(
        'تم رفض الطلب',
        category: 'ApprovalService',
        data: {
          'approvalId': approvalId,
          'approverId': approverId,
          'requestType': approval.requestType,
        },
      );

      return updatedApproval;
    } catch (e) {
      LoggingService.error(
        'خطأ في رفض الطلب',
        category: 'ApprovalService',
        data: {'error': e.toString(), 'approvalId': approvalId},
      );
      rethrow;
    }
  }

  /// إلغاء طلب
  Future<Approval> cancelRequest({
    required int approvalId,
    required int employeeId,
  }) async {
    try {
      final approval = await getApprovalById(approvalId);
      if (approval == null) {
        throw ValidationException('طلب الموافقة غير موجود');
      }

      if (approval.employeeId != employeeId) {
        throw ValidationException('لا يمكن إلغاء طلب موافقة لموظف آخر');
      }

      if (approval.status != 'pending') {
        throw ValidationException('لا يمكن إلغاء طلب تم التعامل معه مسبقاً');
      }

      final db = await _databaseHelper.database;
      final now = DateTime.now();

      final updatedApproval = approval.copyWith(
        status: 'cancelled',
        updatedAt: now,
      );

      await db.update(
        AppConstants.approvalsTable,
        updatedApproval.toMap(),
        where: 'id = ?',
        whereArgs: [approvalId],
      );

      // تسجيل في سجل المراجعة
      await AuditService.log(
        action: 'CANCEL',
        entityType: 'Approval',
        entityId: approvalId,
        description: 'إلغاء الطلب: ${approval.title}',
        oldValues: approval.toMap(),
        newValues: updatedApproval.toMap(),
      );

      LoggingService.info(
        'تم إلغاء الطلب',
        category: 'ApprovalService',
        data: {
          'approvalId': approvalId,
          'employeeId': employeeId,
          'requestType': approval.requestType,
        },
      );

      return updatedApproval;
    } catch (e) {
      LoggingService.error(
        'خطأ في إلغاء الطلب',
        category: 'ApprovalService',
        data: {'error': e.toString(), 'approvalId': approvalId},
      );
      rethrow;
    }
  }

  /// الحصول على الطلبات المعلقة للموافقة
  Future<List<Approval>> getPendingApprovals({
    int? approverId,
    String? requestType,
    String? priority,
  }) async {
    return getAllApprovals(
      approverId: approverId,
      status: 'pending',
      requestType: requestType,
      priority: priority,
    );
  }

  /// الحصول على طلبات موظف معين
  Future<List<Approval>> getEmployeeApprovals({
    required int employeeId,
    String? status,
    String? requestType,
  }) async {
    return getAllApprovals(
      employeeId: employeeId,
      status: status,
      requestType: requestType,
    );
  }

  /// دوال التحقق من صحة البيانات

  Future<void> _validateApproval({
    required String requestType,
    required int employeeId,
    required String title,
    required String priority,
  }) async {
    if (title.trim().isEmpty) {
      throw ValidationException('عنوان الطلب مطلوب');
    }

    if (![
      'leave',
      'loan',
      'overtime',
      'expense',
      'training',
      'other',
    ].contains(requestType)) {
      throw ValidationException('نوع الطلب غير صحيح');
    }

    if (!['low', 'normal', 'high', 'urgent'].contains(priority)) {
      throw ValidationException('أولوية الطلب غير صحيحة');
    }

    // التحقق من وجود الموظف
    final db = await _databaseHelper.database;
    final employeeResult = await db.query(
      AppConstants.employeesTable,
      where: 'id = ?',
      whereArgs: [employeeId],
    );

    if (employeeResult.isEmpty) {
      throw ValidationException('الموظف غير موجود');
    }
  }

  /// إنشاء طلب موافقة إجازة
  Future<Approval> createLeaveApproval({
    required int leaveId,
    required int employeeId,
    required String leaveType,
    required DateTime startDate,
    required DateTime endDate,
    required int totalDays,
    String? reason,
    int? approverId,
  }) async {
    final requestData = {
      'leave_type': leaveType,
      'start_date': startDate.toIso8601String(),
      'end_date': endDate.toIso8601String(),
      'total_days': totalDays,
      'reason': reason,
    };

    return createApproval(
      requestType: 'leave',
      requestId: leaveId,
      employeeId: employeeId,
      approverId: approverId,
      title: 'طلب إجازة $leaveType',
      description:
          'طلب إجازة من ${startDate.day}/${startDate.month}/${startDate.year} إلى ${endDate.day}/${endDate.month}/${endDate.year}',
      requestData: requestData,
    );
  }

  /// إنشاء طلب موافقة قرض
  Future<Approval> createLoanApproval({
    required int loanId,
    required int employeeId,
    required double amount,
    required int installments,
    String? purpose,
    int? approverId,
  }) async {
    final requestData = {
      'amount': amount,
      'installments': installments,
      'purpose': purpose,
    };

    return createApproval(
      requestType: 'loan',
      requestId: loanId,
      employeeId: employeeId,
      approverId: approverId,
      priority: amount > 1000000
          ? 'high'
          : 'normal', // أولوية عالية للمبالغ الكبيرة
      title: 'طلب قرض بمبلغ ${amount.toStringAsFixed(0)} ل.س',
      description:
          'طلب قرض بمبلغ ${amount.toStringAsFixed(0)} ل.س على $installments قسط',
      requestData: requestData,
    );
  }

  /// البحث في الموافقات
  Future<List<Approval>> searchApprovals({
    required String searchQuery,
    String? requestType,
    String? status,
    int? employeeId,
    int? approverId,
  }) async {
    try {
      final db = await _databaseHelper.database;

      String whereClause = '1=1';
      List<dynamic> whereArgs = [];

      // البحث في العنوان والوصف
      if (searchQuery.trim().isNotEmpty) {
        whereClause += ' AND (title LIKE ? OR description LIKE ?)';
        final searchPattern = '%${searchQuery.trim()}%';
        whereArgs.addAll([searchPattern, searchPattern]);
      }

      // فلترة حسب نوع الطلب
      if (requestType != null && requestType.isNotEmpty) {
        whereClause += ' AND request_type = ?';
        whereArgs.add(requestType);
      }

      // فلترة حسب الحالة
      if (status != null && status.isNotEmpty) {
        whereClause += ' AND status = ?';
        whereArgs.add(status);
      }

      // فلترة حسب الموظف
      if (employeeId != null) {
        whereClause += ' AND employee_id = ?';
        whereArgs.add(employeeId);
      }

      // فلترة حسب المعتمد
      if (approverId != null) {
        whereClause += ' AND approver_id = ?';
        whereArgs.add(approverId);
      }

      final result = await db.query(
        AppConstants.approvalsTable,
        where: whereClause,
        whereArgs: whereArgs.isNotEmpty ? whereArgs : null,
        orderBy: 'requested_at DESC',
      );

      return result.map((map) => Approval.fromMap(map)).toList();
    } catch (e) {
      LoggingService.error(
        'خطأ في البحث في الموافقات',
        category: 'ApprovalService',
        data: {'error': e.toString(), 'searchQuery': searchQuery},
      );
      return [];
    }
  }

  /// الحصول على إحصائيات الموافقات
  Future<Map<String, dynamic>> getApprovalStatistics() async {
    try {
      final db = await _databaseHelper.database;

      // إجمالي الطلبات
      final totalResult = await db.rawQuery(
        'SELECT COUNT(*) as total FROM ${AppConstants.approvalsTable}',
      );
      final total = totalResult.first['total'] as int;

      // الطلبات المعلقة
      final pendingResult = await db.rawQuery(
        'SELECT COUNT(*) as pending FROM ${AppConstants.approvalsTable} WHERE status = ?',
        ['pending'],
      );
      final pending = pendingResult.first['pending'] as int;

      // الطلبات المعتمدة
      final approvedResult = await db.rawQuery(
        'SELECT COUNT(*) as approved FROM ${AppConstants.approvalsTable} WHERE status = ?',
        ['approved'],
      );
      final approved = approvedResult.first['approved'] as int;

      // الطلبات المرفوضة
      final rejectedResult = await db.rawQuery(
        'SELECT COUNT(*) as rejected FROM ${AppConstants.approvalsTable} WHERE status = ?',
        ['rejected'],
      );
      final rejected = rejectedResult.first['rejected'] as int;

      // الطلبات الملغاة
      final cancelledResult = await db.rawQuery(
        'SELECT COUNT(*) as cancelled FROM ${AppConstants.approvalsTable} WHERE status = ?',
        ['cancelled'],
      );
      final cancelled = cancelledResult.first['cancelled'] as int;

      // إحصائيات حسب نوع الطلب
      final typeStatsResult = await db.rawQuery('''
        SELECT request_type, COUNT(*) as count
        FROM ${AppConstants.approvalsTable}
        GROUP BY request_type
      ''');

      final typeStats = <String, int>{};
      for (final row in typeStatsResult) {
        typeStats[row['request_type'] as String] = row['count'] as int;
      }

      // إحصائيات حسب الأولوية
      final priorityStatsResult = await db.rawQuery('''
        SELECT priority, COUNT(*) as count
        FROM ${AppConstants.approvalsTable}
        GROUP BY priority
      ''');

      final priorityStats = <String, int>{};
      for (final row in priorityStatsResult) {
        priorityStats[row['priority'] as String] = row['count'] as int;
      }

      return {
        'total': total,
        'pending': pending,
        'approved': approved,
        'rejected': rejected,
        'cancelled': cancelled,
        'typeStats': typeStats,
        'priorityStats': priorityStats,
        'approvalRate': total > 0 ? (approved / total * 100).round() : 0,
        'rejectionRate': total > 0 ? (rejected / total * 100).round() : 0,
      };
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب إحصائيات الموافقات',
        category: 'ApprovalService',
        data: {'error': e.toString()},
      );
      return {
        'total': 0,
        'pending': 0,
        'approved': 0,
        'rejected': 0,
        'cancelled': 0,
        'typeStats': <String, int>{},
        'priorityStats': <String, int>{},
        'approvalRate': 0,
        'rejectionRate': 0,
      };
    }
  }

  // ===== الطرق المحسنة للموافقات الإلكترونية =====

  /// إنشاء طلب موافقة محسن مع سير العمل
  Future<ApprovalRequest> createEnhancedApprovalRequest({
    required String requestType,
    required String title,
    required String description,
    required int requesterId,
    required String requesterName,
    int? entityId,
    required Map<String, dynamic> requestData,
    double? amount,
    required String priority,
    DateTime? dueDate,
    List<String> attachments = const [],
    Map<String, dynamic> metadata = const {},
  }) async {
    try {
      // التحقق من صحة البيانات
      await _validateEnhancedApprovalRequest(
        requestType: requestType,
        title: title,
        description: description,
        requesterId: requesterId,
        priority: priority,
      );

      final db = await _databaseHelper.database;
      final now = DateTime.now();

      // الحصول على سير العمل المناسب
      final workflow = await _getWorkflowForRequestType(requestType);
      if (workflow == null) {
        throw ValidationException('لا يوجد سير عمل محدد لهذا النوع من الطلبات');
      }

      // الحصول على أول مرحلة في سير العمل
      final firstStage = await _getFirstWorkflowStage(workflow['id']);
      if (firstStage == null) {
        throw ValidationException('سير العمل غير مكتمل');
      }

      final request = ApprovalRequest(
        requestType: requestType,
        title: title,
        description: description,
        requesterId: requesterId,
        requesterName: requesterName,
        entityId: entityId,
        requestData: requestData,
        amount: amount,
        priority: priority,
        status: ApprovalStatus.pending.value,
        currentStage: firstStage['stage_name'],
        currentApproverId: firstStage['approver_id'],
        requestDate: now,
        dueDate: dueDate,
        attachments: attachments,
        metadata: metadata,
        createdAt: now,
        updatedAt: now,
      );

      final id = await db.insert(
        AppConstants.approvalRequestsTable,
        request.toMap(),
      );

      final newRequest = request.copyWith(id: id);

      // إرسال إشعار للمعتمد
      await _sendApprovalNotification(newRequest, firstStage);

      // تسجيل في سجل المراجعة
      await AuditService.log(
        action: 'CREATE',
        entityType: 'ApprovalRequest',
        entityId: id,
        description: 'إنشاء طلب موافقة جديد: $title',
        newValues: newRequest.toMap(),
      );

      LoggingService.info(
        'تم إنشاء طلب موافقة محسن بنجاح',
        category: 'ApprovalService',
        data: {'id': id, 'type': requestType, 'title': title},
      );

      return newRequest;
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء طلب الموافقة المحسن',
        category: 'ApprovalService',
        data: {'requestType': requestType, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// الموافقة المحسنة على طلب مع سير العمل
  Future<ApprovalRequest> approveEnhancedRequest({
    required int requestId,
    required int approverId,
    required String approverName,
    String? comments,
    Map<String, dynamic> actionData = const {},
  }) async {
    try {
      final request = await getEnhancedApprovalRequestById(requestId);
      if (request == null) {
        throw ValidationException('الطلب غير موجود');
      }

      if (request.status != ApprovalStatus.pending.value) {
        throw ValidationException('لا يمكن الموافقة على طلب غير معلق');
      }

      if (request.currentApproverId != approverId) {
        throw ValidationException('ليس لديك صلاحية الموافقة على هذا الطلب');
      }

      final db = await _databaseHelper.database;
      final now = DateTime.now();

      // تسجيل الموافقة
      await _recordEnhancedApprovalAction(
        requestId: requestId,
        approverId: approverId,
        approverName: approverName,
        action: 'approved',
        comments: comments,
        actionData: actionData,
      );

      // الانتقال للمرحلة التالية
      final nextStage = await _getNextWorkflowStage(
        request.requestType,
        request.currentStage,
      );

      ApprovalRequest updatedRequest;
      if (nextStage != null) {
        // الانتقال للمرحلة التالية
        updatedRequest = request.copyWith(
          currentStage: nextStage['stage_name'],
          currentApproverId: nextStage['approver_id'],
          updatedAt: now,
        );

        // إرسال إشعار للمعتمد التالي
        await _sendApprovalNotification(updatedRequest, nextStage);
      } else {
        // اكتمال الموافقة
        updatedRequest = request.copyWith(
          status: ApprovalStatus.approved.value,
          currentStage: 'completed',
          currentApproverId: null,
          completedDate: now,
          updatedAt: now,
        );

        // إرسال إشعار بالموافقة النهائية
        await _sendFinalApprovalNotification(updatedRequest);
      }

      await db.update(
        AppConstants.approvalRequestsTable,
        updatedRequest.toMap(),
        where: 'id = ?',
        whereArgs: [requestId],
      );

      // تسجيل في سجل المراجعة
      await AuditService.log(
        action: 'UPDATE',
        entityType: 'ApprovalRequest',
        entityId: requestId,
        description: 'الموافقة على الطلب من قبل $approverName',
        oldValues: request.toMap(),
        newValues: updatedRequest.toMap(),
      );

      LoggingService.info(
        'تم الموافقة على الطلب المحسن بنجاح',
        category: 'ApprovalService',
        data: {'requestId': requestId, 'approverId': approverId},
      );

      return updatedRequest;
    } catch (e) {
      LoggingService.error(
        'خطأ في الموافقة على الطلب المحسن',
        category: 'ApprovalService',
        data: {'requestId': requestId, 'error': e.toString()},
      );
      rethrow;
    }
  }

  // ===== الطرق المساعدة للموافقات المحسنة =====

  /// التحقق من صحة بيانات طلب الموافقة المحسن
  Future<void> _validateEnhancedApprovalRequest({
    required String requestType,
    required String title,
    required String description,
    required int requesterId,
    required String priority,
  }) async {
    if (requestType.trim().isEmpty) {
      throw ValidationException('نوع الطلب مطلوب');
    }

    if (title.trim().isEmpty) {
      throw ValidationException('عنوان الطلب مطلوب');
    }

    if (title.length < 5) {
      throw ValidationException('عنوان الطلب يجب أن يكون 5 أحرف على الأقل');
    }

    if (description.trim().isEmpty) {
      throw ValidationException('وصف الطلب مطلوب');
    }

    if (description.length < 10) {
      throw ValidationException('وصف الطلب يجب أن يكون 10 أحرف على الأقل');
    }

    if (requesterId <= 0) {
      throw ValidationException('معرف مقدم الطلب غير صحيح');
    }

    final validPriorities = ['low', 'medium', 'high', 'urgent'];
    if (!validPriorities.contains(priority)) {
      throw ValidationException('أولوية الطلب غير صحيحة');
    }
  }

  /// الحصول على سير العمل لنوع الطلب
  Future<Map<String, dynamic>?> _getWorkflowForRequestType(
    String requestType,
  ) async {
    try {
      final db = await _databaseHelper.database;
      final result = await db.query(
        'approval_workflows',
        where: 'request_type = ? AND is_active = 1',
        whereArgs: [requestType],
        limit: 1,
      );

      return result.isNotEmpty ? result.first : null;
    } catch (e) {
      return null;
    }
  }

  /// الحصول على أول مرحلة في سير العمل
  Future<Map<String, dynamic>?> _getFirstWorkflowStage(int workflowId) async {
    try {
      final db = await _databaseHelper.database;
      final result = await db.query(
        'approval_stages',
        where: 'workflow_id = ? AND is_active = 1',
        whereArgs: [workflowId],
        orderBy: 'order ASC',
        limit: 1,
      );

      return result.isNotEmpty ? result.first : null;
    } catch (e) {
      return null;
    }
  }

  /// الحصول على المرحلة التالية في سير العمل
  Future<Map<String, dynamic>?> _getNextWorkflowStage(
    String requestType,
    String currentStage,
  ) async {
    try {
      final workflow = await _getWorkflowForRequestType(requestType);
      if (workflow == null) return null;

      final db = await _databaseHelper.database;

      // الحصول على ترتيب المرحلة الحالية
      final currentStageResult = await db.query(
        'approval_stages',
        where: 'workflow_id = ? AND stage_name = ?',
        whereArgs: [workflow['id'], currentStage],
        limit: 1,
      );

      if (currentStageResult.isEmpty) return null;

      final currentOrder = currentStageResult.first['order'] as int;

      // الحصول على المرحلة التالية
      final nextStageResult = await db.query(
        'approval_stages',
        where: 'workflow_id = ? AND order > ? AND is_active = 1',
        whereArgs: [workflow['id'], currentOrder],
        orderBy: 'order ASC',
        limit: 1,
      );

      return nextStageResult.isNotEmpty ? nextStageResult.first : null;
    } catch (e) {
      return null;
    }
  }

  /// الحصول على طلب موافقة محسن بالمعرف
  Future<ApprovalRequest?> getEnhancedApprovalRequestById(int id) async {
    try {
      final db = await _databaseHelper.database;
      final result = await db.query(
        'approval_requests',
        where: 'id = ?',
        whereArgs: [id],
        limit: 1,
      );

      if (result.isNotEmpty) {
        return ApprovalRequest.fromMap(result.first);
      }
      return null;
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب طلب الموافقة المحسن',
        category: 'ApprovalService',
        data: {'id': id, 'error': e.toString()},
      );
      return null;
    }
  }

  /// تسجيل إجراء الموافقة المحسن
  Future<void> _recordEnhancedApprovalAction({
    required int requestId,
    required int approverId,
    required String approverName,
    required String action,
    String? comments,
    Map<String, dynamic> actionData = const {},
  }) async {
    try {
      final db = await _databaseHelper.database;

      // الحصول على معلومات المرحلة الحالية
      final request = await getEnhancedApprovalRequestById(requestId);
      if (request == null) return;

      final record = ApprovalRecord(
        requestId: requestId,
        stageId: 0, // سيتم تحديثه لاحقاً
        stageName: request.currentStage,
        approverId: approverId,
        approverName: approverName,
        action: action,
        comments: comments,
        actionDate: DateTime.now(),
        actionData: actionData,
        createdAt: DateTime.now(),
      );

      await db.insert('approval_records', record.toMap());
    } catch (e) {
      LoggingService.error(
        'خطأ في تسجيل إجراء الموافقة المحسن',
        category: 'ApprovalService',
        data: {'requestId': requestId, 'error': e.toString()},
      );
    }
  }

  /// إرسال إشعار الموافقة
  Future<void> _sendApprovalNotification(
    ApprovalRequest request,
    Map<String, dynamic> stage,
  ) async {
    try {
      await _notificationService.sendNotification(
        userId: stage['approver_id'],
        title: 'طلب موافقة جديد',
        message: 'لديك طلب موافقة جديد: ${request.title}',
        type: 'approval_request',
        data: {'requestId': request.id, 'requestType': request.requestType},
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في إرسال إشعار الموافقة',
        category: 'ApprovalService',
        data: {'requestId': request.id, 'error': e.toString()},
      );
    }
  }

  /// إرسال إشعار الموافقة النهائية
  Future<void> _sendFinalApprovalNotification(ApprovalRequest request) async {
    try {
      await _notificationService.sendNotification(
        userId: request.requesterId,
        title: 'تم اعتماد طلبك',
        message: 'تم اعتماد طلبك: ${request.title}',
        type: 'approval_approved',
        data: {'requestId': request.id, 'requestType': request.requestType},
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في إرسال إشعار الموافقة النهائية',
        category: 'ApprovalService',
        data: {'requestId': request.id, 'error': e.toString()},
      );
    }
  }
}
