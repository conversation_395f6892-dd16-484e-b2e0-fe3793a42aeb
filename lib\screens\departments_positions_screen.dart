/// شاشة إدارة الأقسام والمناصب
/// واجهة شاملة لإدارة الأقسام والمناصب الوظيفية
library;

import 'package:flutter/material.dart';
import '../constants/revolutionary_design_colors.dart';
import '../models/hr_models.dart';
import '../services/department_service.dart';
import '../services/position_service.dart';
import '../services/logging_service.dart';
import '../widgets/loading_widget.dart';
import '../widgets/custom_error_widget.dart' as custom_widgets;

class DepartmentsPositionsScreen extends StatefulWidget {
  const DepartmentsPositionsScreen({super.key});

  @override
  State<DepartmentsPositionsScreen> createState() =>
      _DepartmentsPositionsScreenState();
}

class _DepartmentsPositionsScreenState extends State<DepartmentsPositionsScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final DepartmentService _departmentService = DepartmentService();
  final PositionService _positionService = PositionService();

  List<Department> _departments = [];
  List<Position> _positions = [];
  bool _isLoading = true;
  String? _error;
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final results = await Future.wait([
        _departmentService.getAllDepartments(),
        _positionService.getAllPositions(),
      ]);

      setState(() {
        _departments = results[0] as List<Department>;
        _positions = results[1] as List<Position>;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });

      LoggingService.error(
        'خطأ في تحميل بيانات الأقسام والمناصب',
        category: 'DepartmentsPositionsScreen',
        data: {'error': e.toString()},
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة الأقسام والمناصب'),
        backgroundColor: RevolutionaryColors.damascusSky,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadData,
            tooltip: 'تحديث',
          ),
          PopupMenuButton<String>(
            icon: const Icon(Icons.more_vert),
            onSelected: (value) {
              switch (value) {
                case 'create_defaults':
                  _createDefaults();
                  break;
                case 'export':
                  _exportData();
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'create_defaults',
                child: Text('إنشاء البيانات الافتراضية'),
              ),
              const PopupMenuItem(
                value: 'export',
                child: Text('تصدير البيانات'),
              ),
            ],
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          tabs: const [
            Tab(icon: Icon(Icons.business), text: 'الأقسام'),
            Tab(icon: Icon(Icons.work), text: 'المناصب'),
          ],
        ),
      ),
      body: _buildBody(),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const LoadingWidget();
    }

    if (_error != null) {
      return custom_widgets.ErrorWidget(message: _error!, onRetry: _loadData);
    }

    return TabBarView(
      controller: _tabController,
      children: [_buildDepartmentsTab(), _buildPositionsTab()],
    );
  }

  Widget _buildDepartmentsTab() {
    return Column(
      children: [
        // شريط البحث
        Container(
          padding: const EdgeInsets.all(16),
          child: TextField(
            decoration: const InputDecoration(
              hintText: 'البحث في الأقسام...',
              prefixIcon: Icon(Icons.search),
              border: OutlineInputBorder(),
            ),
            onChanged: (value) {
              setState(() {
                _searchQuery = value;
              });
            },
          ),
        ),
        // قائمة الأقسام
        Expanded(child: _buildDepartmentsList()),
      ],
    );
  }

  Widget _buildDepartmentsList() {
    final filteredDepartments = _departments.where((dept) {
      if (_searchQuery.isEmpty) return true;
      final searchLower = _searchQuery.toLowerCase();
      return dept.name.toLowerCase().contains(searchLower) ||
          (dept.description?.toLowerCase().contains(searchLower) ?? false);
    }).toList();

    if (filteredDepartments.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.business, size: 64, color: Colors.grey),
            const SizedBox(height: 16),
            Text(
              _searchQuery.isEmpty ? 'لا توجد أقسام' : 'لا توجد نتائج للبحث',
              style: const TextStyle(fontSize: 18, color: Colors.grey),
            ),
            if (_searchQuery.isEmpty) ...[
              const SizedBox(height: 8),
              ElevatedButton.icon(
                onPressed: _showAddDepartmentDialog,
                icon: const Icon(Icons.add),
                label: const Text('إضافة قسم جديد'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: RevolutionaryColors.successGlow,
                ),
              ),
            ],
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: filteredDepartments.length,
      itemBuilder: (context, index) {
        final department = filteredDepartments[index];
        return _buildDepartmentCard(department);
      },
    );
  }

  Widget _buildDepartmentCard(Department department) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ExpansionTile(
        leading: CircleAvatar(
          backgroundColor: department.isActive
              ? RevolutionaryColors.successGlow.withValues(alpha: 0.2)
              : Colors.grey.withValues(alpha: 0.2),
          child: Icon(
            Icons.business,
            color: department.isActive
                ? RevolutionaryColors.successGlow
                : Colors.grey,
          ),
        ),
        title: Text(
          department.name,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Text(department.description ?? 'لا يوجد وصف'),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (!department.isActive)
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.grey,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Text(
                  'غير نشط',
                  style: TextStyle(color: Colors.white, fontSize: 12),
                ),
              ),
            const SizedBox(width: 8),
            PopupMenuButton<String>(
              onSelected: (value) {
                switch (value) {
                  case 'edit':
                    _showEditDepartmentDialog(department);
                    break;
                  case 'toggle':
                    _toggleDepartmentStatus(department);
                    break;
                  case 'delete':
                    _deleteDepartment(department);
                    break;
                }
              },
              itemBuilder: (context) => [
                const PopupMenuItem(value: 'edit', child: Text('تعديل')),
                PopupMenuItem(
                  value: 'toggle',
                  child: Text(department.isActive ? 'إلغاء التفعيل' : 'تفعيل'),
                ),
                const PopupMenuItem(
                  value: 'delete',
                  child: Text('حذف', style: TextStyle(color: Colors.red)),
                ),
              ],
            ),
          ],
        ),
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (department.managerId != null) ...[
                  Row(
                    children: [
                      const Icon(Icons.person, size: 20, color: Colors.grey),
                      const SizedBox(width: 8),
                      Text('معرف المدير: ${department.managerId}'),
                    ],
                  ),
                  const SizedBox(height: 8),
                ],
                Row(
                  children: [
                    const Icon(
                      Icons.calendar_today,
                      size: 20,
                      color: Colors.grey,
                    ),
                    const SizedBox(width: 8),
                    Text('تاريخ الإنشاء: ${_formatDate(department.createdAt)}'),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    ElevatedButton.icon(
                      onPressed: () => _viewDepartmentPositions(department),
                      icon: const Icon(Icons.work),
                      label: const Text('عرض المناصب'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: RevolutionaryColors.infoTurquoise,
                      ),
                    ),
                    const SizedBox(width: 12),
                    OutlinedButton.icon(
                      onPressed: () => _viewDepartmentEmployees(department),
                      icon: const Icon(Icons.people),
                      label: const Text('عرض الموظفين'),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPositionsTab() {
    return Column(
      children: [
        // شريط البحث
        Container(
          padding: const EdgeInsets.all(16),
          child: TextField(
            decoration: const InputDecoration(
              hintText: 'البحث في المناصب...',
              prefixIcon: Icon(Icons.search),
              border: OutlineInputBorder(),
            ),
            onChanged: (value) {
              setState(() {
                _searchQuery = value;
              });
            },
          ),
        ),
        // قائمة المناصب
        Expanded(child: _buildPositionsList()),
      ],
    );
  }

  Widget _buildPositionsList() {
    final filteredPositions = _positions.where((position) {
      if (_searchQuery.isEmpty) return true;
      final searchLower = _searchQuery.toLowerCase();
      return position.title.toLowerCase().contains(searchLower) ||
          (position.description?.toLowerCase().contains(searchLower) ?? false);
    }).toList();

    if (filteredPositions.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.work, size: 64, color: Colors.grey),
            const SizedBox(height: 16),
            Text(
              _searchQuery.isEmpty ? 'لا توجد مناصب' : 'لا توجد نتائج للبحث',
              style: const TextStyle(fontSize: 18, color: Colors.grey),
            ),
            if (_searchQuery.isEmpty) ...[
              const SizedBox(height: 8),
              ElevatedButton.icon(
                onPressed: _showAddPositionDialog,
                icon: const Icon(Icons.add),
                label: const Text('إضافة منصب جديد'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: RevolutionaryColors.warningAmber,
                ),
              ),
            ],
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: filteredPositions.length,
      itemBuilder: (context, index) {
        final position = filteredPositions[index];
        return _buildPositionCard(position);
      },
    );
  }

  Widget _buildPositionCard(Position position) {
    final department = _departments.firstWhere(
      (dept) => dept.id == position.departmentId,
      orElse: () => Department(
        name: 'قسم غير معروف',
        code: 'UNKNOWN',
        isActive: false,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
    );

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ExpansionTile(
        leading: CircleAvatar(
          backgroundColor: position.isActive
              ? RevolutionaryColors.warningAmber.withValues(alpha: 0.2)
              : Colors.grey.withValues(alpha: 0.2),
          child: Icon(
            Icons.work,
            color: position.isActive
                ? RevolutionaryColors.warningAmber
                : Colors.grey,
          ),
        ),
        title: Text(
          position.title,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Text('القسم: ${department.name}'),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (!position.isActive)
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.grey,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Text(
                  'غير نشط',
                  style: TextStyle(color: Colors.white, fontSize: 12),
                ),
              ),
            const SizedBox(width: 8),
            PopupMenuButton<String>(
              onSelected: (value) {
                switch (value) {
                  case 'edit':
                    _showEditPositionDialog(position);
                    break;
                  case 'toggle':
                    _togglePositionStatus(position);
                    break;
                  case 'delete':
                    _deletePosition(position);
                    break;
                }
              },
              itemBuilder: (context) => [
                const PopupMenuItem(value: 'edit', child: Text('تعديل')),
                PopupMenuItem(
                  value: 'toggle',
                  child: Text(position.isActive ? 'إلغاء التفعيل' : 'تفعيل'),
                ),
                const PopupMenuItem(
                  value: 'delete',
                  child: Text('حذف', style: TextStyle(color: Colors.red)),
                ),
              ],
            ),
          ],
        ),
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (position.description != null) ...[
                  Text(
                    position.description!,
                    style: const TextStyle(fontSize: 14),
                  ),
                  const SizedBox(height: 12),
                ],
                Row(
                  children: [
                    Expanded(
                      child: _buildPositionDetail(
                        'الحد الأدنى للراتب',
                        position.minSalary > 0
                            ? position.minSalary.toStringAsFixed(0)
                            : 'غير محدد',
                      ),
                    ),
                    Expanded(
                      child: _buildPositionDetail(
                        'الحد الأقصى للراتب',
                        position.maxSalary > 0
                            ? position.maxSalary.toStringAsFixed(0)
                            : 'غير محدد',
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                _buildPositionDetail(
                  'تاريخ الإنشاء',
                  _formatDate(position.createdAt),
                ),
                if (position.requirements != null) ...[
                  const SizedBox(height: 12),
                  const Text(
                    'المتطلبات:',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 4),
                  Text(position.requirements!),
                ],
                const SizedBox(height: 16),
                ElevatedButton.icon(
                  onPressed: () => _viewPositionEmployees(position),
                  icon: const Icon(Icons.people),
                  label: const Text('عرض الموظفين'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: RevolutionaryColors.infoTurquoise,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPositionDetail(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 2),
        Text(
          value,
          style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
        ),
      ],
    );
  }

  Widget? _buildFloatingActionButton() {
    switch (_tabController.index) {
      case 0: // الأقسام
        return FloatingActionButton(
          onPressed: _showAddDepartmentDialog,
          backgroundColor: RevolutionaryColors.successGlow,
          child: const Icon(Icons.add, color: Colors.white),
        );
      case 1: // المناصب
        return FloatingActionButton(
          onPressed: _showAddPositionDialog,
          backgroundColor: RevolutionaryColors.warningAmber,
          child: const Icon(Icons.add, color: Colors.white),
        );
      default:
        return null;
    }
  }

  // دوال الأحداث والحوارات

  void _showAddDepartmentDialog() {
    _showDepartmentDialog();
  }

  void _showDepartmentDialog({Department? department}) {
    final isEditing = department != null;
    final nameController = TextEditingController(text: department?.name ?? '');
    final codeController = TextEditingController(text: department?.code ?? '');
    final descriptionController = TextEditingController(
      text: department?.description ?? '',
    );

    bool isActive = department?.isActive ?? true;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          title: Text(isEditing ? 'تعديل القسم' : 'إضافة قسم جديد'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  controller: nameController,
                  decoration: const InputDecoration(
                    labelText: 'اسم القسم *',
                    border: OutlineInputBorder(),
                  ),
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: codeController,
                  decoration: const InputDecoration(
                    labelText: 'رمز القسم *',
                    border: OutlineInputBorder(),
                    hintText: 'مثال: FIN',
                  ),
                  textCapitalization: TextCapitalization.characters,
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: descriptionController,
                  decoration: const InputDecoration(
                    labelText: 'الوصف',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 3,
                ),
                const SizedBox(height: 16),
                CheckboxListTile(
                  title: const Text('نشط'),
                  value: isActive,
                  onChanged: (value) {
                    setDialogState(() {
                      isActive = value ?? true;
                    });
                  },
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () async {
                // Store context references before async operations
                final navigator = Navigator.of(context);
                final scaffoldMessenger = ScaffoldMessenger.of(context);

                if (nameController.text.trim().isEmpty ||
                    codeController.text.trim().isEmpty) {
                  scaffoldMessenger.showSnackBar(
                    const SnackBar(
                      content: Text('يرجى ملء جميع الحقول المطلوبة'),
                      backgroundColor: RevolutionaryColors.errorCoral,
                    ),
                  );
                  return;
                }

                try {
                  if (isEditing) {
                    final updatedDepartment = department.copyWith(
                      name: nameController.text.trim(),
                      code: codeController.text.trim().toUpperCase(),
                      description: descriptionController.text.trim().isEmpty
                          ? null
                          : descriptionController.text.trim(),
                      isActive: isActive,
                      updatedAt: DateTime.now(),
                    );
                    await _departmentService.updateDepartment(
                      updatedDepartment,
                    );
                  } else {
                    final newDepartment = Department(
                      name: nameController.text.trim(),
                      code: codeController.text.trim().toUpperCase(),
                      description: descriptionController.text.trim().isEmpty
                          ? null
                          : descriptionController.text.trim(),
                      isActive: isActive,
                      createdAt: DateTime.now(),
                      updatedAt: DateTime.now(),
                    );
                    await _departmentService.createDepartment(
                      name: newDepartment.name,
                      code: newDepartment.code,
                      description: newDepartment.description,
                      isActive: newDepartment.isActive,
                    );
                  }

                  if (mounted) {
                    navigator.pop();
                    await _loadData();

                    if (mounted) {
                      scaffoldMessenger.showSnackBar(
                        SnackBar(
                          content: Text(
                            isEditing
                                ? 'تم تحديث القسم بنجاح'
                                : 'تم إضافة القسم بنجاح',
                          ),
                          backgroundColor: RevolutionaryColors.successGlow,
                        ),
                      );
                    }
                  }
                } catch (e) {
                  if (mounted) {
                    scaffoldMessenger.showSnackBar(
                      SnackBar(
                        content: Text('خطأ في حفظ القسم: $e'),
                        backgroundColor: RevolutionaryColors.errorCoral,
                      ),
                    );
                  }
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: RevolutionaryColors.successGlow,
              ),
              child: Text(isEditing ? 'تحديث' : 'إضافة'),
            ),
          ],
        ),
      ),
    );
  }

  void _showEditDepartmentDialog(Department department) {
    _showDepartmentDialog(department: department);
  }

  void _showAddPositionDialog() {
    _showPositionDialog();
  }

  void _showPositionDialog({Position? position}) {
    final isEditing = position != null;
    final titleController = TextEditingController(text: position?.title ?? '');
    final codeController = TextEditingController(text: position?.code ?? '');
    final descriptionController = TextEditingController(
      text: position?.description ?? '',
    );
    final requirementsController = TextEditingController(
      text: position?.requirements ?? '',
    );
    final minSalaryController = TextEditingController(
      text: position?.minSalary.toString() ?? '',
    );
    final maxSalaryController = TextEditingController(
      text: position?.maxSalary.toString() ?? '',
    );

    int? selectedDepartmentId = position?.departmentId;
    bool isActive = position?.isActive ?? true;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          title: Text(isEditing ? 'تعديل المنصب' : 'إضافة منصب جديد'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  controller: titleController,
                  decoration: const InputDecoration(
                    labelText: 'عنوان المنصب *',
                    border: OutlineInputBorder(),
                  ),
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: codeController,
                  decoration: const InputDecoration(
                    labelText: 'رمز المنصب *',
                    border: OutlineInputBorder(),
                    hintText: 'مثال: MGR',
                  ),
                  textCapitalization: TextCapitalization.characters,
                ),
                const SizedBox(height: 16),
                DropdownButtonFormField<int>(
                  value: selectedDepartmentId,
                  decoration: const InputDecoration(
                    labelText: 'القسم *',
                    border: OutlineInputBorder(),
                  ),
                  items: _departments.map((dept) {
                    return DropdownMenuItem(
                      value: dept.id,
                      child: Text(dept.name),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setDialogState(() {
                      selectedDepartmentId = value;
                    });
                  },
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: descriptionController,
                  decoration: const InputDecoration(
                    labelText: 'الوصف',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 2,
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: requirementsController,
                  decoration: const InputDecoration(
                    labelText: 'المتطلبات',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 2,
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: TextField(
                        controller: minSalaryController,
                        decoration: const InputDecoration(
                          labelText: 'الحد الأدنى للراتب',
                          border: OutlineInputBorder(),
                          suffixText: 'ل.س',
                        ),
                        keyboardType: TextInputType.number,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: TextField(
                        controller: maxSalaryController,
                        decoration: const InputDecoration(
                          labelText: 'الحد الأقصى للراتب',
                          border: OutlineInputBorder(),
                          suffixText: 'ل.س',
                        ),
                        keyboardType: TextInputType.number,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                CheckboxListTile(
                  title: const Text('نشط'),
                  value: isActive,
                  onChanged: (value) {
                    setDialogState(() {
                      isActive = value ?? true;
                    });
                  },
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () async {
                // Store context references before async operations
                final navigator = Navigator.of(context);
                final scaffoldMessenger = ScaffoldMessenger.of(context);

                if (titleController.text.trim().isEmpty ||
                    codeController.text.trim().isEmpty ||
                    selectedDepartmentId == null) {
                  scaffoldMessenger.showSnackBar(
                    const SnackBar(
                      content: Text('يرجى ملء جميع الحقول المطلوبة'),
                      backgroundColor: RevolutionaryColors.errorCoral,
                    ),
                  );
                  return;
                }

                try {
                  final minSalary = minSalaryController.text.trim().isEmpty
                      ? null
                      : double.tryParse(minSalaryController.text);
                  final maxSalary = maxSalaryController.text.trim().isEmpty
                      ? null
                      : double.tryParse(maxSalaryController.text);

                  if (isEditing) {
                    final updatedPosition = position.copyWith(
                      title: titleController.text.trim(),
                      code: codeController.text.trim().toUpperCase(),
                      departmentId: selectedDepartmentId!,
                      description: descriptionController.text.trim().isEmpty
                          ? null
                          : descriptionController.text.trim(),
                      requirements: requirementsController.text.trim().isEmpty
                          ? null
                          : requirementsController.text.trim(),
                      minSalary: minSalary,
                      maxSalary: maxSalary,
                      isActive: isActive,
                      updatedAt: DateTime.now(),
                    );
                    await _positionService.updatePosition(updatedPosition);
                  } else {
                    await _positionService.createPosition(
                      title: titleController.text.trim(),
                      code: codeController.text.trim().toUpperCase(),
                      departmentId: selectedDepartmentId!,
                      description: descriptionController.text.trim().isEmpty
                          ? null
                          : descriptionController.text.trim(),
                      requirements: requirementsController.text.trim().isEmpty
                          ? null
                          : requirementsController.text.trim(),
                      minSalary: minSalary,
                      maxSalary: maxSalary,
                      isActive: isActive,
                    );
                  }

                  if (mounted) {
                    navigator.pop();
                    await _loadData();

                    if (mounted) {
                      scaffoldMessenger.showSnackBar(
                        SnackBar(
                          content: Text(
                            isEditing
                                ? 'تم تحديث المنصب بنجاح'
                                : 'تم إضافة المنصب بنجاح',
                          ),
                          backgroundColor: RevolutionaryColors.successGlow,
                        ),
                      );
                    }
                  }
                } catch (e) {
                  if (mounted) {
                    scaffoldMessenger.showSnackBar(
                      SnackBar(
                        content: Text('خطأ في حفظ المنصب: $e'),
                        backgroundColor: RevolutionaryColors.errorCoral,
                      ),
                    );
                  }
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: RevolutionaryColors.warningAmber,
              ),
              child: Text(isEditing ? 'تحديث' : 'إضافة'),
            ),
          ],
        ),
      ),
    );
  }

  void _showEditPositionDialog(Position position) {
    _showPositionDialog(position: position);
  }

  void _toggleDepartmentStatus(Department department) async {
    try {
      await _departmentService.toggleDepartmentStatus(department.id!);
      await _loadData();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'تم ${department.isActive ? 'إلغاء تفعيل' : 'تفعيل'} القسم بنجاح',
            ),
            backgroundColor: RevolutionaryColors.successGlow,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تغيير حالة القسم: $e'),
            backgroundColor: RevolutionaryColors.errorCoral,
          ),
        );
      }
    }
  }

  void _togglePositionStatus(Position position) async {
    try {
      await _positionService.togglePositionStatus(position.id!);
      await _loadData();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'تم ${position.isActive ? 'إلغاء تفعيل' : 'تفعيل'} المنصب بنجاح',
            ),
            backgroundColor: RevolutionaryColors.successGlow,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تغيير حالة المنصب: $e'),
            backgroundColor: RevolutionaryColors.errorCoral,
          ),
        );
      }
    }
  }

  void _deleteDepartment(Department department) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف القسم "${department.name}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              // Store context references before async operations
              final navigator = Navigator.of(context);
              final scaffoldMessenger = ScaffoldMessenger.of(context);

              navigator.pop();
              try {
                await _departmentService.deleteDepartment(department.id!);
                await _loadData();

                if (mounted) {
                  scaffoldMessenger.showSnackBar(
                    const SnackBar(
                      content: Text('تم حذف القسم بنجاح'),
                      backgroundColor: RevolutionaryColors.successGlow,
                    ),
                  );
                }
              } catch (e) {
                if (mounted) {
                  scaffoldMessenger.showSnackBar(
                    SnackBar(
                      content: Text('خطأ في حذف القسم: $e'),
                      backgroundColor: RevolutionaryColors.errorCoral,
                    ),
                  );
                }
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: RevolutionaryColors.errorCoral,
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  void _deletePosition(Position position) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف المنصب "${position.title}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              // Store context references before async operations
              final navigator = Navigator.of(context);
              final scaffoldMessenger = ScaffoldMessenger.of(context);

              navigator.pop();
              try {
                await _positionService.deletePosition(position.id!);
                await _loadData();

                if (mounted) {
                  scaffoldMessenger.showSnackBar(
                    const SnackBar(
                      content: Text('تم حذف المنصب بنجاح'),
                      backgroundColor: RevolutionaryColors.successGlow,
                    ),
                  );
                }
              } catch (e) {
                if (mounted) {
                  scaffoldMessenger.showSnackBar(
                    SnackBar(
                      content: Text('خطأ في حذف المنصب: $e'),
                      backgroundColor: RevolutionaryColors.errorCoral,
                    ),
                  );
                }
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: RevolutionaryColors.errorCoral,
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  void _createDefaults() async {
    try {
      await Future.wait([
        _departmentService.createDefaultDepartments(),
        _positionService.createDefaultPositions(),
      ]);

      await _loadData();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إنشاء البيانات الافتراضية بنجاح'),
            backgroundColor: RevolutionaryColors.successGlow,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إنشاء البيانات الافتراضية: $e'),
            backgroundColor: RevolutionaryColors.errorCoral,
          ),
        );
      }
    }
  }

  void _exportData() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تصدير البيانات - قيد التطوير'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  void _viewDepartmentPositions(Department department) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('عرض مناصب القسم: ${department.name}'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  void _viewDepartmentEmployees(Department department) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('عرض موظفي القسم: ${department.name}'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  void _viewPositionEmployees(Position position) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('عرض موظفي المنصب: ${position.title}'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
