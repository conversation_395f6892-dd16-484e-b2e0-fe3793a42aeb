/// خدمة إدارة مكونات الراتب
/// توفر وظائف شاملة لإدارة مكونات الرواتب والبدلات والاستقطاعات
library;

import '../database/database_helper.dart';
import '../models/hr_models.dart';
import '../constants/app_constants.dart';
import '../services/logging_service.dart';
import '../services/audit_service.dart';
import '../exceptions/validation_exception.dart';

class SalaryComponentsService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  /// الحصول على جميع مكونات الراتب
  Future<List<SalaryComponent>> getAllSalaryComponents({
    bool activeOnly = false,
    String? type, // 'allowance', 'deduction', 'bonus'
    String? searchQuery,
  }) async {
    try {
      final db = await _databaseHelper.database;

      String whereClause = '1=1';
      List<dynamic> whereArgs = [];

      if (activeOnly) {
        whereClause += ' AND is_active = ?';
        whereArgs.add(1);
      }

      if (type != null && type.isNotEmpty) {
        whereClause += ' AND type = ?';
        whereArgs.add(type);
      }

      if (searchQuery != null && searchQuery.isNotEmpty) {
        whereClause += ' AND (name LIKE ? OR description LIKE ?)';
        whereArgs.add('%$searchQuery%');
        whereArgs.add('%$searchQuery%');
      }

      final result = await db.query(
        AppConstants.salaryComponentsTable,
        where: whereClause,
        whereArgs: whereArgs.isNotEmpty ? whereArgs : null,
        orderBy: 'type ASC, name ASC',
      );

      return result.map((map) => SalaryComponent.fromMap(map)).toList();
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب مكونات الراتب',
        category: 'SalaryComponentsService',
        data: {'error': e.toString()},
      );
      return [];
    }
  }

  /// الحصول على مكون راتب بالمعرف
  Future<SalaryComponent?> getSalaryComponentById(int id) async {
    try {
      final db = await _databaseHelper.database;

      final result = await db.query(
        AppConstants.salaryComponentsTable,
        where: 'id = ?',
        whereArgs: [id],
      );

      if (result.isNotEmpty) {
        return SalaryComponent.fromMap(result.first);
      }
      return null;
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب مكون الراتب',
        category: 'SalaryComponentsService',
        data: {'error': e.toString(), 'id': id},
      );
      return null;
    }
  }

  /// الحصول على مكون راتب بالرمز
  Future<SalaryComponent?> getSalaryComponentByCode(String code) async {
    try {
      final db = await _databaseHelper.database;

      final result = await db.query(
        AppConstants.salaryComponentsTable,
        where: 'code = ?',
        whereArgs: [code.toUpperCase()],
      );

      if (result.isNotEmpty) {
        return SalaryComponent.fromMap(result.first);
      }
      return null;
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب مكون الراتب بالرمز',
        category: 'SalaryComponentsService',
        data: {'error': e.toString(), 'code': code},
      );
      return null;
    }
  }

  /// إنشاء مكون راتب جديد
  Future<SalaryComponent> createSalaryComponent(
    SalaryComponent component,
  ) async {
    try {
      // التحقق من صحة البيانات
      await _validateSalaryComponent(component);

      final db = await _databaseHelper.database;
      final now = DateTime.now();
      final componentData = component.copyWith(createdAt: now, updatedAt: now);

      final id = await db.insert(
        AppConstants.salaryComponentsTable,
        componentData.toMap(),
      );

      final newComponent = componentData.copyWith(id: id);

      // تسجيل في سجل المراجعة
      await AuditService.log(
        action: 'CREATE',
        entityType: 'SalaryComponent',
        entityId: id,
        description: 'إنشاء مكون راتب جديد: ${component.name}',
        newValues: newComponent.toMap(),
      );

      LoggingService.info(
        'تم إنشاء مكون راتب جديد',
        category: 'SalaryComponentsService',
        data: {
          'componentId': id,
          'name': component.name,
          'type': component.type,
        },
      );

      return newComponent;
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء مكون الراتب',
        category: 'SalaryComponentsService',
        data: {'error': e.toString()},
      );
      rethrow;
    }
  }

  /// تحديث مكون راتب
  Future<SalaryComponent> updateSalaryComponent(
    SalaryComponent component,
  ) async {
    try {
      if (component.id == null) {
        throw ValidationException('معرف مكون الراتب مطلوب للتحديث');
      }

      // التحقق من وجود المكون
      final existingComponent = await getSalaryComponentById(component.id!);
      if (existingComponent == null) {
        throw ValidationException('مكون الراتب غير موجود');
      }

      // التحقق من صحة البيانات
      await _validateSalaryComponent(component);

      final db = await _databaseHelper.database;
      final now = DateTime.now();
      final componentData = component.copyWith(updatedAt: now);

      await db.update(
        AppConstants.salaryComponentsTable,
        componentData.toMap(),
        where: 'id = ?',
        whereArgs: [component.id],
      );

      // تسجيل في سجل المراجعة
      await AuditService.log(
        action: 'UPDATE',
        entityType: 'SalaryComponent',
        entityId: component.id!,
        description: 'تحديث مكون الراتب: ${component.name}',
        oldValues: existingComponent.toMap(),
        newValues: componentData.toMap(),
      );

      LoggingService.info(
        'تم تحديث مكون الراتب',
        category: 'SalaryComponentsService',
        data: {'componentId': component.id, 'name': component.name},
      );

      return componentData;
    } catch (e) {
      LoggingService.error(
        'خطأ في تحديث مكون الراتب',
        category: 'SalaryComponentsService',
        data: {'error': e.toString()},
      );
      rethrow;
    }
  }

  /// حذف مكون راتب
  Future<void> deleteSalaryComponent(int id) async {
    try {
      // التحقق من وجود المكون
      final component = await getSalaryComponentById(id);
      if (component == null) {
        throw ValidationException('مكون الراتب غير موجود');
      }

      // التحقق من عدم استخدام المكون في كشوف الرواتب
      final usageDetails = await getComponentUsageDetails(id);
      if (usageDetails['isUsed'] as bool) {
        final usageList =
            usageDetails['usageDetails'] as List<Map<String, dynamic>>;
        final usageDescriptions = usageList
            .map((usage) => usage['description'] as String)
            .join('، ');

        throw ValidationException(
          'لا يمكن حذف مكون الراتب "${component.name}" لأنه مستخدم في النظام:\n$usageDescriptions',
        );
      }

      final db = await _databaseHelper.database;

      await db.delete(
        AppConstants.salaryComponentsTable,
        where: 'id = ?',
        whereArgs: [id],
      );

      // تسجيل في سجل المراجعة
      await AuditService.log(
        action: 'DELETE',
        entityType: 'SalaryComponent',
        entityId: id,
        description: 'حذف مكون الراتب: ${component.name}',
        oldValues: component.toMap(),
      );

      LoggingService.info(
        'تم حذف مكون الراتب',
        category: 'SalaryComponentsService',
        data: {'componentId': id, 'name': component.name},
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في حذف مكون الراتب',
        category: 'SalaryComponentsService',
        data: {'error': e.toString(), 'id': id},
      );
      rethrow;
    }
  }

  /// تفعيل/إلغاء تفعيل مكون راتب
  Future<void> toggleComponentStatus(int id) async {
    try {
      final component = await getSalaryComponentById(id);
      if (component == null) {
        throw ValidationException('مكون الراتب غير موجود');
      }

      final updatedComponent = component.copyWith(
        isActive: !component.isActive,
        updatedAt: DateTime.now(),
      );

      await updateSalaryComponent(updatedComponent);

      LoggingService.info(
        'تم تغيير حالة مكون الراتب',
        category: 'SalaryComponentsService',
        data: {
          'componentId': id,
          'newStatus': updatedComponent.isActive ? 'active' : 'inactive',
        },
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في تغيير حالة مكون الراتب',
        category: 'SalaryComponentsService',
        data: {'error': e.toString(), 'id': id},
      );
      rethrow;
    }
  }

  /// إنشاء مكونات راتب افتراضية
  Future<void> createDefaultSalaryComponents() async {
    try {
      final existingComponents = await getAllSalaryComponents();
      if (existingComponents.isNotEmpty) {
        LoggingService.info(
          'مكونات الراتب موجودة مسبقاً',
          category: 'SalaryComponentsService',
        );
        return;
      }

      final defaultComponents = [
        // البدلات
        SalaryComponent(
          name: 'بدل نقل',
          code: 'TRANS_ALLOW',
          type: 'allowance',
          description: 'بدل المواصلات والنقل',
          defaultAmount: 50000,
          isPercentage: false,
          isTaxable: true,
          isActive: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        SalaryComponent(
          name: 'بدل طعام',
          code: 'MEAL_ALLOW',
          type: 'allowance',
          description: 'بدل الطعام اليومي',
          defaultAmount: 30000,
          isPercentage: false,
          isTaxable: true,
          isActive: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        SalaryComponent(
          name: 'بدل سكن',
          code: 'HOUSE_ALLOW',
          type: 'allowance',
          description: 'بدل السكن والإقامة',
          defaultAmount: 100000,
          isPercentage: false,
          isTaxable: true,
          isActive: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        // الحوافز
        SalaryComponent(
          name: 'حافز أداء',
          code: 'PERF_BONUS',
          type: 'bonus',
          description: 'حافز الأداء المتميز',
          defaultAmount: 10,
          isPercentage: true,
          isTaxable: true,
          isActive: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        // الاستقطاعات
        SalaryComponent(
          name: 'ضريبة الدخل',
          code: 'INCOME_TAX',
          type: 'deduction',
          description: 'ضريبة الدخل حسب القانون السوري',
          defaultAmount: 5,
          isPercentage: true,
          isTaxable: false,
          isActive: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        SalaryComponent(
          name: 'التأمينات الاجتماعية',
          code: 'SOC_INS',
          type: 'deduction',
          description: 'اشتراك التأمينات الاجتماعية',
          defaultAmount: 7,
          isPercentage: true,
          isTaxable: false,
          isActive: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
      ];

      for (final component in defaultComponents) {
        await createSalaryComponent(component);
      }

      LoggingService.info(
        'تم إنشاء ${defaultComponents.length} مكون راتب افتراضي',
        category: 'SalaryComponentsService',
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء مكونات الراتب الافتراضية',
        category: 'SalaryComponentsService',
        data: {'error': e.toString()},
      );
      rethrow;
    }
  }

  /// دوال التحقق من صحة البيانات

  Future<void> _validateSalaryComponent(SalaryComponent component) async {
    if (component.name.trim().isEmpty) {
      throw ValidationException('اسم مكون الراتب مطلوب');
    }

    if (component.code.trim().isEmpty) {
      throw ValidationException('رمز مكون الراتب مطلوب');
    }

    if (!['allowance', 'deduction', 'bonus'].contains(component.type)) {
      throw ValidationException('نوع مكون الراتب غير صحيح');
    }

    if (component.defaultAmount < 0) {
      throw ValidationException('المبلغ الافتراضي لا يمكن أن يكون سالباً');
    }

    if (component.isPercentage && component.defaultAmount > 100) {
      throw ValidationException('النسبة المئوية لا يمكن أن تزيد عن 100%');
    }

    // التحقق من عدم تكرار الرمز
    final existingComponent = await getSalaryComponentByCode(component.code);
    if (existingComponent != null && existingComponent.id != component.id) {
      throw ValidationException('رمز مكون الراتب موجود مسبقاً');
    }
  }

  /// الحصول على تفاصيل استخدام مكون الراتب
  /// يعيد معلومات مفصلة عن أماكن استخدام المكون
  Future<Map<String, dynamic>> getComponentUsageDetails(int componentId) async {
    try {
      final db = await _databaseHelper.database;
      final component = await getSalaryComponentById(componentId);

      if (component == null) {
        return {'isUsed': false, 'usageDetails': [], 'totalUsages': 0};
      }

      final List<Map<String, dynamic>> usageDetails = [];

      // فحص استخدام في تفاصيل الرواتب
      final salaryDetailsCount = await db.rawQuery(
        '''
        SELECT COUNT(*) as count, COUNT(DISTINCT employee_id) as employees
        FROM ${AppConstants.salaryDetailsTable}
        WHERE component_name = ? AND is_active = 1
      ''',
        [component.name],
      );

      if (salaryDetailsCount.isNotEmpty &&
          salaryDetailsCount.first['count'] as int > 0) {
        usageDetails.add({
          'table': 'salary_details',
          'tableName': 'تفاصيل الرواتب',
          'count': salaryDetailsCount.first['count'],
          'employees': salaryDetailsCount.first['employees'],
          'description':
              'مستخدم في ${salaryDetailsCount.first['employees']} موظف',
        });
      }

      // فحص استخدام في كشوف الرواتب المعتمدة
      final payrollCount = await db.rawQuery(
        '''
        SELECT COUNT(DISTINCT p.id) as payrolls, COUNT(DISTINCT pd.employee_id) as employees
        FROM ${AppConstants.payrollDetailsTable} pd
        INNER JOIN ${AppConstants.payrollTable} p ON pd.payroll_id = p.id
        WHERE p.status IN ('confirmed', 'paid')
        AND (
          (? = 'allowance' AND pd.allowances > 0) OR
          (? = 'deduction' AND pd.deductions > 0) OR
          (? = 'bonus' AND pd.bonus_amount > 0)
        )
      ''',
        [component.type, component.type, component.type],
      );

      if (payrollCount.isNotEmpty &&
          payrollCount.first['payrolls'] as int > 0) {
        usageDetails.add({
          'table': 'payroll_details',
          'tableName': 'كشوف الرواتب المعتمدة',
          'count': payrollCount.first['payrolls'],
          'employees': payrollCount.first['employees'],
          'description':
              'مستخدم في ${payrollCount.first['payrolls']} كشف راتب معتمد',
        });
      }

      // فحص استخدام في قوالب الرواتب
      final templateCount = await db.rawQuery(
        '''
        SELECT COUNT(*) as count
        FROM ${AppConstants.salaryTemplatesTable}
        WHERE components LIKE ? AND is_active = 1
      ''',
        ['%"code":"${component.code}"%'],
      );

      if (templateCount.isNotEmpty && templateCount.first['count'] as int > 0) {
        usageDetails.add({
          'table': 'salary_templates',
          'tableName': 'قوالب الرواتب',
          'count': templateCount.first['count'],
          'description': 'مستخدم في ${templateCount.first['count']} قالب راتب',
        });
      }

      return {
        'isUsed': usageDetails.isNotEmpty,
        'usageDetails': usageDetails,
        'totalUsages': usageDetails.fold<int>(
          0,
          (sum, detail) => sum + (detail['count'] as int),
        ),
        'componentName': component.name,
        'componentCode': component.code,
        'componentType': component.type,
      };
    } catch (e) {
      LoggingService.error(
        'خطأ في الحصول على تفاصيل استخدام مكون الراتب',
        category: 'SalaryComponentsService',
        data: {'componentId': componentId, 'error': e.toString()},
      );
      return {
        'isUsed': true, // في حالة الخطأ، نفترض أنه مستخدم
        'usageDetails': [],
        'totalUsages': 0,
        'error': e.toString(),
      };
    }
  }
}
