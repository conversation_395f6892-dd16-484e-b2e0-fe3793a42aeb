/// نماذج API للتكامل الخارجي
/// يوفر جميع النماذج المطلوبة لإدارة API والمصادقة والتوثيق
library;

/// نموذج مفتاح API
class ApiKey {
  final int? id;
  final String keyName;
  final String keyValue;
  final String keySecret;
  final int userId;
  final String userName;
  final List<String> permissions;
  final List<String> allowedEndpoints;
  final String status; // active, inactive, revoked
  final DateTime? expiresAt;
  final int requestCount;
  final int requestLimit; // -1 for unlimited
  final String? ipWhitelist;
  final Map<String, dynamic> metadata;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? lastUsedAt;

  const ApiKey({
    this.id,
    required this.keyName,
    required this.keyValue,
    required this.keySecret,
    required this.userId,
    required this.userName,
    required this.permissions,
    required this.allowedEndpoints,
    required this.status,
    this.expiresAt,
    required this.requestCount,
    required this.requestLimit,
    this.ipWhitelist,
    required this.metadata,
    required this.createdAt,
    required this.updatedAt,
    this.lastUsedAt,
  });

  factory ApiKey.fromMap(Map<String, dynamic> map) {
    return ApiKey(
      id: map['id'] as int?,
      keyName: map['key_name'] as String,
      keyValue: map['key_value'] as String,
      keySecret: map['key_secret'] as String,
      userId: map['user_id'] as int,
      userName: map['user_name'] as String,
      permissions: List<String>.from(map['permissions'] ?? []),
      allowedEndpoints: List<String>.from(map['allowed_endpoints'] ?? []),
      status: map['status'] as String,
      expiresAt: map['expires_at'] != null ? DateTime.parse(map['expires_at'] as String) : null,
      requestCount: map['request_count'] as int,
      requestLimit: map['request_limit'] as int,
      ipWhitelist: map['ip_whitelist'] as String?,
      metadata: Map<String, dynamic>.from(map['metadata'] ?? {}),
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
      lastUsedAt: map['last_used_at'] != null ? DateTime.parse(map['last_used_at'] as String) : null,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'key_name': keyName,
      'key_value': keyValue,
      'key_secret': keySecret,
      'user_id': userId,
      'user_name': userName,
      'permissions': permissions,
      'allowed_endpoints': allowedEndpoints,
      'status': status,
      'expires_at': expiresAt?.toIso8601String(),
      'request_count': requestCount,
      'request_limit': requestLimit,
      'ip_whitelist': ipWhitelist,
      'metadata': metadata,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'last_used_at': lastUsedAt?.toIso8601String(),
    };
  }

  ApiKey copyWith({
    int? id,
    String? keyName,
    String? keyValue,
    String? keySecret,
    int? userId,
    String? userName,
    List<String>? permissions,
    List<String>? allowedEndpoints,
    String? status,
    DateTime? expiresAt,
    int? requestCount,
    int? requestLimit,
    String? ipWhitelist,
    Map<String, dynamic>? metadata,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? lastUsedAt,
  }) {
    return ApiKey(
      id: id ?? this.id,
      keyName: keyName ?? this.keyName,
      keyValue: keyValue ?? this.keyValue,
      keySecret: keySecret ?? this.keySecret,
      userId: userId ?? this.userId,
      userName: userName ?? this.userName,
      permissions: permissions ?? this.permissions,
      allowedEndpoints: allowedEndpoints ?? this.allowedEndpoints,
      status: status ?? this.status,
      expiresAt: expiresAt ?? this.expiresAt,
      requestCount: requestCount ?? this.requestCount,
      requestLimit: requestLimit ?? this.requestLimit,
      ipWhitelist: ipWhitelist ?? this.ipWhitelist,
      metadata: metadata ?? this.metadata,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      lastUsedAt: lastUsedAt ?? this.lastUsedAt,
    );
  }
}

/// نموذج طلب API
class ApiRequest {
  final int? id;
  final String apiKeyId;
  final String endpoint;
  final String method;
  final Map<String, dynamic> headers;
  final Map<String, dynamic> queryParams;
  final Map<String, dynamic> body;
  final String ipAddress;
  final String userAgent;
  final int responseStatus;
  final String? responseBody;
  final int responseTime; // milliseconds
  final DateTime requestTime;
  final bool isSuccessful;
  final String? errorMessage;

  const ApiRequest({
    this.id,
    required this.apiKeyId,
    required this.endpoint,
    required this.method,
    required this.headers,
    required this.queryParams,
    required this.body,
    required this.ipAddress,
    required this.userAgent,
    required this.responseStatus,
    this.responseBody,
    required this.responseTime,
    required this.requestTime,
    required this.isSuccessful,
    this.errorMessage,
  });

  factory ApiRequest.fromMap(Map<String, dynamic> map) {
    return ApiRequest(
      id: map['id'] as int?,
      apiKeyId: map['api_key_id'] as String,
      endpoint: map['endpoint'] as String,
      method: map['method'] as String,
      headers: Map<String, dynamic>.from(map['headers'] ?? {}),
      queryParams: Map<String, dynamic>.from(map['query_params'] ?? {}),
      body: Map<String, dynamic>.from(map['body'] ?? {}),
      ipAddress: map['ip_address'] as String,
      userAgent: map['user_agent'] as String,
      responseStatus: map['response_status'] as int,
      responseBody: map['response_body'] as String?,
      responseTime: map['response_time'] as int,
      requestTime: DateTime.parse(map['request_time'] as String),
      isSuccessful: (map['is_successful'] as int?) == 1,
      errorMessage: map['error_message'] as String?,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'api_key_id': apiKeyId,
      'endpoint': endpoint,
      'method': method,
      'headers': headers,
      'query_params': queryParams,
      'body': body,
      'ip_address': ipAddress,
      'user_agent': userAgent,
      'response_status': responseStatus,
      'response_body': responseBody,
      'response_time': responseTime,
      'request_time': requestTime.toIso8601String(),
      'is_successful': isSuccessful ? 1 : 0,
      'error_message': errorMessage,
    };
  }
}

/// نموذج استجابة API
class ApiResponse<T> {
  final bool success;
  final String message;
  final T? data;
  final Map<String, dynamic>? errors;
  final Map<String, dynamic>? meta;
  final int statusCode;

  const ApiResponse({
    required this.success,
    required this.message,
    this.data,
    this.errors,
    this.meta,
    required this.statusCode,
  });

  factory ApiResponse.success({
    required String message,
    T? data,
    Map<String, dynamic>? meta,
    int statusCode = 200,
  }) {
    return ApiResponse<T>(
      success: true,
      message: message,
      data: data,
      meta: meta,
      statusCode: statusCode,
    );
  }

  factory ApiResponse.error({
    required String message,
    Map<String, dynamic>? errors,
    int statusCode = 400,
  }) {
    return ApiResponse<T>(
      success: false,
      message: message,
      errors: errors,
      statusCode: statusCode,
    );
  }

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{
      'success': success,
      'message': message,
      'status_code': statusCode,
    };

    if (data != null) {
      json['data'] = data;
    }

    if (errors != null) {
      json['errors'] = errors;
    }

    if (meta != null) {
      json['meta'] = meta;
    }

    return json;
  }
}

/// نموذج نقطة النهاية API
class ApiEndpoint {
  final String path;
  final String method;
  final String description;
  final List<String> requiredPermissions;
  final Map<String, dynamic> parameters;
  final Map<String, dynamic> responseSchema;
  final bool isPublic;
  final int rateLimitPerMinute;

  const ApiEndpoint({
    required this.path,
    required this.method,
    required this.description,
    required this.requiredPermissions,
    required this.parameters,
    required this.responseSchema,
    required this.isPublic,
    required this.rateLimitPerMinute,
  });
}

/// حالات مفتاح API
enum ApiKeyStatus {
  active('active', 'نشط'),
  inactive('inactive', 'غير نشط'),
  revoked('revoked', 'ملغي'),
  expired('expired', 'منتهي الصلاحية');

  const ApiKeyStatus(this.value, this.displayName);
  final String value;
  final String displayName;
}

/// صلاحيات API
enum ApiPermission {
  readEmployees('read:employees', 'قراءة بيانات الموظفين'),
  writeEmployees('write:employees', 'كتابة بيانات الموظفين'),
  readPayroll('read:payroll', 'قراءة بيانات الرواتب'),
  writePayroll('write:payroll', 'كتابة بيانات الرواتب'),
  readReports('read:reports', 'قراءة التقارير'),
  writeReports('write:reports', 'كتابة التقارير'),
  readAttendance('read:attendance', 'قراءة بيانات الحضور'),
  writeAttendance('write:attendance', 'كتابة بيانات الحضور'),
  admin('admin', 'صلاحيات إدارية كاملة');

  const ApiPermission(this.value, this.displayName);
  final String value;
  final String displayName;
}

/// طرق HTTP
enum HttpMethod {
  get('GET'),
  post('POST'),
  put('PUT'),
  patch('PATCH'),
  delete('DELETE');

  const HttpMethod(this.value);
  final String value;
}
