/// حوار إضافة برنامج تدريب جديد
/// يوفر واجهة لإدخال تفاصيل برنامج التدريب
library;

import 'package:flutter/material.dart';
import '../models/hr_models.dart';
import '../constants/revolutionary_design_colors.dart';

class AddTrainingProgramDialog extends StatefulWidget {
  final Function(TrainingProgram) onSave;

  const AddTrainingProgramDialog({super.key, required this.onSave});

  @override
  State<AddTrainingProgramDialog> createState() =>
      _AddTrainingProgramDialogState();
}

class _AddTrainingProgramDialogState extends State<AddTrainingProgramDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _durationController = TextEditingController();
  final _costController = TextEditingController();
  final _prerequisitesController = TextEditingController();

  String _selectedCategory = 'technical';

  final List<Map<String, String>> _categories = [
    {'value': 'technical', 'label': 'تقني'},
    {'value': 'management', 'label': 'إداري'},
    {'value': 'soft_skills', 'label': 'مهارات شخصية'},
    {'value': 'safety', 'label': 'السلامة'},
    {'value': 'language', 'label': 'لغات'},
    {'value': 'finance', 'label': 'مالي'},
    {'value': 'marketing', 'label': 'تسويق'},
    {'value': 'other', 'label': 'أخرى'},
  ];

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _durationController.dispose();
    _costController.dispose();
    _prerequisitesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Row(
        children: [
          Icon(Icons.school, color: RevolutionaryColors.damascusSky, size: 28),
          const SizedBox(width: 12),
          const Text(
            'إضافة برنامج تدريب جديد',
            style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
        ],
      ),
      content: SizedBox(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.7,
        child: Form(
          key: _formKey,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // اسم البرنامج
                TextFormField(
                  controller: _nameController,
                  decoration: InputDecoration(
                    labelText: 'اسم البرنامج *',
                    border: const OutlineInputBorder(),
                    prefixIcon: Icon(
                      Icons.title,
                      color: RevolutionaryColors.damascusSky,
                    ),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'يرجى إدخال اسم البرنامج';
                    }
                    if (value.length < 3) {
                      return 'اسم البرنامج يجب أن يكون 3 أحرف على الأقل';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // الوصف
                TextFormField(
                  controller: _descriptionController,
                  decoration: InputDecoration(
                    labelText: 'الوصف',
                    border: const OutlineInputBorder(),
                    prefixIcon: Icon(
                      Icons.description,
                      color: RevolutionaryColors.damascusSky,
                    ),
                  ),
                  maxLines: 3,
                ),
                const SizedBox(height: 16),

                // الفئة
                DropdownButtonFormField<String>(
                  value: _selectedCategory,
                  decoration: InputDecoration(
                    labelText: 'الفئة',
                    border: const OutlineInputBorder(),
                    prefixIcon: Icon(
                      Icons.category,
                      color: RevolutionaryColors.damascusSky,
                    ),
                  ),
                  items: _categories.map((category) {
                    return DropdownMenuItem<String>(
                      value: category['value'],
                      child: Text(category['label']!),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedCategory = value!;
                    });
                  },
                ),
                const SizedBox(height: 16),

                // المدة والتكلفة
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        controller: _durationController,
                        decoration: InputDecoration(
                          labelText: 'المدة (ساعة) *',
                          border: const OutlineInputBorder(),
                          prefixIcon: Icon(
                            Icons.access_time,
                            color: RevolutionaryColors.damascusSky,
                          ),
                        ),
                        keyboardType: TextInputType.number,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'يرجى إدخال المدة';
                          }
                          if (int.tryParse(value) == null) {
                            return 'يرجى إدخال رقم صحيح';
                          }
                          final duration = int.parse(value);
                          if (duration <= 0) {
                            return 'المدة يجب أن تكون أكبر من صفر';
                          }
                          return null;
                        },
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: TextFormField(
                        controller: _costController,
                        decoration: InputDecoration(
                          labelText: 'التكلفة (ل.س)',
                          border: const OutlineInputBorder(),
                          prefixIcon: Icon(
                            Icons.attach_money,
                            color: RevolutionaryColors.damascusSky,
                          ),
                        ),
                        keyboardType: TextInputType.number,
                        validator: (value) {
                          if (value != null && value.isNotEmpty) {
                            if (double.tryParse(value) == null) {
                              return 'يرجى إدخال رقم صحيح';
                            }
                            final cost = double.parse(value);
                            if (cost < 0) {
                              return 'التكلفة لا يمكن أن تكون سالبة';
                            }
                          }
                          return null;
                        },
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                // المتطلبات المسبقة
                TextFormField(
                  controller: _prerequisitesController,
                  decoration: InputDecoration(
                    labelText: 'المتطلبات المسبقة',
                    border: const OutlineInputBorder(),
                    prefixIcon: Icon(
                      Icons.checklist,
                      color: RevolutionaryColors.damascusSky,
                    ),
                    hintText: 'اذكر المتطلبات المسبقة للبرنامج (اختياري)',
                  ),
                  maxLines: 2,
                ),
                const SizedBox(height: 24),

                // معلومات إضافية
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: RevolutionaryColors.damascusSky.withValues(
                      alpha: 0.1,
                    ),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: RevolutionaryColors.damascusSky.withValues(
                        alpha: 0.3,
                      ),
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.info_outline,
                        color: RevolutionaryColors.damascusSky,
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      const Expanded(
                        child: Text(
                          'سيتم إنشاء البرنامج بحالة نشطة ويمكن تعديل التفاصيل الإضافية لاحقاً',
                          style: TextStyle(fontSize: 12, color: Colors.black87),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text(
            'إلغاء',
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
          ),
        ),
        ElevatedButton(
          onPressed: _saveProgram,
          style: ElevatedButton.styleFrom(
            backgroundColor: RevolutionaryColors.successGlow,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(Icons.save, size: 20),
              const SizedBox(width: 8),
              const Text(
                'إضافة البرنامج',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
            ],
          ),
        ),
      ],
    );
  }

  void _saveProgram() {
    if (_formKey.currentState!.validate()) {
      final program = TrainingProgram(
        name: _nameController.text.trim(),
        description: _descriptionController.text.trim().isEmpty
            ? null
            : _descriptionController.text.trim(),
        category: _selectedCategory,
        durationHours: int.parse(_durationController.text),
        cost: _costController.text.isEmpty
            ? 0
            : double.parse(_costController.text),
        prerequisites: _prerequisitesController.text.trim().isEmpty
            ? null
            : _prerequisitesController.text.trim(),
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      widget.onSave(program);
      Navigator.of(context).pop();
    }
  }
}
