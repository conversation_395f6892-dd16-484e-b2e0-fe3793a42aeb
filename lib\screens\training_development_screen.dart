/// شاشة التدريب وتطوير الموظفين
/// توفر واجهة شاملة لإدارة برامج التدريب والتطوير المهني
library;

import 'package:flutter/material.dart';
import '../models/hr_models.dart';
import '../services/training_service.dart';
import '../services/logging_service.dart';
import '../widgets/loading_widget.dart';
import '../widgets/custom_error_widget.dart' as custom_widgets;
import '../widgets/add_training_program_dialog.dart';
import '../widgets/add_training_session_dialog.dart';
import '../widgets/add_skill_dialog.dart';
import '../constants/revolutionary_design_colors.dart';

class TrainingDevelopmentScreen extends StatefulWidget {
  const TrainingDevelopmentScreen({super.key});

  @override
  State<TrainingDevelopmentScreen> createState() =>
      _TrainingDevelopmentScreenState();
}

class _TrainingDevelopmentScreenState extends State<TrainingDevelopmentScreen>
    with TickerProviderStateMixin {
  final TrainingService _trainingService = TrainingService();

  late TabController _tabController;
  List<TrainingProgram> _programs = [];
  List<TrainingEnrollment> _enrollments = [];
  // البيانات المحملة من الخدمة
  Map<String, dynamic> _statistics = {};

  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 6, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final results = await Future.wait([
        _trainingService.getAllTrainingPrograms(),
        _trainingService.getTrainingStatistics(),
      ]);

      setState(() {
        _programs = results[0] as List<TrainingProgram>;
        _statistics = results[1] as Map<String, dynamic>;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });

      LoggingService.error(
        'خطأ في تحميل بيانات التدريب',
        category: 'TrainingDevelopmentScreen',
        data: {'error': e.toString()},
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('التدريب وتطوير الموظفين'),
        backgroundColor: RevolutionaryColors.damascusSky,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadData,
            tooltip: 'تحديث',
          ),
          PopupMenuButton<String>(
            icon: const Icon(Icons.more_vert),
            onSelected: (value) {
              switch (value) {
                case 'add_program':
                  _showAddProgramDialog();
                  break;
                case 'add_session':
                  _showAddSessionDialog();
                  break;
                case 'export':
                  _exportTrainingData();
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'add_program',
                child: Text('إضافة برنامج تدريب'),
              ),
              const PopupMenuItem(
                value: 'add_session',
                child: Text('إضافة جلسة تدريب'),
              ),
              const PopupMenuItem(
                value: 'export',
                child: Text('تصدير البيانات'),
              ),
            ],
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          isScrollable: true,
          tabs: const [
            Tab(icon: Icon(Icons.dashboard), text: 'نظرة عامة'),
            Tab(icon: Icon(Icons.school), text: 'البرامج'),
            Tab(icon: Icon(Icons.event), text: 'الجلسات'),
            Tab(icon: Icon(Icons.people), text: 'التسجيلات'),
            Tab(icon: Icon(Icons.star), text: 'المهارات'),
            Tab(icon: Icon(Icons.trending_up), text: 'خطط التطوير'),
          ],
        ),
      ),
      body: _buildBody(),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const LoadingWidget();
    }

    if (_error != null) {
      return custom_widgets.ErrorWidget(message: _error!, onRetry: _loadData);
    }

    return TabBarView(
      controller: _tabController,
      children: [
        _buildOverviewTab(),
        _buildProgramsTab(),
        _buildSessionsTab(),
        _buildEnrollmentsTab(),
        _buildSkillsTab(),
        _buildDevelopmentPlansTab(),
      ],
    );
  }

  Widget _buildOverviewTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildStatisticsCards(),
          const SizedBox(height: 24),
          _buildQuickActions(),
          const SizedBox(height: 24),
          _buildRecentActivity(),
        ],
      ),
    );
  }

  Widget _buildStatisticsCards() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'إحصائيات التدريب والتطوير',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
          childAspectRatio: 1.5,
          children: [
            _buildStatCard(
              'برامج التدريب',
              '${_statistics['totalPrograms'] ?? 0}',
              Icons.school,
              RevolutionaryColors.damascusSky,
            ),
            _buildStatCard(
              'الجلسات',
              '${_statistics['totalSessions'] ?? 0}',
              Icons.event,
              RevolutionaryColors.successGlow,
            ),
            _buildStatCard(
              'التسجيلات',
              '${_statistics['totalEnrollments'] ?? 0}',
              Icons.people,
              RevolutionaryColors.infoTurquoise,
            ),
            _buildStatCard(
              'معدل الإكمال',
              '${_statistics['completionRate'] ?? '0.0'}%',
              Icons.check_circle,
              RevolutionaryColors.warningAmber,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: 32, color: color),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: const TextStyle(fontSize: 12),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'الإجراءات السريعة',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 12,
          runSpacing: 12,
          children: [
            _buildActionCard(
              'إضافة برنامج',
              Icons.add_circle,
              RevolutionaryColors.successGlow,
              _showAddProgramDialog,
            ),
            _buildActionCard(
              'إضافة جلسة',
              Icons.event_available,
              RevolutionaryColors.infoTurquoise,
              _showAddSessionDialog,
            ),
            _buildActionCard(
              'تسجيل موظف',
              Icons.person_add,
              RevolutionaryColors.warningAmber,
              _showEnrollEmployeeDialog,
            ),
            _buildActionCard(
              'إضافة مهارة',
              Icons.star_border,
              RevolutionaryColors.damascusSky,
              _showAddSkillDialog,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildActionCard(
    String title,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        width: (MediaQuery.of(context).size.width - 56) / 2,
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: color.withValues(alpha: 0.3)),
        ),
        child: Row(
          children: [
            Icon(icon, color: color, size: 24),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                title,
                style: TextStyle(fontWeight: FontWeight.w600, color: color),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentActivity() {
    final popularPrograms =
        _statistics['popularPrograms'] as List<dynamic>? ?? [];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'أكثر البرامج شعبية',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),
        if (popularPrograms.isEmpty)
          const Center(
            child: Padding(
              padding: EdgeInsets.all(32),
              child: Text(
                'لا توجد بيانات كافية',
                style: TextStyle(color: Colors.grey),
              ),
            ),
          )
        else
          ...popularPrograms.map(
            (program) => Card(
              margin: const EdgeInsets.only(bottom: 8),
              child: ListTile(
                leading: CircleAvatar(
                  backgroundColor: RevolutionaryColors.damascusSky.withValues(
                    alpha: 0.1,
                  ),
                  child: Text(
                    '${program['enrollments']}',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      color: RevolutionaryColors.damascusSky,
                    ),
                  ),
                ),
                title: Text(program['name'] ?? 'برنامج غير معروف'),
                subtitle: Text('${program['enrollments']} تسجيل'),
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildProgramsTab() {
    return Column(
      children: [
        // شريط البحث والفلترة
        Container(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Expanded(
                child: TextField(
                  decoration: const InputDecoration(
                    hintText: 'البحث في البرامج...',
                    prefixIcon: Icon(Icons.search),
                    border: OutlineInputBorder(),
                  ),
                  onChanged: (value) {
                    _searchPrograms(value);
                  },
                ),
              ),
              const SizedBox(width: 12),
              ElevatedButton.icon(
                onPressed: _showAddProgramDialog,
                icon: const Icon(Icons.add),
                label: const Text('إضافة برنامج'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: RevolutionaryColors.successGlow,
                  foregroundColor: Colors.white,
                ),
              ),
            ],
          ),
        ),
        // قائمة البرامج
        Expanded(
          child: _programs.isEmpty
              ? const Center(
                  child: Text(
                    'لا توجد برامج تدريب',
                    style: TextStyle(color: Colors.grey),
                  ),
                )
              : ListView.builder(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  itemCount: _programs.length,
                  itemBuilder: (context, index) {
                    final program = _programs[index];
                    return _buildProgramCard(program);
                  },
                ),
        ),
      ],
    );
  }

  Widget _buildProgramCard(TrainingProgram program) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ExpansionTile(
        leading: Icon(
          _getProgramIcon(program.category ?? 'general'),
          color: RevolutionaryColors.damascusSky,
        ),
        title: Text(
          program.name,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Text(
          '${program.category ?? 'عام'} • ${program.durationHours} ساعة',
        ),
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  program.description ?? 'لا يوجد وصف',
                  style: const TextStyle(fontSize: 14),
                ),
                const SizedBox(height: 12),
                Row(
                  children: [
                    Expanded(
                      child: _buildProgramDetail(
                        'المدة',
                        '${program.durationHours} ساعة',
                      ),
                    ),
                    Expanded(
                      child: _buildProgramDetail(
                        'التكلفة',
                        '${program.cost.toStringAsFixed(0)} ل.س',
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(
                      child: _buildProgramDetail(
                        'الفئة',
                        program.category ?? 'عام',
                      ),
                    ),
                    Expanded(
                      child: _buildProgramDetail(
                        'التكلفة',
                        '${program.cost.toStringAsFixed(0)} ل.س',
                      ),
                    ),
                  ],
                ),
                if (program.prerequisites != null) ...[
                  const SizedBox(height: 8),
                  _buildProgramDetail(
                    'المتطلبات المسبقة',
                    program.prerequisites!,
                  ),
                ],
                const SizedBox(height: 16),
                Row(
                  children: [
                    ElevatedButton.icon(
                      onPressed: () => _createSessionForProgram(program),
                      icon: const Icon(Icons.add),
                      label: const Text('إنشاء جلسة'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: RevolutionaryColors.successGlow,
                        foregroundColor: Colors.white,
                      ),
                    ),
                    const SizedBox(width: 12),
                    OutlinedButton.icon(
                      onPressed: () => _editProgram(program),
                      icon: const Icon(Icons.edit),
                      label: const Text('تعديل'),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProgramDetail(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 2),
        Text(
          value,
          style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
        ),
      ],
    );
  }

  Widget _buildSessionsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSessionsHeader(),
          const SizedBox(height: 24),
          _buildSessionsList(),
        ],
      ),
    );
  }

  Widget _buildSessionsHeader() {
    return Row(
      children: [
        const Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'الجلسات التدريبية',
                style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
              ),
              Text(
                'إدارة جلسات التدريب المجدولة والمكتملة',
                style: TextStyle(fontSize: 14, color: Colors.grey),
              ),
            ],
          ),
        ),
        ElevatedButton.icon(
          onPressed: _showAddSessionDialog,
          icon: const Icon(Icons.add),
          label: const Text('إضافة جلسة'),
          style: ElevatedButton.styleFrom(
            backgroundColor: RevolutionaryColors.infoTurquoise,
          ),
        ),
      ],
    );
  }

  Widget _buildSessionsList() {
    // بيانات وهمية للجلسات
    final sessions = [
      {
        'name': 'جلسة تطوير المهارات التقنية',
        'program': 'البرمجة المتقدمة',
        'date': '2024-01-15',
        'time': '09:00 - 12:00',
        'trainer': 'أحمد محمد',
        'location': 'قاعة التدريب الأولى',
        'participants': '15/20',
        'status': 'scheduled',
      },
      {
        'name': 'جلسة إدارة الوقت',
        'program': 'المهارات الإدارية',
        'date': '2024-01-20',
        'time': '14:00 - 17:00',
        'trainer': 'فاطمة أحمد',
        'location': 'قاعة التدريب الثانية',
        'participants': '12/15',
        'status': 'ongoing',
      },
    ];

    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: sessions.length,
      itemBuilder: (context, index) {
        final session = sessions[index];
        return _buildSessionCard(session);
      },
    );
  }

  Widget _buildSessionCard(Map<String, dynamic> session) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        session['name'],
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'البرنامج: ${session['program']}',
                        style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                      ),
                    ],
                  ),
                ),
                _buildSessionStatusChip(session['status']),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildSessionDetail(
                    Icons.calendar_today,
                    'التاريخ',
                    session['date'],
                  ),
                ),
                Expanded(
                  child: _buildSessionDetail(
                    Icons.access_time,
                    'الوقت',
                    session['time'],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildSessionDetail(
                    Icons.person,
                    'المدرب',
                    session['trainer'],
                  ),
                ),
                Expanded(
                  child: _buildSessionDetail(
                    Icons.location_on,
                    'المكان',
                    session['location'],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildSessionDetail(
                    Icons.group,
                    'المشاركون',
                    session['participants'],
                  ),
                ),
                const Spacer(),
                PopupMenuButton<String>(
                  onSelected: (value) {
                    switch (value) {
                      case 'edit':
                        _editSession(session);
                        break;
                      case 'participants':
                        _viewSessionParticipants(session);
                        break;
                      case 'cancel':
                        _cancelSession(session);
                        break;
                    }
                  },
                  itemBuilder: (context) => [
                    const PopupMenuItem(value: 'edit', child: Text('تعديل')),
                    const PopupMenuItem(
                      value: 'participants',
                      child: Text('المشاركون'),
                    ),
                    const PopupMenuItem(value: 'cancel', child: Text('إلغاء')),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSessionDetail(IconData icon, String label, String value) {
    return Row(
      children: [
        Icon(icon, size: 16, color: Colors.grey[600]),
        const SizedBox(width: 8),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
            Text(
              value,
              style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildSessionStatusChip(String status) {
    Color color;
    String label;
    switch (status) {
      case 'scheduled':
        color = RevolutionaryColors.infoTurquoise;
        label = 'مجدولة';
        break;
      case 'ongoing':
        color = RevolutionaryColors.warningAmber;
        label = 'جارية';
        break;
      case 'completed':
        color = RevolutionaryColors.successGlow;
        label = 'مكتملة';
        break;
      case 'cancelled':
        color = Colors.red;
        label = 'ملغية';
        break;
      default:
        color = Colors.grey;
        label = status;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Text(
        label,
        style: TextStyle(
          color: color,
          fontSize: 12,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget _buildEnrollmentsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildEnrollmentFilters(),
          const SizedBox(height: 24),
          _buildEnrollmentsList(),
        ],
      ),
    );
  }

  Widget _buildEnrollmentFilters() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'فلاتر التسجيلات',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: DropdownButtonFormField<int>(
                    decoration: const InputDecoration(
                      labelText: 'البرنامج التدريبي',
                      border: OutlineInputBorder(),
                    ),
                    items: _programs.map((program) {
                      return DropdownMenuItem(
                        value: program.id,
                        child: Text(program.name),
                      );
                    }).toList(),
                    onChanged: (value) {
                      // تطبيق فلتر البرنامج
                      _filterEnrollmentsByProgram(value);
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: DropdownButtonFormField<String>(
                    decoration: const InputDecoration(
                      labelText: 'حالة التسجيل',
                      border: OutlineInputBorder(),
                    ),
                    items: const [
                      DropdownMenuItem(value: 'enrolled', child: Text('مسجل')),
                      DropdownMenuItem(
                        value: 'in_progress',
                        child: Text('قيد التنفيذ'),
                      ),
                      DropdownMenuItem(
                        value: 'completed',
                        child: Text('مكتمل'),
                      ),
                      DropdownMenuItem(value: 'cancelled', child: Text('ملغي')),
                    ],
                    onChanged: (value) {
                      // تطبيق فلتر الحالة
                      _filterEnrollmentsByStatus(value);
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                ElevatedButton.icon(
                  onPressed: _showEnrollEmployeeDialog,
                  icon: const Icon(Icons.person_add),
                  label: const Text('تسجيل موظف'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: RevolutionaryColors.successGlow,
                  ),
                ),
                const SizedBox(width: 16),
                ElevatedButton.icon(
                  onPressed: _exportEnrollments,
                  icon: const Icon(Icons.download),
                  label: const Text('تصدير'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: RevolutionaryColors.damascusSky,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEnrollmentsList() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'قائمة التسجيلات',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            // قائمة وهمية للتسجيلات
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: 5, // عدد وهمي
              itemBuilder: (context, index) {
                return _buildEnrollmentCard(index);
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEnrollmentCard(int index) {
    // بيانات وهمية للتسجيل
    final enrollmentData = {
      'employeeName': 'موظف ${index + 1}',
      'programName': 'برنامج تدريبي ${index + 1}',
      'status': ['enrolled', 'in_progress', 'completed'][index % 3],
      'progress': (index + 1) * 20.0,
      'enrollmentDate': DateTime.now().subtract(Duration(days: index * 10)),
    };

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: _getEnrollmentStatusColor(
            enrollmentData['status'] as String,
          ),
          child: Text(
            '${(enrollmentData['progress'] as double).toInt()}%',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 12,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        title: Text(enrollmentData['employeeName'] as String),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(enrollmentData['programName'] as String),
            const SizedBox(height: 4),
            LinearProgressIndicator(
              value: (enrollmentData['progress'] as double) / 100,
              backgroundColor: Colors.grey[300],
              valueColor: AlwaysStoppedAnimation<Color>(
                _getEnrollmentStatusColor(enrollmentData['status'] as String),
              ),
            ),
          ],
        ),
        trailing: PopupMenuButton<String>(
          onSelected: (value) {
            switch (value) {
              case 'view':
                _viewEnrollmentDetails(index);
                break;
              case 'update_progress':
                _updateEnrollmentProgress(index);
                break;
              case 'cancel':
                _cancelEnrollment(index);
                break;
            }
          },
          itemBuilder: (context) => [
            const PopupMenuItem(value: 'view', child: Text('عرض التفاصيل')),
            const PopupMenuItem(
              value: 'update_progress',
              child: Text('تحديث التقدم'),
            ),
            const PopupMenuItem(value: 'cancel', child: Text('إلغاء التسجيل')),
          ],
        ),
        onTap: () => _viewEnrollmentDetails(index),
      ),
    );
  }

  Widget _buildSkillsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSkillsHeader(),
          const SizedBox(height: 24),
          _buildSkillsMatrix(),
          const SizedBox(height: 24),
          _buildTopSkills(),
        ],
      ),
    );
  }

  Widget _buildSkillsHeader() {
    return Row(
      children: [
        const Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'مهارات الموظفين',
                style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
              ),
              Text(
                'إدارة وتتبع مهارات الموظفين وتطويرها',
                style: TextStyle(fontSize: 14, color: Colors.grey),
              ),
            ],
          ),
        ),
        ElevatedButton.icon(
          onPressed: _showAddSkillDialog,
          icon: const Icon(Icons.add),
          label: const Text('إضافة مهارة'),
          style: ElevatedButton.styleFrom(
            backgroundColor: RevolutionaryColors.damascusSky,
          ),
        ),
      ],
    );
  }

  Widget _buildSkillsMatrix() {
    final skillCategories = [
      {
        'name': 'المهارات التقنية',
        'icon': Icons.computer,
        'color': RevolutionaryColors.infoTurquoise,
        'skills': ['البرمجة', 'قواعد البيانات', 'الشبكات', 'الأمان السيبراني'],
      },
      {
        'name': 'المهارات الإدارية',
        'icon': Icons.business,
        'color': RevolutionaryColors.successGlow,
        'skills': ['إدارة المشاريع', 'القيادة', 'التخطيط', 'اتخاذ القرارات'],
      },
      {
        'name': 'مهارات التواصل',
        'icon': Icons.people,
        'color': RevolutionaryColors.warningAmber,
        'skills': [
          'العرض والتقديم',
          'التفاوض',
          'العمل الجماعي',
          'خدمة العملاء',
        ],
      },
      {
        'name': 'المهارات اللغوية',
        'icon': Icons.language,
        'color': RevolutionaryColors.damascusSky,
        'skills': ['الإنجليزية', 'الفرنسية', 'الترجمة', 'الكتابة التقنية'],
      },
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'مصفوفة المهارات',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
            childAspectRatio: 1.2,
          ),
          itemCount: skillCategories.length,
          itemBuilder: (context, index) {
            final category = skillCategories[index];
            return _buildSkillCategoryCard(category);
          },
        ),
      ],
    );
  }

  Widget _buildSkillCategoryCard(Map<String, dynamic> category) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: (category['color'] as Color).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    category['icon'] as IconData,
                    color: category['color'] as Color,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    category['name'] as String,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  ...(category['skills'] as List<String>)
                      .take(3)
                      .map(
                        (skill) => Padding(
                          padding: const EdgeInsets.symmetric(vertical: 2),
                          child: Row(
                            children: [
                              Container(
                                width: 4,
                                height: 4,
                                decoration: BoxDecoration(
                                  color: category['color'] as Color,
                                  shape: BoxShape.circle,
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  skill,
                                  style: const TextStyle(fontSize: 12),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                  if ((category['skills'] as List).length > 3)
                    Padding(
                      padding: const EdgeInsets.only(top: 4),
                      child: Text(
                        '+${(category['skills'] as List).length - 3} أخرى',
                        style: TextStyle(
                          fontSize: 10,
                          color: Colors.grey[600],
                          fontStyle: FontStyle.italic,
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTopSkills() {
    final topSkills = [
      {'name': 'البرمجة بـ Flutter', 'employees': 8, 'level': 4.2},
      {'name': 'إدارة المشاريع', 'employees': 12, 'level': 3.8},
      {'name': 'اللغة الإنجليزية', 'employees': 15, 'level': 3.5},
      {'name': 'التصميم الجرافيكي', 'employees': 6, 'level': 4.0},
      {'name': 'التسويق الرقمي', 'employees': 9, 'level': 3.7},
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'أهم المهارات في الشركة',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: topSkills.length,
          itemBuilder: (context, index) {
            final skill = topSkills[index];
            return _buildTopSkillCard(skill, index);
          },
        ),
      ],
    );
  }

  Widget _buildTopSkillCard(Map<String, dynamic> skill, int index) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: RevolutionaryColors.damascusSky.withValues(
            alpha: 0.1,
          ),
          child: Text(
            '${index + 1}',
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              color: RevolutionaryColors.damascusSky,
            ),
          ),
        ),
        title: Text(
          skill['name'] as String,
          style: const TextStyle(fontWeight: FontWeight.w600),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('${skill['employees']} موظف'),
            const SizedBox(height: 4),
            Row(
              children: [
                ...List.generate(5, (starIndex) {
                  return Icon(
                    starIndex < (skill['level'] as double).floor()
                        ? Icons.star
                        : starIndex < (skill['level'] as double)
                        ? Icons.star_half
                        : Icons.star_border,
                    size: 16,
                    color: Colors.amber,
                  );
                }),
                const SizedBox(width: 8),
                Text('${skill['level']}', style: const TextStyle(fontSize: 12)),
              ],
            ),
          ],
        ),
        trailing: PopupMenuButton<String>(
          onSelected: (value) {
            switch (value) {
              case 'view':
                _viewSkillDetails(skill);
                break;
              case 'assess':
                _assessSkill(skill);
                break;
              case 'training':
                _suggestTraining(skill);
                break;
            }
          },
          itemBuilder: (context) => [
            const PopupMenuItem(value: 'view', child: Text('عرض التفاصيل')),
            const PopupMenuItem(value: 'assess', child: Text('تقييم المهارة')),
            const PopupMenuItem(value: 'training', child: Text('اقتراح تدريب')),
          ],
        ),
      ),
    );
  }

  Widget _buildDevelopmentPlansTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildDevelopmentPlansHeader(),
          const SizedBox(height: 24),
          _buildDevelopmentPlansList(),
        ],
      ),
    );
  }

  Widget _buildDevelopmentPlansHeader() {
    return Row(
      children: [
        const Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'خطط التطوير المهني',
                style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
              ),
              Text(
                'إدارة ومتابعة خطط التطوير المهني للموظفين',
                style: TextStyle(fontSize: 14, color: Colors.grey),
              ),
            ],
          ),
        ),
        ElevatedButton.icon(
          onPressed: _showAddDevelopmentPlanDialog,
          icon: const Icon(Icons.add),
          label: const Text('إضافة خطة'),
          style: ElevatedButton.styleFrom(
            backgroundColor: RevolutionaryColors.successGlow,
          ),
        ),
      ],
    );
  }

  Widget _buildDevelopmentPlansList() {
    final developmentPlans = [
      {
        'title': 'خطة تطوير المهارات التقنية',
        'employee': 'أحمد محمد',
        'startDate': '2024-01-01',
        'targetDate': '2024-06-30',
        'progress': 65.0,
        'status': 'active',
        'goals': ['تعلم Flutter', 'إتقان قواعد البيانات', 'تطوير مهارات API'],
        'mentor': 'سارة أحمد',
      },
      {
        'title': 'خطة تطوير المهارات الإدارية',
        'employee': 'فاطمة علي',
        'startDate': '2024-02-01',
        'targetDate': '2024-08-31',
        'progress': 40.0,
        'status': 'active',
        'goals': ['إدارة الفرق', 'التخطيط الاستراتيجي', 'مهارات التواصل'],
        'mentor': 'محمد خالد',
      },
      {
        'title': 'خطة تطوير مهارات التسويق',
        'employee': 'عمر حسن',
        'startDate': '2023-10-01',
        'targetDate': '2024-03-31',
        'progress': 90.0,
        'status': 'completed',
        'goals': ['التسويق الرقمي', 'تحليل البيانات', 'إدارة الحملات'],
        'mentor': 'ليلى أحمد',
      },
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'الخطط النشطة',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: developmentPlans.length,
          itemBuilder: (context, index) {
            final plan = developmentPlans[index];
            return _buildDevelopmentPlanCard(plan);
          },
        ),
      ],
    );
  }

  Widget _buildDevelopmentPlanCard(Map<String, dynamic> plan) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        plan['title'] as String,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'الموظف: ${plan['employee']}',
                        style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                      ),
                    ],
                  ),
                ),
                _buildPlanStatusChip(plan['status'] as String),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildPlanDetail(
                    Icons.calendar_today,
                    'تاريخ البداية',
                    plan['startDate'] as String,
                  ),
                ),
                Expanded(
                  child: _buildPlanDetail(
                    Icons.event,
                    'التاريخ المستهدف',
                    plan['targetDate'] as String,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildPlanDetail(
                    Icons.person_outline,
                    'المرشد',
                    plan['mentor'] as String,
                  ),
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'التقدم',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[600],
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          Expanded(
                            child: LinearProgressIndicator(
                              value: (plan['progress'] as double) / 100,
                              backgroundColor: Colors.grey[300],
                              valueColor: AlwaysStoppedAnimation<Color>(
                                _getPlanStatusColor(plan['status'] as String),
                              ),
                            ),
                          ),
                          const SizedBox(width: 8),
                          Text(
                            '${(plan['progress'] as double).toInt()}%',
                            style: const TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            const Text(
              'الأهداف:',
              style: TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              runSpacing: 4,
              children: (plan['goals'] as List<String>).map((goal) {
                return Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: RevolutionaryColors.damascusSky.withValues(
                      alpha: 0.1,
                    ),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: RevolutionaryColors.damascusSky.withValues(
                        alpha: 0.3,
                      ),
                    ),
                  ),
                  child: Text(
                    goal,
                    style: const TextStyle(
                      fontSize: 12,
                      color: RevolutionaryColors.damascusSky,
                    ),
                  ),
                );
              }).toList(),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                ElevatedButton.icon(
                  onPressed: () => _viewPlanDetails(plan),
                  icon: const Icon(Icons.visibility, size: 16),
                  label: const Text('عرض التفاصيل'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: RevolutionaryColors.infoTurquoise,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                OutlinedButton.icon(
                  onPressed: () => _updatePlanProgress(plan),
                  icon: const Icon(Icons.update, size: 16),
                  label: const Text('تحديث التقدم'),
                ),
                const Spacer(),
                PopupMenuButton<String>(
                  onSelected: (value) {
                    switch (value) {
                      case 'edit':
                        _editPlan(plan);
                        break;
                      case 'complete':
                        _completePlan(plan);
                        break;
                      case 'cancel':
                        _cancelPlan(plan);
                        break;
                    }
                  },
                  itemBuilder: (context) => [
                    const PopupMenuItem(value: 'edit', child: Text('تعديل')),
                    const PopupMenuItem(
                      value: 'complete',
                      child: Text('إكمال'),
                    ),
                    const PopupMenuItem(value: 'cancel', child: Text('إلغاء')),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPlanDetail(IconData icon, String label, String value) {
    return Row(
      children: [
        Icon(icon, size: 16, color: Colors.grey[600]),
        const SizedBox(width: 8),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
            Text(
              value,
              style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildPlanStatusChip(String status) {
    Color color = _getPlanStatusColor(status);
    String label = _getPlanStatusLabel(status);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Text(
        label,
        style: TextStyle(
          color: color,
          fontSize: 12,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Color _getPlanStatusColor(String status) {
    switch (status) {
      case 'draft':
        return Colors.grey;
      case 'active':
        return RevolutionaryColors.infoTurquoise;
      case 'completed':
        return RevolutionaryColors.successGlow;
      case 'cancelled':
        return RevolutionaryColors.errorCoral;
      default:
        return Colors.grey;
    }
  }

  String _getPlanStatusLabel(String status) {
    switch (status) {
      case 'draft':
        return 'مسودة';
      case 'active':
        return 'نشطة';
      case 'completed':
        return 'مكتملة';
      case 'cancelled':
        return 'ملغية';
      default:
        return status;
    }
  }

  Widget? _buildFloatingActionButton() {
    switch (_tabController.index) {
      case 1: // البرامج
        return FloatingActionButton(
          onPressed: _showAddProgramDialog,
          backgroundColor: RevolutionaryColors.successGlow,
          child: const Icon(Icons.add, color: Colors.white),
        );
      case 2: // الجلسات
        return FloatingActionButton(
          onPressed: _showAddSessionDialog,
          backgroundColor: RevolutionaryColors.infoTurquoise,
          child: const Icon(Icons.event_available, color: Colors.white),
        );
      default:
        return null;
    }
  }

  // دوال مساعدة
  IconData _getProgramIcon(String category) {
    switch (category.toLowerCase()) {
      case 'technical':
        return Icons.computer;
      case 'management':
        return Icons.business;
      case 'soft_skills':
        return Icons.people;
      case 'safety':
        return Icons.security;
      case 'language':
        return Icons.language;
      default:
        return Icons.school;
    }
  }

  // دوال الأحداث
  void _showAddProgramDialog() {
    showDialog(
      context: context,
      builder: (context) => AddTrainingProgramDialog(
        onSave: (program) async {
          final scaffoldMessenger = ScaffoldMessenger.of(context);
          try {
            await _trainingService.addTrainingProgram(program);

            if (mounted) {
              scaffoldMessenger.showSnackBar(
                const SnackBar(
                  content: Text('تم إضافة برنامج التدريب بنجاح'),
                  backgroundColor: Colors.green,
                ),
              );
              _loadData(); // إعادة تحميل البيانات
            }
          } catch (e) {
            if (mounted) {
              scaffoldMessenger.showSnackBar(
                SnackBar(
                  content: Text('خطأ في إضافة البرنامج: ${e.toString()}'),
                  backgroundColor: Colors.red,
                ),
              );
            }
          }
        },
      ),
    );
  }

  void _showAddSessionDialog() {
    showDialog(
      context: context,
      builder: (context) => AddTrainingSessionDialog(
        programs: _programs,
        onSave: (session) async {
          try {
            // TODO: إضافة الجلسة عبر الخدمة
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('تم إنشاء الجلسة: ${session.sessionName}'),
                backgroundColor: RevolutionaryColors.successGlow,
              ),
            );
            _loadData(); // إعادة تحميل البيانات
          } catch (e) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('خطأ في إنشاء الجلسة: $e'),
                backgroundColor: Colors.red,
              ),
            );
          }
        },
      ),
    );
  }

  void _showAddSkillDialog() {
    // عرض حوار اختيار الموظف أولاً
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختيار الموظف'),
        content: const Text('يرجى اختيار الموظف لإضافة المهارة له'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _showSkillDialogForEmployee(1); // موظف افتراضي
            },
            child: const Text('متابعة'),
          ),
        ],
      ),
    );
  }

  void _showSkillDialogForEmployee(int employeeId) {
    showDialog(
      context: context,
      builder: (context) => AddSkillDialog(
        employeeId: employeeId,
        onSave: (skill) async {
          try {
            // TODO: إضافة المهارة عبر الخدمة
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('تم إضافة المهارة: ${skill.skillName}'),
                backgroundColor: RevolutionaryColors.successGlow,
              ),
            );
            _loadData(); // إعادة تحميل البيانات
          } catch (e) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('خطأ في إضافة المهارة: $e'),
                backgroundColor: Colors.red,
              ),
            );
          }
        },
      ),
    );
  }

  void _createSessionForProgram(TrainingProgram program) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('إنشاء جلسة للبرنامج: ${program.name}'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  void _editProgram(TrainingProgram program) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تعديل البرنامج: ${program.name}'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  void _showEnrollEmployeeDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تسجيل موظف في برنامج تدريبي'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            DropdownButtonFormField<int>(
              decoration: const InputDecoration(
                labelText: 'اختر الموظف',
                border: OutlineInputBorder(),
              ),
              items: const [
                DropdownMenuItem(value: 1, child: Text('موظف 1')),
                DropdownMenuItem(value: 2, child: Text('موظف 2')),
                DropdownMenuItem(value: 3, child: Text('موظف 3')),
              ],
              onChanged: (value) {
                // حفظ الموظف المختار
              },
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<int>(
              decoration: const InputDecoration(
                labelText: 'اختر البرنامج التدريبي',
                border: OutlineInputBorder(),
              ),
              items: _programs.map((program) {
                return DropdownMenuItem(
                  value: program.id,
                  child: Text(program.name),
                );
              }).toList(),
              onChanged: (value) {
                // حفظ البرنامج المختار
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              // تنفيذ التسجيل
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تم تسجيل الموظف بنجاح'),
                  backgroundColor: Colors.green,
                ),
              );
            },
            child: const Text('تسجيل'),
          ),
        ],
      ),
    );
  }

  Color _getEnrollmentStatusColor(String status) {
    switch (status) {
      case 'enrolled':
        return RevolutionaryColors.infoTurquoise;
      case 'in_progress':
        return RevolutionaryColors.warningAmber;
      case 'completed':
        return RevolutionaryColors.successGlow;
      case 'cancelled':
        return RevolutionaryColors.errorCoral;
      default:
        return Colors.grey;
    }
  }

  void _viewEnrollmentDetails(int index) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تفاصيل التسجيل'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('الموظف: موظف ${index + 1}'),
            Text('البرنامج: برنامج تدريبي ${index + 1}'),
            Text('التقدم: ${(index + 1) * 20}%'),
            Text('الحالة: ${['مسجل', 'قيد التنفيذ', 'مكتمل'][index % 3]}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  void _updateEnrollmentProgress(int index) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تحديث التقدم'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('موظف ${index + 1}'),
            const SizedBox(height: 16),
            TextFormField(
              decoration: const InputDecoration(
                labelText: 'نسبة التقدم (%)',
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.number,
              initialValue: '${(index + 1) * 20}',
            ),
            const SizedBox(height: 16),
            TextFormField(
              decoration: const InputDecoration(
                labelText: 'ملاحظات',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تم تحديث التقدم بنجاح'),
                  backgroundColor: Colors.green,
                ),
              );
            },
            child: const Text('حفظ'),
          ),
        ],
      ),
    );
  }

  void _cancelEnrollment(int index) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إلغاء التسجيل'),
        content: Text('هل أنت متأكد من إلغاء تسجيل موظف ${index + 1}؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('لا'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تم إلغاء التسجيل'),
                  backgroundColor: Colors.orange,
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: RevolutionaryColors.errorCoral,
            ),
            child: const Text('نعم، إلغاء'),
          ),
        ],
      ),
    );
  }

  // دوال البحث والفلترة المفقودة
  void _searchPrograms(String query) {
    setState(() {
      if (query.isEmpty) {
        // إعادة تحميل جميع البرامج
        _loadData();
      } else {
        // فلترة البرامج حسب النص المدخل
        _programs = _programs.where((program) {
          return program.name.toLowerCase().contains(query.toLowerCase()) ||
              program.description!.toLowerCase().contains(
                query.toLowerCase(),
              ) ||
              program.category!.toLowerCase().contains(query.toLowerCase());
        }).toList();
      }
    });
  }

  void _filterEnrollmentsByProgram(int? programId) {
    setState(() {
      if (programId == null) {
        // إعادة تحميل جميع التسجيلات
        _loadData();
      } else {
        // فلترة التسجيلات حسب البرنامج
        _enrollments = _enrollments.where((enrollment) {
          return enrollment.sessionId ==
              programId; // استخدام sessionId كـ programId
        }).toList();
      }
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم فلترة التسجيلات حسب البرنامج'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  void _filterEnrollmentsByStatus(String? status) {
    setState(() {
      if (status == null) {
        // إعادة تحميل جميع التسجيلات
        _loadData();
      } else {
        // فلترة التسجيلات حسب الحالة
        _enrollments = _enrollments.where((enrollment) {
          return enrollment.status == status;
        }).toList();
      }
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم فلترة التسجيلات حسب الحالة: $status'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  void _exportEnrollments() async {
    try {
      // إنشاء بيانات CSV للتسجيلات
      String csvData = 'الموظف,البرنامج,تاريخ التسجيل,الحالة,التقدم\n';

      for (var enrollment in _enrollments) {
        // البحث عن اسم الموظف والبرنامج
        final employeeName =
            'موظف ${enrollment.employeeId}'; // يمكن تحسينه لاحقاً
        final programName =
            'برنامج ${enrollment.sessionId}'; // يمكن تحسينه لاحقاً

        csvData +=
            '$employeeName,$programName,${enrollment.enrollmentDate.toString().split(' ')[0]},${enrollment.status},${enrollment.score ?? 0}%\n';
      }

      // هنا يمكن إضافة منطق حفظ الملف
      LoggingService.info(
        'تصدير بيانات التسجيلات',
        category: 'TrainingDevelopmentScreen',
        data: {'enrollments_count': _enrollments.length},
      );
      LoggingService.info('بيانات CSV: $csvData');

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('تم تصدير ${_enrollments.length} تسجيل بنجاح'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في تصدير التسجيلات: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _exportTrainingData() async {
    try {
      // إنشاء بيانات شاملة للتدريب
      String csvData = 'البرنامج,الفئة,الوصف,المدة,التكلفة,المتطلبات,الحالة\n';

      for (var program in _programs) {
        csvData +=
            '${program.name},${program.category ?? 'غير محدد'},${program.description ?? 'غير محدد'},${program.durationHours},${program.cost},${program.prerequisites ?? 'لا يوجد'},${program.isActive ? 'نشط' : 'غير نشط'}\n';
      }

      // هنا يمكن إضافة كود حفظ الملف أو مشاركته
      LoggingService.info(
        'تصدير بيانات التدريب',
        category: 'TrainingDevelopmentScreen',
        data: {'programs_count': _programs.length},
      );
      LoggingService.info('بيانات CSV: $csvData');

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('تم تصدير ${_programs.length} برنامج تدريبي بنجاح'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في تصدير بيانات التدريب: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  // دوال الجلسات المفقودة
  void _editSession(Map<String, dynamic> session) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تعديل الجلسة'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('تعديل جلسة: ${session['name']}'),
            const SizedBox(height: 16),
            const Text('هذه الميزة قيد التطوير'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تم حفظ التعديلات'),
                  backgroundColor: Colors.green,
                ),
              );
            },
            child: const Text('حفظ'),
          ),
        ],
      ),
    );
  }

  void _viewSessionParticipants(Map<String, dynamic> session) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('مشاركو الجلسة'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('جلسة: ${session['name']}'),
            const SizedBox(height: 16),
            Text('عدد المشاركين: ${session['participants']}'),
            const SizedBox(height: 16),
            const Text('قائمة المشاركين:'),
            const SizedBox(height: 8),
            // قائمة وهمية للمشاركين
            ...List.generate(
              3,
              (index) => Padding(
                padding: const EdgeInsets.symmetric(vertical: 4),
                child: Row(
                  children: [
                    const Icon(Icons.person, size: 16),
                    const SizedBox(width: 8),
                    Text('مشارك ${index + 1}'),
                  ],
                ),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  void _cancelSession(Map<String, dynamic> session) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إلغاء الجلسة'),
        content: Text('هل أنت متأكد من إلغاء جلسة "${session['name']}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('لا'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('تم إلغاء جلسة "${session['name']}"'),
                  backgroundColor: Colors.orange,
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: RevolutionaryColors.errorCoral,
            ),
            child: const Text('نعم، إلغاء'),
          ),
        ],
      ),
    );
  }

  // دوال المهارات المفقودة
  void _viewSkillDetails(Map<String, dynamic> skill) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تفاصيل المهارة'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'المهارة: ${skill['name']}',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            Text('عدد الموظفين: ${skill['employees']}'),
            const SizedBox(height: 8),
            Text('متوسط المستوى: ${skill['level']}/5'),
            const SizedBox(height: 12),
            const Text(
              'الموظفون الذين يمتلكون هذه المهارة:',
              style: TextStyle(fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 8),
            // قائمة وهمية للموظفين
            ...List.generate(
              3,
              (index) => Padding(
                padding: const EdgeInsets.symmetric(vertical: 2),
                child: Row(
                  children: [
                    const Icon(Icons.person, size: 16),
                    const SizedBox(width: 8),
                    Text('موظف ${index + 1}'),
                    const Spacer(),
                    Row(
                      children: List.generate(5, (starIndex) {
                        return Icon(
                          starIndex < (3 + index)
                              ? Icons.star
                              : Icons.star_border,
                          size: 12,
                          color: Colors.amber,
                        );
                      }),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  void _assessSkill(Map<String, dynamic> skill) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تقييم المهارة'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('تقييم مهارة: ${skill['name']}'),
            const SizedBox(height: 16),
            const Text('اختر الموظف للتقييم:'),
            const SizedBox(height: 8),
            DropdownButtonFormField<String>(
              decoration: const InputDecoration(
                border: OutlineInputBorder(),
                hintText: 'اختر الموظف',
              ),
              items: const [
                DropdownMenuItem(value: '1', child: Text('موظف 1')),
                DropdownMenuItem(value: '2', child: Text('موظف 2')),
                DropdownMenuItem(value: '3', child: Text('موظف 3')),
              ],
              onChanged: (value) {},
            ),
            const SizedBox(height: 16),
            const Text('التقييم الجديد:'),
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: List.generate(5, (index) {
                return IconButton(
                  onPressed: () {},
                  icon: Icon(Icons.star_border, color: Colors.amber),
                );
              }),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تم حفظ التقييم بنجاح'),
                  backgroundColor: Colors.green,
                ),
              );
            },
            child: const Text('حفظ التقييم'),
          ),
        ],
      ),
    );
  }

  void _suggestTraining(Map<String, dynamic> skill) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اقتراح تدريب'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('اقتراح تدريب لمهارة: ${skill['name']}'),
            const SizedBox(height: 16),
            const Text(
              'البرامج التدريبية المقترحة:',
              style: TextStyle(fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 8),
            // قائمة وهمية للبرامج المقترحة
            ...List.generate(
              2,
              (index) => Card(
                margin: const EdgeInsets.symmetric(vertical: 4),
                child: ListTile(
                  leading: const Icon(Icons.school, size: 20),
                  title: Text('برنامج تطوير ${skill['name']} ${index + 1}'),
                  subtitle: Text('مدة: ${(index + 1) * 8} ساعات'),
                  trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                  onTap: () {
                    // الانتقال لتفاصيل البرنامج
                  },
                ),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تم إرسال اقتراحات التدريب'),
                  backgroundColor: Colors.blue,
                ),
              );
            },
            child: const Text('إرسال الاقتراحات'),
          ),
        ],
      ),
    );
  }

  // دوال خطط التطوير المفقودة
  void _showAddDevelopmentPlanDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إضافة خطة تطوير مهني'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('هذه الميزة قيد التطوير'),
            SizedBox(height: 16),
            Text('سيتم إضافة حوار شامل لإنشاء خطط التطوير المهني'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تم إنشاء خطة تطوير جديدة'),
                  backgroundColor: Colors.green,
                ),
              );
            },
            child: const Text('إنشاء'),
          ),
        ],
      ),
    );
  }

  void _viewPlanDetails(Map<String, dynamic> plan) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تفاصيل خطة التطوير'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'العنوان: ${plan['title']}',
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 12),
              Text('الموظف: ${plan['employee']}'),
              const SizedBox(height: 8),
              Text('المرشد: ${plan['mentor']}'),
              const SizedBox(height: 8),
              Text('تاريخ البداية: ${plan['startDate']}'),
              const SizedBox(height: 8),
              Text('التاريخ المستهدف: ${plan['targetDate']}'),
              const SizedBox(height: 8),
              Text('التقدم: ${plan['progress']}%'),
              const SizedBox(height: 12),
              const Text(
                'الأهداف:',
                style: TextStyle(fontWeight: FontWeight.w600),
              ),
              const SizedBox(height: 8),
              ...(plan['goals'] as List<String>).map(
                (goal) => Padding(
                  padding: const EdgeInsets.symmetric(vertical: 2),
                  child: Row(
                    children: [
                      const Icon(Icons.check_circle_outline, size: 16),
                      const SizedBox(width: 8),
                      Expanded(child: Text(goal)),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  void _updatePlanProgress(Map<String, dynamic> plan) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تحديث التقدم'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('خطة: ${plan['title']}'),
            const SizedBox(height: 16),
            TextFormField(
              decoration: const InputDecoration(
                labelText: 'نسبة التقدم الجديدة (%)',
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.number,
              initialValue: '${plan['progress']}',
            ),
            const SizedBox(height: 16),
            TextFormField(
              decoration: const InputDecoration(
                labelText: 'ملاحظات التحديث',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تم تحديث التقدم بنجاح'),
                  backgroundColor: Colors.green,
                ),
              );
            },
            child: const Text('حفظ'),
          ),
        ],
      ),
    );
  }

  void _editPlan(Map<String, dynamic> plan) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تعديل خطة التطوير'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('تعديل خطة: ${plan['title']}'),
            const SizedBox(height: 16),
            const Text('هذه الميزة قيد التطوير'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تم حفظ التعديلات'),
                  backgroundColor: Colors.green,
                ),
              );
            },
            child: const Text('حفظ'),
          ),
        ],
      ),
    );
  }

  void _completePlan(Map<String, dynamic> plan) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إكمال خطة التطوير'),
        content: Text('هل أنت متأكد من إكمال خطة "${plan['title']}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('لا'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('تم إكمال خطة "${plan['title']}" بنجاح'),
                  backgroundColor: Colors.green,
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: RevolutionaryColors.successGlow,
            ),
            child: const Text('نعم، إكمال'),
          ),
        ],
      ),
    );
  }

  void _cancelPlan(Map<String, dynamic> plan) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إلغاء خطة التطوير'),
        content: Text('هل أنت متأكد من إلغاء خطة "${plan['title']}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('لا'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('تم إلغاء خطة "${plan['title']}"'),
                  backgroundColor: Colors.orange,
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: RevolutionaryColors.errorCoral,
            ),
            child: const Text('نعم، إلغاء'),
          ),
        ],
      ),
    );
  }
}
