/// خدمة إدارة الإجازات
/// توفر عمليات إدارة طلبات الإجازات ونظام الموافقات
library;

import '../database/database_helper.dart';
import '../constants/app_constants.dart';
import '../services/logging_service.dart';
import '../services/audit_service.dart';
import '../exceptions/validation_exception.dart';
import '../models/hr_models.dart';

class LeaveService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  /// إنشاء طلب إجازة جديد
  Future<Leave> createLeaveRequest({
    required int employeeId,
    required String leaveType,
    required DateTime startDate,
    required DateTime endDate,
    String? reason,
    int? requestedBy,
  }) async {
    try {
      // التحقق من صحة البيانات
      if (startDate.isAfter(endDate)) {
        throw ValidationException(
          'تاريخ البداية لا يمكن أن يكون بعد تاريخ النهاية',
        );
      }

      if (startDate.isBefore(
        DateTime.now().subtract(const Duration(days: 1)),
      )) {
        throw ValidationException('لا يمكن طلب إجازة في الماضي');
      }

      // حساب عدد الأيام
      final totalDays = endDate.difference(startDate).inDays + 1;

      // التحقق من تداخل الإجازات
      final overlappingLeaves = await _checkOverlappingLeaves(
        employeeId,
        startDate,
        endDate,
      );

      if (overlappingLeaves.isNotEmpty) {
        throw ValidationException('يوجد تداخل مع إجازة أخرى في نفس الفترة');
      }

      // التحقق من رصيد الإجازات (للأنواع التي تحتاج رصيد)
      if (_isLeaveTypeWithBalance(leaveType)) {
        final remainingBalance = await _getEmployeeLeaveBalance(
          employeeId,
          leaveType,
        );
        if (remainingBalance < totalDays) {
          throw ValidationException(
            'رصيد الإجازات غير كافي. المتبقي: $remainingBalance يوم',
          );
        }
      }

      final leave = Leave(
        employeeId: employeeId,
        leaveType: leaveType,
        startDate: startDate,
        endDate: endDate,
        totalDays: totalDays,
        reason: reason,
        requestedBy: requestedBy ?? employeeId,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // حفظ طلب الإجازة في قاعدة البيانات
      final db = await _databaseHelper.database;
      final id = await db.insert(AppConstants.leavesTable, leave.toMap());

      final savedLeave = leave.copyWith(id: id);

      // تسجيل في سجل المراجعة
      await AuditService.log(
        action: 'CREATE',
        entityType: 'Leave',
        entityId: id,
        description: 'إنشاء طلب إجازة جديد',
        newValues: savedLeave.toMap(),
      );

      LoggingService.info(
        'تم إنشاء طلب إجازة جديد بنجاح',
        category: 'LeaveService',
        data: {
          'leaveId': id,
          'employeeId': employeeId,
          'leaveType': leaveType,
          'totalDays': totalDays,
        },
      );

      return savedLeave;
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء طلب الإجازة',
        category: 'LeaveService',
        data: {
          'employeeId': employeeId,
          'leaveType': leaveType,
          'error': e.toString(),
        },
      );
      rethrow;
    }
  }

  /// الحصول على جميع طلبات الإجازات
  Future<List<Leave>> getAllLeaves({
    int? employeeId,
    String? status,
    String? leaveType,
    DateTime? fromDate,
    DateTime? toDate,
  }) async {
    try {
      final db = await _databaseHelper.database;

      List<String> whereConditions = [];
      List<dynamic> whereArgs = [];

      if (employeeId != null) {
        whereConditions.add('employee_id = ?');
        whereArgs.add(employeeId);
      }

      if (status != null) {
        whereConditions.add('status = ?');
        whereArgs.add(status);
      }

      if (leaveType != null) {
        whereConditions.add('leave_type = ?');
        whereArgs.add(leaveType);
      }

      if (fromDate != null) {
        whereConditions.add('start_date >= ?');
        whereArgs.add(fromDate.toIso8601String().split('T')[0]);
      }

      if (toDate != null) {
        whereConditions.add('end_date <= ?');
        whereArgs.add(toDate.toIso8601String().split('T')[0]);
      }

      String? whereClause;
      if (whereConditions.isNotEmpty) {
        whereClause = whereConditions.join(' AND ');
      }

      final result = await db.query(
        AppConstants.leavesTable,
        where: whereClause,
        whereArgs: whereArgs.isNotEmpty ? whereArgs : null,
        orderBy: 'created_at DESC',
      );

      return result.map((map) => Leave.fromMap(map)).toList();
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب طلبات الإجازات',
        category: 'LeaveService',
        data: {'error': e.toString()},
      );
      return [];
    }
  }

  /// الحصول على طلب إجازة بالمعرف
  Future<Leave?> getLeaveById(int id) async {
    try {
      final db = await _databaseHelper.database;
      final result = await db.query(
        AppConstants.leavesTable,
        where: 'id = ?',
        whereArgs: [id],
        limit: 1,
      );

      if (result.isNotEmpty) {
        return Leave.fromMap(result.first);
      }
      return null;
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب طلب الإجازة',
        category: 'LeaveService',
        data: {'leaveId': id, 'error': e.toString()},
      );
      return null;
    }
  }

  /// الحصول على طلبات الإجازات المعلقة
  Future<List<Leave>> getPendingLeaves() async {
    return getAllLeaves(status: AppConstants.leaveStatusPending);
  }

  /// الحصول على إجازات موظف معين
  Future<List<Leave>> getEmployeeLeaves(int employeeId) async {
    return getAllLeaves(employeeId: employeeId);
  }

  /// الموافقة على طلب إجازة
  Future<Leave> approveLeave({
    required int leaveId,
    required int approvedBy,
    String? notes,
  }) async {
    try {
      final leave = await getLeaveById(leaveId);
      if (leave == null) {
        throw ValidationException('طلب الإجازة غير موجود');
      }

      if (!leave.isPending) {
        throw ValidationException('طلب الإجازة تم البت فيه مسبقاً');
      }

      // التحقق من رصيد الإجازات مرة أخرى عند الموافقة
      if (leave.leaveType == AppConstants.leaveTypeAnnual) {
        final remainingBalance = await _getEmployeeLeaveBalance(
          leave.employeeId,
          leave.leaveType,
        );
        if (remainingBalance < leave.totalDays) {
          throw ValidationException(
            'رصيد الإجازات غير كافي. المتبقي: $remainingBalance يوم',
          );
        }
      }

      final approvedLeave = leave.copyWith(
        status: AppConstants.leaveStatusApproved,
        approvedBy: approvedBy,
        approvedAt: DateTime.now(),
        rejectionReason: notes,
        updatedAt: DateTime.now(),
      );

      final db = await _databaseHelper.database;
      await db.update(
        AppConstants.leavesTable,
        approvedLeave.toMap(),
        where: 'id = ?',
        whereArgs: [leaveId],
      );

      // تسجيل في سجل المراجعة
      await AuditService.log(
        action: 'UPDATE',
        entityType: 'Leave',
        entityId: leaveId,
        description: 'الموافقة على طلب الإجازة',
        newValues: approvedLeave.toMap(),
      );

      LoggingService.info(
        'تم الموافقة على طلب الإجازة بنجاح',
        category: 'LeaveService',
        data: {'leaveId': leaveId, 'approvedBy': approvedBy},
      );

      return approvedLeave;
    } catch (e) {
      LoggingService.error(
        'خطأ في الموافقة على طلب الإجازة',
        category: 'LeaveService',
        data: {'leaveId': leaveId, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// رفض طلب إجازة
  Future<Leave> rejectLeave({
    required int leaveId,
    required int rejectedBy,
    required String rejectionReason,
  }) async {
    try {
      final leave = await getLeaveById(leaveId);
      if (leave == null) {
        throw ValidationException('طلب الإجازة غير موجود');
      }

      if (!leave.isPending) {
        throw ValidationException('طلب الإجازة تم البت فيه مسبقاً');
      }

      if (rejectionReason.trim().isEmpty) {
        throw ValidationException('سبب الرفض مطلوب');
      }

      final rejectedLeave = leave.copyWith(
        status: AppConstants.leaveStatusRejected,
        approvedBy: rejectedBy,
        approvedAt: DateTime.now(),
        rejectionReason: rejectionReason,
        updatedAt: DateTime.now(),
      );

      final db = await _databaseHelper.database;
      await db.update(
        AppConstants.leavesTable,
        rejectedLeave.toMap(),
        where: 'id = ?',
        whereArgs: [leaveId],
      );

      // تسجيل في سجل المراجعة
      await AuditService.log(
        action: 'UPDATE',
        entityType: 'Leave',
        entityId: leaveId,
        description: 'رفض طلب الإجازة',
        newValues: rejectedLeave.toMap(),
      );

      LoggingService.info(
        'تم رفض طلب الإجازة',
        category: 'LeaveService',
        data: {
          'leaveId': leaveId,
          'rejectedBy': rejectedBy,
          'reason': rejectionReason,
        },
      );

      return rejectedLeave;
    } catch (e) {
      LoggingService.error(
        'خطأ في رفض طلب الإجازة',
        category: 'LeaveService',
        data: {'leaveId': leaveId, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// التحقق من تداخل الإجازات
  Future<List<Leave>> _checkOverlappingLeaves(
    int employeeId,
    DateTime startDate,
    DateTime endDate,
  ) async {
    final db = await _databaseHelper.database;

    final result = await db.query(
      AppConstants.leavesTable,
      where: '''
        employee_id = ? AND 
        status IN (?, ?) AND
        ((start_date <= ? AND end_date >= ?) OR
         (start_date <= ? AND end_date >= ?) OR
         (start_date >= ? AND end_date <= ?))
      ''',
      whereArgs: [
        employeeId,
        AppConstants.leaveStatusPending,
        AppConstants.leaveStatusApproved,
        startDate.toIso8601String().split('T')[0],
        startDate.toIso8601String().split('T')[0],
        endDate.toIso8601String().split('T')[0],
        endDate.toIso8601String().split('T')[0],
        startDate.toIso8601String().split('T')[0],
        endDate.toIso8601String().split('T')[0],
      ],
    );

    return result.map((map) => Leave.fromMap(map)).toList();
  }

  /// حساب رصيد الإجازات المتبقي للموظف
  Future<int> _getEmployeeLeaveBalance(int employeeId, String leaveType) async {
    // هذا مثال بسيط - يمكن تطويره ليكون أكثر تعقيداً
    // بناءً على سياسة الشركة للإجازات

    const int annualLeaveEntitlement = 30; // 30 يوم إجازة سنوية

    final currentYear = DateTime.now().year;
    final startOfYear = DateTime(currentYear, 1, 1);
    final endOfYear = DateTime(currentYear, 12, 31);

    final usedLeaves = await getAllLeaves(
      employeeId: employeeId,
      leaveType: leaveType,
      status: AppConstants.leaveStatusApproved,
      fromDate: startOfYear,
      toDate: endOfYear,
    );

    final usedDays = usedLeaves.fold<int>(
      0,
      (sum, leave) => sum + leave.totalDays,
    );

    return annualLeaveEntitlement - usedDays;
  }

  /// التحقق من أن نوع الإجازة يحتاج رصيد
  bool _isLeaveTypeWithBalance(String leaveType) {
    return AppConstants.paidLeaveTypes.contains(leaveType) &&
        leaveType != AppConstants.leaveTypeEmergency &&
        leaveType != AppConstants.leaveTypeMaternity &&
        leaveType != AppConstants.leaveTypePaternity &&
        leaveType != AppConstants.leaveTypeMarriage &&
        leaveType != AppConstants.leaveTypeDeath;
  }

  /// الحصول على معلومات نوع الإجازة
  Map<String, dynamic> getLeaveTypeInfo(String leaveType) {
    final maxDays = AppConstants.leaveTypeMaxDays[leaveType] ?? 0;
    final isPaid = AppConstants.paidLeaveTypes.contains(leaveType);
    final needsSpecialApproval = AppConstants.specialApprovalLeaveTypes
        .contains(leaveType);

    String displayName;
    String description;

    switch (leaveType) {
      case AppConstants.leaveTypeAnnual:
        displayName = 'إجازة سنوية';
        description = 'إجازة سنوية مدفوعة الأجر';
        break;
      case AppConstants.leaveTypeSick:
        displayName = 'إجازة مرضية';
        description = 'إجازة مرضية بتقرير طبي';
        break;
      case AppConstants.leaveTypeEmergency:
        displayName = 'إجازة طارئة';
        description = 'إجازة طارئة لظروف استثنائية';
        break;
      case AppConstants.leaveTypeMaternity:
        displayName = 'إجازة أمومة';
        description = 'إجازة أمومة مدفوعة الأجر';
        break;
      case AppConstants.leaveTypePaternity:
        displayName = 'إجازة أبوة';
        description = 'إجازة أبوة مدفوعة الأجر';
        break;
      case AppConstants.leaveTypeMarriage:
        displayName = 'إجازة زواج';
        description = 'إجازة زواج مدفوعة الأجر';
        break;
      case AppConstants.leaveTypeDeath:
        displayName = 'إجازة وفاة';
        description = 'إجازة وفاة لأحد الأقارب';
        break;
      case AppConstants.leaveTypePilgrimage:
        displayName = 'إجازة حج';
        description = 'إجازة أداء فريضة الحج';
        break;
      case AppConstants.leaveTypeStudy:
        displayName = 'إجازة دراسية';
        description = 'إجازة للدراسة والتعليم';
        break;
      case AppConstants.leaveTypeTraining:
        displayName = 'إجازة تدريب';
        description = 'إجازة للتدريب والتطوير';
        break;
      case AppConstants.leaveTypePersonal:
        displayName = 'إجازة شخصية';
        description = 'إجازة شخصية بدون راتب';
        break;
      case AppConstants.leaveTypeCompensatory:
        displayName = 'إجازة تعويضية';
        description = 'إجازة تعويضية مقابل عمل إضافي';
        break;
      case AppConstants.leaveTypeUnpaid:
        displayName = 'إجازة بدون راتب';
        description = 'إجازة بدون راتب لظروف خاصة';
        break;
      default:
        displayName = 'نوع إجازة غير معروف';
        description = 'نوع إجازة غير محدد';
    }

    return {
      'type': leaveType,
      'displayName': displayName,
      'description': description,
      'maxDays': maxDays,
      'isPaid': isPaid,
      'needsSpecialApproval': needsSpecialApproval,
      'hasBalance': _isLeaveTypeWithBalance(leaveType),
    };
  }

  /// الحصول على جميع أنواع الإجازات المتاحة
  List<Map<String, dynamic>> getAllLeaveTypes() {
    return [
      AppConstants.leaveTypeAnnual,
      AppConstants.leaveTypeSick,
      AppConstants.leaveTypeEmergency,
      AppConstants.leaveTypeMaternity,
      AppConstants.leaveTypePaternity,
      AppConstants.leaveTypeMarriage,
      AppConstants.leaveTypeDeath,
      AppConstants.leaveTypePilgrimage,
      AppConstants.leaveTypeStudy,
      AppConstants.leaveTypeTraining,
      AppConstants.leaveTypePersonal,
      AppConstants.leaveTypeCompensatory,
      AppConstants.leaveTypeUnpaid,
    ].map((type) => getLeaveTypeInfo(type)).toList();
  }

  /// إلغاء طلب إجازة
  Future<void> cancelLeaveRequest(int leaveId, String reason) async {
    try {
      final leave = await getLeaveById(leaveId);
      if (leave == null) {
        throw ValidationException('طلب الإجازة غير موجود');
      }

      if (leave.status != AppConstants.leaveStatusPending) {
        throw ValidationException(
          'لا يمكن إلغاء طلب إجازة تم الموافقة عليه أو رفضه',
        );
      }

      final db = await _databaseHelper.database;
      await db.update(
        AppConstants.leavesTable,
        {
          'status': AppConstants.leaveStatusCancelled,
          'rejection_reason': reason,
          'updated_at': DateTime.now().toIso8601String(),
        },
        where: 'id = ?',
        whereArgs: [leaveId],
      );

      // تسجيل في سجل المراجعة
      await AuditService.log(
        action: 'CANCEL',
        entityType: 'Leave',
        entityId: leaveId,
        description: 'إلغاء طلب إجازة',
        oldValues: leave.toMap(),
        newValues: {
          'status': AppConstants.leaveStatusCancelled,
          'reason': reason,
        },
      );

      LoggingService.info(
        'تم إلغاء طلب الإجازة',
        category: 'LeaveService',
        data: {'leaveId': leaveId, 'reason': reason},
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في إلغاء طلب الإجازة',
        category: 'LeaveService',
        data: {'leaveId': leaveId, 'error': e.toString()},
      );
      rethrow;
    }
  }
}
