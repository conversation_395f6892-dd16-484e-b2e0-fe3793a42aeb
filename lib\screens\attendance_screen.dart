/// شاشة الحضور والانصراف
/// واجهة لتسجيل وإدارة الحضور والانصراف للموظفين
library;

import 'package:flutter/material.dart';

import '../constants/revolutionary_design_colors.dart';
import '../models/hr_models.dart';
import '../services/attendance_service.dart' as attendance_service;
import '../services/employee_service.dart';
import '../services/logging_service.dart';
import '../widgets/loading_widget.dart';

class AttendanceScreen extends StatefulWidget {
  const AttendanceScreen({super.key});

  @override
  State<AttendanceScreen> createState() => _AttendanceScreenState();
}

class _AttendanceScreenState extends State<AttendanceScreen> {
  final attendance_service.AttendanceService _attendanceService =
      attendance_service.AttendanceService();
  final EmployeeService _employeeService = EmployeeService();

  List<Employee> _employees = [];
  List<Attendance> _todayAttendance = [];
  bool _isLoading = true;
  String? _error;
  DateTime _selectedDate = DateTime.now();

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      // تحميل قائمة الموظفين النشطين
      final employees = await _employeeService.getAllEmployees(
        activeOnly: true,
      );

      // تحميل حضور اليوم
      final todayAttendance = <Attendance>[];
      for (final employee in employees) {
        final attendance = await _attendanceService.getAttendanceByDate(
          employee.id!,
          _selectedDate,
        );
        if (attendance != null) {
          todayAttendance.add(attendance);
        }
      }

      setState(() {
        _employees = employees;
        _todayAttendance = todayAttendance;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });

      LoggingService.error(
        'خطأ في تحميل بيانات الحضور',
        category: 'AttendanceScreen',
        data: {'error': e.toString()},
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('الحضور والانصراف'),
        backgroundColor: RevolutionaryColors.damascusSky,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.calendar_today),
            onPressed: _selectDate,
          ),
          IconButton(
            icon: const Icon(Icons.analytics),
            onPressed: _showAdvancedReports,
            tooltip: 'التقارير المتقدمة',
          ),
          IconButton(icon: const Icon(Icons.refresh), onPressed: _loadData),
        ],
      ),
      body: Column(
        children: [
          _buildDateHeader(),
          _buildQuickStats(),
          Expanded(child: _buildAttendanceList()),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _showQuickAttendanceDialog,
        backgroundColor: RevolutionaryColors.successGlow,
        icon: const Icon(Icons.access_time, color: Colors.white),
        label: const Text('تسجيل سريع', style: TextStyle(color: Colors.white)),
      ),
    );
  }

  Widget _buildDateHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: RevolutionaryColors.damascusSky.withValues(alpha: 0.1),
        border: Border(bottom: BorderSide(color: Colors.grey[300]!)),
      ),
      child: Row(
        children: [
          Icon(Icons.calendar_today, color: RevolutionaryColors.damascusSky),
          const SizedBox(width: 8),
          Text(
            'التاريخ: ${_selectedDate.day}/${_selectedDate.month}/${_selectedDate.year}',
            style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const Spacer(),
          if (_selectedDate.day == DateTime.now().day &&
              _selectedDate.month == DateTime.now().month &&
              _selectedDate.year == DateTime.now().year)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: RevolutionaryColors.successGlow,
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Text(
                'اليوم',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildQuickStats() {
    final totalEmployees = _employees.length;
    final presentEmployees = _todayAttendance.length;
    final absentEmployees = totalEmployees - presentEmployees;
    final lateEmployees = _todayAttendance
        .where((a) => a.lateMinutes > 0)
        .length;

    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Expanded(
            child: _buildStatCard(
              'إجمالي الموظفين',
              totalEmployees.toString(),
              Icons.people,
              RevolutionaryColors.damascusSky,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: _buildStatCard(
              'حاضر',
              presentEmployees.toString(),
              Icons.check_circle,
              RevolutionaryColors.successGlow,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: _buildStatCard(
              'غائب',
              absentEmployees.toString(),
              Icons.cancel,
              RevolutionaryColors.errorCoral,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: _buildStatCard(
              'متأخر',
              lateEmployees.toString(),
              Icons.access_time,
              RevolutionaryColors.warningAmber,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          children: [
            Icon(icon, color: color, size: 24),
            const SizedBox(height: 4),
            Text(
              value,
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            const SizedBox(height: 2),
            Text(
              title,
              style: const TextStyle(fontSize: 12, color: Colors.grey),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAttendanceList() {
    if (_isLoading) {
      return const LoadingWidget(message: 'جاري تحميل بيانات الحضور...');
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: Colors.red[400]),
            const SizedBox(height: 16),
            Text(
              'حدث خطأ',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.red[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _error!,
              style: TextStyle(fontSize: 14, color: Colors.grey[600]),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadData,
              child: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      );
    }

    if (_employees.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.people_outline, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'لا يوجد موظفين',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text(
              'يرجى إضافة موظفين أولاً',
              style: TextStyle(color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _employees.length,
      itemBuilder: (context, index) {
        final employee = _employees[index];
        final attendance = _todayAttendance.firstWhere(
          (a) => a.employeeId == employee.id,
          orElse: () => Attendance(
            employeeId: employee.id!,
            attendanceDate: _selectedDate,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
        );

        return _buildEmployeeAttendanceCard(employee, attendance);
      },
    );
  }

  Widget _buildEmployeeAttendanceCard(
    Employee employee,
    Attendance attendance,
  ) {
    final hasAttendance = attendance.id != null;
    final isPresent = hasAttendance && attendance.status == 'present';
    final isLate = hasAttendance && attendance.lateMinutes > 0;

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Row(
              children: [
                // صورة الموظف
                CircleAvatar(
                  radius: 25,
                  backgroundColor: RevolutionaryColors.damascusSky.withValues(
                    alpha: 0.1,
                  ),
                  backgroundImage: employee.photoPath != null
                      ? AssetImage(employee.photoPath!)
                      : null,
                  child: employee.photoPath == null
                      ? Icon(
                          Icons.person,
                          size: 25,
                          color: RevolutionaryColors.damascusSky,
                        )
                      : null,
                ),
                const SizedBox(width: 12),
                // معلومات الموظف
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        employee.displayName,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'رقم الموظف: ${employee.employeeNumber}',
                        style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                      ),
                    ],
                  ),
                ),
                // حالة الحضور
                _buildAttendanceStatus(isPresent, isLate),
              ],
            ),
            if (hasAttendance) ...[
              const SizedBox(height: 12),
              const Divider(),
              const SizedBox(height: 8),
              _buildAttendanceDetails(attendance),
            ],
            const SizedBox(height: 12),
            _buildAttendanceActions(employee, attendance),
          ],
        ),
      ),
    );
  }

  Widget _buildAttendanceStatus(bool isPresent, bool isLate) {
    if (!isPresent) {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: RevolutionaryColors.errorCoral.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: RevolutionaryColors.errorCoral),
        ),
        child: const Text(
          'غائب',
          style: TextStyle(
            fontSize: 12,
            color: RevolutionaryColors.errorCoral,
            fontWeight: FontWeight.bold,
          ),
        ),
      );
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color:
            (isLate
                    ? RevolutionaryColors.warningAmber
                    : RevolutionaryColors.successGlow)
                .withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isLate
              ? RevolutionaryColors.warningAmber
              : RevolutionaryColors.successGlow,
        ),
      ),
      child: Text(
        isLate ? 'متأخر' : 'حاضر',
        style: TextStyle(
          fontSize: 12,
          color: isLate
              ? RevolutionaryColors.warningAmber
              : RevolutionaryColors.successGlow,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildAttendanceDetails(Attendance attendance) {
    return Row(
      children: [
        Expanded(
          child: _buildTimeInfo(
            'الحضور',
            attendance.checkInTime,
            Icons.login,
            RevolutionaryColors.successGlow,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildTimeInfo(
            'الانصراف',
            attendance.checkOutTime,
            Icons.logout,
            RevolutionaryColors.errorCoral,
          ),
        ),
        if (attendance.lateMinutes > 0) ...[
          const SizedBox(width: 16),
          Expanded(
            child: _buildTimeInfo(
              'التأخير',
              null,
              Icons.access_time,
              RevolutionaryColors.warningAmber,
              customText: '${attendance.lateMinutes} دقيقة',
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildTimeInfo(
    String label,
    DateTime? time,
    IconData icon,
    Color color, {
    String? customText,
  }) {
    return Column(
      children: [
        Icon(icon, color: color, size: 20),
        const SizedBox(height: 4),
        Text(label, style: const TextStyle(fontSize: 12, color: Colors.grey)),
        const SizedBox(height: 2),
        Text(
          customText ??
              (time != null
                  ? '${time.hour}:${time.minute.toString().padLeft(2, '0')}'
                  : '--:--'),
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
      ],
    );
  }

  Widget _buildAttendanceActions(Employee employee, Attendance attendance) {
    final hasCheckIn = attendance.hasCheckIn;
    final hasCheckOut = attendance.hasCheckOut;
    final isToday =
        _selectedDate.day == DateTime.now().day &&
        _selectedDate.month == DateTime.now().month &&
        _selectedDate.year == DateTime.now().year;

    if (!isToday) {
      return const SizedBox.shrink();
    }

    return Row(
      children: [
        if (!hasCheckIn)
          Expanded(
            child: ElevatedButton.icon(
              onPressed: () => _checkIn(employee),
              icon: const Icon(Icons.login, size: 18),
              label: const Text('تسجيل حضور'),
              style: ElevatedButton.styleFrom(
                backgroundColor: RevolutionaryColors.successGlow,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 8),
              ),
            ),
          ),
        if (hasCheckIn && !hasCheckOut) ...[
          Expanded(
            child: ElevatedButton.icon(
              onPressed: () => _checkOut(employee),
              icon: const Icon(Icons.logout, size: 18),
              label: const Text('تسجيل انصراف'),
              style: ElevatedButton.styleFrom(
                backgroundColor: RevolutionaryColors.errorCoral,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 8),
              ),
            ),
          ),
        ],
        if (hasCheckIn && hasCheckOut)
          Expanded(
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 12),
              decoration: BoxDecoration(
                color: RevolutionaryColors.successGlow.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: RevolutionaryColors.successGlow),
              ),
              child: const Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.check_circle,
                    color: RevolutionaryColors.successGlow,
                    size: 18,
                  ),
                  SizedBox(width: 8),
                  Text(
                    'مكتمل',
                    style: TextStyle(
                      color: RevolutionaryColors.successGlow,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ),
      ],
    );
  }

  Future<void> _selectDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now().add(const Duration(days: 30)),
    );

    if (date != null) {
      setState(() {
        _selectedDate = date;
      });
      _loadData();
    }
  }

  Future<void> _checkIn(Employee employee) async {
    try {
      await _attendanceService.checkIn(employeeId: employee.id!);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم تسجيل حضور "${employee.displayName}" بنجاح'),
            backgroundColor: RevolutionaryColors.successGlow,
          ),
        );
      }

      _loadData();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تسجيل الحضور: ${e.toString()}'),
            backgroundColor: RevolutionaryColors.errorCoral,
          ),
        );
      }
    }
  }

  Future<void> _checkOut(Employee employee) async {
    try {
      await _attendanceService.checkOut(employeeId: employee.id!);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم تسجيل انصراف "${employee.displayName}" بنجاح'),
            backgroundColor: RevolutionaryColors.successGlow,
          ),
        );
      }

      _loadData();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تسجيل الانصراف: ${e.toString()}'),
            backgroundColor: RevolutionaryColors.errorCoral,
          ),
        );
      }
    }
  }

  void _showQuickAttendanceDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تسجيل سريع'),
        content: const Text('اختر نوع التسجيل السريع'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _showBulkCheckInDialog();
            },
            child: const Text('حضور جماعي'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _showBulkCheckOutDialog();
            },
            child: const Text('انصراف جماعي'),
          ),
        ],
      ),
    );
  }

  void _showBulkCheckInDialog() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('الحضور الجماعي - قريباً'),
        backgroundColor: RevolutionaryColors.infoTurquoise,
      ),
    );
  }

  void _showBulkCheckOutDialog() {
    final presentEmployees = _todayAttendance
        .where(
          (attendance) =>
              attendance.checkInTime != null && attendance.checkOutTime == null,
        )
        .toList();

    if (presentEmployees.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('لا يوجد موظفون حاضرون للانصراف'),
          backgroundColor: RevolutionaryColors.warningAmber,
        ),
      );
      return;
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('الانصراف الجماعي'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('سيتم تسجيل انصراف ${presentEmployees.length} موظف'),
            const SizedBox(height: 16),
            const Text('الموظفون الحاضرون:'),
            const SizedBox(height: 8),
            Container(
              height: 200,
              width: double.maxFinite,
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey[300]!),
                borderRadius: BorderRadius.circular(8),
              ),
              child: ListView.builder(
                itemCount: presentEmployees.length,
                itemBuilder: (context, index) {
                  final attendance = presentEmployees[index];
                  final employee = _employees.firstWhere(
                    (e) => e.id == attendance.employeeId,
                    orElse: () => Employee(
                      employeeNumber: '',
                      nationalId: '',
                      firstName: 'غير معروف',
                      lastName: '',
                      fullName: 'غير معروف',
                      hireDate: DateTime.now(),
                      createdAt: DateTime.now(),
                      updatedAt: DateTime.now(),
                    ),
                  );

                  return ListTile(
                    dense: true,
                    leading: const Icon(Icons.person, size: 20),
                    title: Text(
                      employee.displayName,
                      style: const TextStyle(fontSize: 14),
                    ),
                    subtitle: Text(
                      'حضر: ${attendance.checkInTime?.hour.toString().padLeft(2, '0')}:${attendance.checkInTime?.minute.toString().padLeft(2, '0')}',
                      style: const TextStyle(fontSize: 12),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await _performBulkCheckOut(presentEmployees);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: RevolutionaryColors.errorCoral,
            ),
            child: const Text('تسجيل الانصراف الجماعي'),
          ),
        ],
      ),
    );
  }

  Future<void> _performBulkCheckOut(List<Attendance> attendances) async {
    try {
      int successCount = 0;
      int failureCount = 0;

      for (final attendance in attendances) {
        try {
          await _attendanceService.checkOut(
            employeeId: attendance.employeeId,
            checkOutTime: DateTime.now(),
          );
          successCount++;
        } catch (e) {
          failureCount++;
          LoggingService.error(
            'خطأ في تسجيل انصراف الموظف ${attendance.employeeId}',
            category: 'AttendanceScreen',
            data: {'error': e.toString()},
          );
        }
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'تم تسجيل انصراف $successCount موظف${failureCount > 0 ? '، فشل $failureCount' : ''}',
            ),
            backgroundColor: failureCount > 0
                ? RevolutionaryColors.warningAmber
                : RevolutionaryColors.successGlow,
          ),
        );

        // إعادة تحميل البيانات
        await _loadData();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في الانصراف الجماعي: $e'),
            backgroundColor: RevolutionaryColors.errorCoral,
          ),
        );
      }
    }
  }

  /// إضافة ميزات متقدمة للتقارير
  void _showAdvancedReports() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('التقارير المتقدمة'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.calendar_today),
              title: const Text('تقرير الحضور الشهري'),
              onTap: () {
                Navigator.of(context).pop();
                _generateMonthlyReport();
              },
            ),
            ListTile(
              leading: const Icon(Icons.schedule),
              title: const Text('تقرير ساعات العمل'),
              onTap: () {
                Navigator.of(context).pop();
                _generateWorkHoursReport();
              },
            ),
            ListTile(
              leading: const Icon(Icons.warning),
              title: const Text('تقرير التأخير والغياب'),
              onTap: () {
                Navigator.of(context).pop();
                _generateAbsenceReport();
              },
            ),
            ListTile(
              leading: const Icon(Icons.trending_up),
              title: const Text('إحصائيات الحضور'),
              onTap: () {
                Navigator.of(context).pop();
                _showAttendanceStatistics();
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  void _generateMonthlyReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تقرير الحضور الشهري - قيد التطوير'),
        backgroundColor: RevolutionaryColors.infoTurquoise,
      ),
    );
  }

  void _generateWorkHoursReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تقرير ساعات العمل - قيد التطوير'),
        backgroundColor: RevolutionaryColors.infoTurquoise,
      ),
    );
  }

  void _generateAbsenceReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تقرير التأخير والغياب - قيد التطوير'),
        backgroundColor: RevolutionaryColors.infoTurquoise,
      ),
    );
  }

  void _showAttendanceStatistics() {
    final totalEmployees = _employees.length;
    final presentToday = _todayAttendance
        .where((a) => a.checkInTime != null)
        .length;
    final absentToday = totalEmployees - presentToday;
    final lateToday = _todayAttendance
        .where(
          (a) =>
              a.checkInTime != null &&
              a.checkInTime!.hour > 8, // افتراض أن الدوام يبدأ الساعة 8
        )
        .length;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إحصائيات الحضور'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildStatisticRow('إجمالي الموظفين', '$totalEmployees'),
            _buildStatisticRow('الحاضرون اليوم', '$presentToday'),
            _buildStatisticRow('الغائبون اليوم', '$absentToday'),
            _buildStatisticRow('المتأخرون اليوم', '$lateToday'),
            const Divider(),
            _buildStatisticRow(
              'نسبة الحضور',
              '${totalEmployees > 0 ? ((presentToday / totalEmployees) * 100).toStringAsFixed(1) : 0}%',
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  Widget _buildStatisticRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Text(value, style: const TextStyle(fontWeight: FontWeight.bold)),
        ],
      ),
    );
  }
}
