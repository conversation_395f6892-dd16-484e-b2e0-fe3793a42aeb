/// خدمة التكامل المحاسبي لنظام الموارد البشرية
/// توفر ربط شامل بين عمليات HR والنظام المحاسبي
library;

import '../database/database_helper.dart';
import '../models/hr_models.dart';
import '../models/account.dart';
import '../models/journal_entry.dart';
import '../services/journal_entry_service.dart';
import '../services/account_service.dart';
import '../services/logging_service.dart';
import '../services/audit_service.dart';
import '../exceptions/validation_exception.dart';
import '../constants/app_constants.dart';

class HRAccountingIntegrationService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  final JournalEntryService _journalEntryService = JournalEntryService();
  final AccountService _accountService = AccountService();

  /// إنشاء الحسابات المحاسبية المطلوبة لنظام HR
  Future<void> createHRAccounts() async {
    try {
      await _createSalaryAccounts();
      await _createTaxAccounts();
      await _createInsuranceAccounts();
      await _createLoanAccounts();
      await _createBenefitAccounts();
      await _createDeductionAccounts();

      LoggingService.info(
        'تم إنشاء الحسابات المحاسبية لنظام HR',
        category: 'HRAccountingIntegration',
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء الحسابات المحاسبية لنظام HR',
        category: 'HRAccountingIntegration',
        data: {'error': e.toString()},
      );
      rethrow;
    }
  }

  /// إنشاء حسابات الرواتب
  Future<void> _createSalaryAccounts() async {
    final accounts = [
      {
        'code': '6100',
        'name': 'مصروف الرواتب الأساسية',
        'type': AppConstants.accountTypeExpense,
        'description': 'مصروف الرواتب الأساسية للموظفين',
      },
      {
        'code': '6101',
        'name': 'مصروف البدلات',
        'type': AppConstants.accountTypeExpense,
        'description': 'مصروف البدلات والعلاوات',
      },
      {
        'code': '6102',
        'name': 'مصروف الساعات الإضافية',
        'type': AppConstants.accountTypeExpense,
        'description': 'مصروف أجور الساعات الإضافية',
      },
      {
        'code': '6103',
        'name': 'مصروف المكافآت',
        'type': AppConstants.accountTypeExpense,
        'description': 'مصروف المكافآت والحوافز',
      },
      {
        'code': '2300',
        'name': 'رواتب مستحقة الدفع',
        'type': AppConstants.accountTypeLiability,
        'description': 'الرواتب المستحقة للموظفين',
      },
    ];

    for (final accountData in accounts) {
      await _createAccountIfNotExists(accountData);
    }
  }

  /// إنشاء حسابات الضرائب
  Future<void> _createTaxAccounts() async {
    final accounts = [
      {
        'code': '2100',
        'name': 'ضريبة الدخل مستحقة الدفع',
        'type': AppConstants.accountTypeLiability,
        'description': 'ضريبة الدخل المستحقة على الموظفين',
      },
      {
        'code': '6110',
        'name': 'مصروف ضريبة الدخل',
        'type': AppConstants.accountTypeExpense,
        'description': 'مصروف ضريبة الدخل على الشركة',
      },
    ];

    for (final accountData in accounts) {
      await _createAccountIfNotExists(accountData);
    }
  }

  /// إنشاء حسابات الضمان الاجتماعي
  Future<void> _createInsuranceAccounts() async {
    final accounts = [
      {
        'code': '2200',
        'name': 'ضمان اجتماعي مستحق الدفع - موظفين',
        'type': AppConstants.accountTypeLiability,
        'description': 'اشتراك الضمان الاجتماعي للموظفين',
      },
      {
        'code': '6120',
        'name': 'مصروف الضمان الاجتماعي - صاحب العمل',
        'type': AppConstants.accountTypeExpense,
        'description': 'مصروف اشتراك صاحب العمل في الضمان الاجتماعي',
      },
    ];

    for (final accountData in accounts) {
      await _createAccountIfNotExists(accountData);
    }
  }

  /// إنشاء حسابات القروض
  Future<void> _createLoanAccounts() async {
    final accounts = [
      {
        'code': '1300',
        'name': 'قروض الموظفين',
        'type': AppConstants.accountTypeAsset,
        'description': 'القروض المقدمة للموظفين',
      },
      {
        'code': '2400',
        'name': 'أقساط قروض مستحقة التحصيل',
        'type': AppConstants.accountTypeLiability,
        'description': 'أقساط القروض المستحقة من الموظفين',
      },
    ];

    for (final accountData in accounts) {
      await _createAccountIfNotExists(accountData);
    }
  }

  /// إنشاء حسابات المزايا
  Future<void> _createBenefitAccounts() async {
    final accounts = [
      {
        'code': '6130',
        'name': 'مصروف التأمين الطبي',
        'type': AppConstants.accountTypeExpense,
        'description': 'مصروف التأمين الطبي للموظفين',
      },
      {
        'code': '6131',
        'name': 'مصروف التدريب والتطوير',
        'type': AppConstants.accountTypeExpense,
        'description': 'مصروف برامج التدريب والتطوير',
      },
      {
        'code': '6132',
        'name': 'مصروف مكافأة نهاية الخدمة',
        'type': AppConstants.accountTypeExpense,
        'description': 'مصروف مكافأة نهاية الخدمة',
      },
    ];

    for (final accountData in accounts) {
      await _createAccountIfNotExists(accountData);
    }
  }

  /// إنشاء حسابات الخصومات
  Future<void> _createDeductionAccounts() async {
    final accounts = [
      {
        'code': '2500',
        'name': 'خصومات متنوعة مستحقة',
        'type': AppConstants.accountTypeLiability,
        'description': 'الخصومات المتنوعة من الموظفين',
      },
      {
        'code': '2501',
        'name': 'غرامات وخصومات',
        'type': AppConstants.accountTypeLiability,
        'description': 'الغرامات والخصومات المطبقة على الموظفين',
      },
    ];

    for (final accountData in accounts) {
      await _createAccountIfNotExists(accountData);
    }
  }

  /// إنشاء حساب إذا لم يكن موجوداً
  Future<void> _createAccountIfNotExists(
    Map<String, dynamic> accountData,
  ) async {
    try {
      final existingAccount = await _accountService.getAccountByCode(
        accountData['code'],
      );
      if (existingAccount == null) {
        final account = Account(
          code: accountData['code'],
          name: accountData['name'],
          type: accountData['type'],
          currencyId: 1,
          description: accountData['description'],
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        await _accountService.insertAccount(account);

        LoggingService.info(
          'تم إنشاء حساب محاسبي جديد',
          category: 'HRAccountingIntegration',
          data: {'code': accountData['code'], 'name': accountData['name']},
        );
      }
    } catch (e) {
      LoggingService.warning(
        'تعذر إنشاء الحساب المحاسبي',
        category: 'HRAccountingIntegration',
        data: {'code': accountData['code'], 'error': e.toString()},
      );
    }
  }

  /// إنشاء قيد محاسبي للراتب الشهري
  Future<int> createPayrollJournalEntry({
    required List<PayrollRecord> payrollRecords,
    required DateTime payDate,
    String? description,
  }) async {
    try {
      if (payrollRecords.isEmpty) {
        throw ValidationException('لا توجد كشوف رواتب لإنشاء القيد');
      }

      // حساب الإجماليات
      double totalBasicSalary = 0;
      double totalAllowances = 0;
      double totalOvertimePay = 0;
      double totalBonuses = 0;
      double totalTaxDeductions = 0;
      double totalInsuranceDeductions = 0;
      double totalLoanDeductions = 0;
      double totalOtherDeductions = 0;
      double totalNetSalary = 0;

      for (final record in payrollRecords) {
        totalBasicSalary += record.basicSalary;
        totalAllowances += record.allowances;
        totalOvertimePay += record.overtimeAmount;
        totalBonuses += record.bonuses;
        totalTaxDeductions += record.incomeTax;
        totalInsuranceDeductions += record.socialInsurance;
        totalLoanDeductions += record.loanDeductions;
        totalOtherDeductions += record.otherDeductions;
        totalNetSalary += record.netSalary;
      }

      // الحصول على الحسابات المطلوبة
      final accounts = await _getRequiredAccounts();

      // إنشاء القيد المحاسبي
      final entryNumber = await _journalEntryService.generateEntryNumber();
      final journalEntry = JournalEntry(
        entryNumber: entryNumber,
        entryDate: payDate,
        description:
            description ?? 'قيد الرواتب لشهر ${payDate.month}/${payDate.year}',
        type: 'payroll',
        currencyId: 1,
        referenceType: 'payroll',
        referenceId: payDate.month,
      );

      // إنشاء تفاصيل القيد
      final details = await _createPayrollJournalDetails(
        accounts,
        totalBasicSalary,
        totalAllowances,
        totalOvertimePay,
        totalBonuses,
        totalTaxDeductions,
        totalInsuranceDeductions,
        totalLoanDeductions,
        totalOtherDeductions,
        totalNetSalary,
      );

      // إنشاء القيد مع التفاصيل
      final journalEntryWithDetails = journalEntry.copyWith(details: details);
      final entryId = await _journalEntryService.insertJournalEntry(
        journalEntryWithDetails,
      );

      // تسجيل في سجل المراجعة
      await AuditService.log(
        action: 'CREATE',
        entityType: 'PayrollJournalEntry',
        entityId: entryId,
        description: 'إنشاء قيد محاسبي للرواتب',
        newValues: {
          'entryId': entryId,
          'payrollCount': payrollRecords.length,
          'totalNetSalary': totalNetSalary,
          'payDate': payDate.toIso8601String(),
        },
      );

      LoggingService.info(
        'تم إنشاء قيد محاسبي للرواتب',
        category: 'HRAccountingIntegration',
        data: {
          'entryId': entryId,
          'payrollCount': payrollRecords.length,
          'totalNetSalary': totalNetSalary,
        },
      );

      return entryId;
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء قيد محاسبي للرواتب',
        category: 'HRAccountingIntegration',
        data: {'error': e.toString()},
      );
      rethrow;
    }
  }

  /// الحصول على الحسابات المطلوبة
  Future<Map<String, Account>> _getRequiredAccounts() async {
    final accounts = <String, Account>{};

    final accountCodes = {
      'basicSalary': '6100',
      'allowances': '6101',
      'overtime': '6102',
      'bonuses': '6103',
      'salaryPayable': '2300',
      'taxPayable': '2100',
      'insurancePayable': '2200',
      'loanReceivable': '1300',
      'deductionsPayable': '2500',
      'employerInsurance': '6120',
    };

    for (final entry in accountCodes.entries) {
      final account = await _accountService.getAccountByCode(entry.value);
      if (account == null) {
        throw ValidationException('الحساب المحاسبي ${entry.value} غير موجود');
      }
      accounts[entry.key] = account;
    }

    return accounts;
  }

  /// إنشاء تفاصيل القيد المحاسبي للرواتب
  Future<List<JournalEntryDetail>> _createPayrollJournalDetails(
    Map<String, Account> accounts,
    double totalBasicSalary,
    double totalAllowances,
    double totalOvertimePay,
    double totalBonuses,
    double totalTaxDeductions,
    double totalInsuranceDeductions,
    double totalLoanDeductions,
    double totalOtherDeductions,
    double totalNetSalary,
  ) async {
    final details = <JournalEntryDetail>[];

    // مدين: مصروف الرواتب الأساسية
    if (totalBasicSalary > 0) {
      details.add(
        JournalEntryDetail(
          journalEntryId: 0, // سيتم تعيينه لاحقاً
          accountId: accounts['basicSalary']!.id!,
          debitAmount: totalBasicSalary,
          creditAmount: 0,
          description: 'مصروف الرواتب الأساسية',
        ),
      );
    }

    // مدين: مصروف البدلات
    if (totalAllowances > 0) {
      details.add(
        JournalEntryDetail(
          journalEntryId: 0, // سيتم تعيينه لاحقاً
          accountId: accounts['allowances']!.id!,
          debitAmount: totalAllowances,
          creditAmount: 0,
          description: 'مصروف البدلات',
        ),
      );
    }

    // مدين: مصروف الساعات الإضافية
    if (totalOvertimePay > 0) {
      details.add(
        JournalEntryDetail(
          journalEntryId: 0, // سيتم تعيينه لاحقاً
          accountId: accounts['overtime']!.id!,
          debitAmount: totalOvertimePay,
          creditAmount: 0,
          description: 'مصروف الساعات الإضافية',
        ),
      );
    }

    // مدين: مصروف المكافآت
    if (totalBonuses > 0) {
      details.add(
        JournalEntryDetail(
          journalEntryId: 0, // سيتم تعيينه لاحقاً
          accountId: accounts['bonuses']!.id!,
          debitAmount: totalBonuses,
          creditAmount: 0,
          description: 'مصروف المكافآت',
        ),
      );
    }

    // مدين: مصروف الضمان الاجتماعي - صاحب العمل
    final employerInsurance =
        totalInsuranceDeductions * 1.5; // نسبة صاحب العمل أعلى
    if (employerInsurance > 0) {
      details.add(
        JournalEntryDetail(
          journalEntryId: 0, // سيتم تعيينه لاحقاً
          accountId: accounts['employerInsurance']!.id!,
          debitAmount: employerInsurance,
          creditAmount: 0,
          description: 'مصروف الضمان الاجتماعي - صاحب العمل',
        ),
      );
    }

    // دائن: رواتب مستحقة الدفع
    if (totalNetSalary > 0) {
      details.add(
        JournalEntryDetail(
          journalEntryId: 0, // سيتم تعيينه لاحقاً
          accountId: accounts['salaryPayable']!.id!,
          debitAmount: 0,
          creditAmount: totalNetSalary,
          description: 'رواتب مستحقة الدفع',
        ),
      );
    }

    // دائن: ضريبة الدخل مستحقة الدفع
    if (totalTaxDeductions > 0) {
      details.add(
        JournalEntryDetail(
          journalEntryId: 0, // سيتم تعيينه لاحقاً
          accountId: accounts['taxPayable']!.id!,
          debitAmount: 0,
          creditAmount: totalTaxDeductions,
          description: 'ضريبة الدخل مستحقة الدفع',
        ),
      );
    }

    // دائن: ضمان اجتماعي مستحق الدفع (موظفين + صاحب العمل)
    final totalInsurancePayable = totalInsuranceDeductions + employerInsurance;
    if (totalInsurancePayable > 0) {
      details.add(
        JournalEntryDetail(
          journalEntryId: 0, // سيتم تعيينه لاحقاً
          accountId: accounts['insurancePayable']!.id!,
          debitAmount: 0,
          creditAmount: totalInsurancePayable,
          description: 'ضمان اجتماعي مستحق الدفع',
        ),
      );
    }

    // دائن: قروض الموظفين (تحصيل أقساط)
    if (totalLoanDeductions > 0) {
      details.add(
        JournalEntryDetail(
          journalEntryId: 0, // سيتم تعيينه لاحقاً
          accountId: accounts['loanReceivable']!.id!,
          debitAmount: 0,
          creditAmount: totalLoanDeductions,
          description: 'تحصيل أقساط قروض الموظفين',
        ),
      );
    }

    // دائن: خصومات متنوعة
    if (totalOtherDeductions > 0) {
      details.add(
        JournalEntryDetail(
          journalEntryId: 0, // سيتم تعيينه لاحقاً
          accountId: accounts['deductionsPayable']!.id!,
          debitAmount: 0,
          creditAmount: totalOtherDeductions,
          description: 'خصومات متنوعة',
        ),
      );
    }

    return details;
  }

  /// إنشاء قيد محاسبي لقرض موظف
  Future<int> createLoanJournalEntry({
    required Loan loan,
    required Employee employee,
    String? description,
  }) async {
    try {
      // الحصول على الحسابات المطلوبة
      final loanAccount = await _accountService.getAccountByCode(
        '1300',
      ); // قروض الموظفين
      final cashAccount = await _accountService.getAccountByCode(
        '1001',
      ); // الصندوق

      if (loanAccount == null || cashAccount == null) {
        throw ValidationException(
          'الحسابات المحاسبية المطلوبة للقروض غير موجودة',
        );
      }

      // إنشاء القيد المحاسبي
      final entryNumber = await _journalEntryService.generateEntryNumber();
      final journalEntry = JournalEntry(
        entryNumber: entryNumber,
        entryDate: loan.createdAt,
        description: description ?? 'قرض للموظف ${employee.fullName}',
        type: 'loan',
        currencyId: 1,
        referenceType: 'loan',
        referenceId: loan.id,
      );

      // إنشاء تفاصيل القيد
      final details = [
        JournalEntryDetail(
          journalEntryId: 0, // سيتم تعيينه لاحقاً
          accountId: loanAccount.id!,
          debitAmount: loan.amount,
          creditAmount: 0,
          description: 'قرض للموظف ${employee.fullName}',
        ),
        JournalEntryDetail(
          journalEntryId: 0, // سيتم تعيينه لاحقاً
          accountId: cashAccount.id!,
          debitAmount: 0,
          creditAmount: loan.amount,
          description: 'صرف قرض للموظف ${employee.fullName}',
        ),
      ];

      // إنشاء القيد مع التفاصيل
      final journalEntryWithDetails = journalEntry.copyWith(details: details);
      final entryId = await _journalEntryService.insertJournalEntry(
        journalEntryWithDetails,
      );

      // تسجيل في سجل المراجعة
      await AuditService.log(
        action: 'CREATE',
        entityType: 'LoanJournalEntry',
        entityId: entryId,
        description: 'إنشاء قيد محاسبي لقرض موظف',
        newValues: {
          'entryId': entryId,
          'loanId': loan.id,
          'employeeId': employee.id,
          'amount': loan.amount,
        },
      );

      LoggingService.info(
        'تم إنشاء قيد محاسبي لقرض موظف',
        category: 'HRAccountingIntegration',
        data: {
          'entryId': entryId,
          'loanId': loan.id,
          'employeeId': employee.id,
          'amount': loan.amount,
        },
      );

      return entryId;
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء قيد محاسبي لقرض موظف',
        category: 'HRAccountingIntegration',
        data: {
          'loanId': loan.id,
          'employeeId': employee.id,
          'error': e.toString(),
        },
      );
      rethrow;
    }
  }

  /// إنشاء قيد محاسبي لمكافأة أو بدل
  Future<int> createBonusJournalEntry({
    required int employeeId,
    required double amount,
    required String type, // bonus, allowance, deduction
    required String description,
    DateTime? date,
  }) async {
    try {
      final entryDate = date ?? DateTime.now();

      // تحديد الحسابات حسب النوع
      String expenseAccountCode;
      String payableAccountCode;

      switch (type) {
        case 'bonus':
          expenseAccountCode = '6103'; // مصروف المكافآت
          payableAccountCode = '2300'; // رواتب مستحقة الدفع
          break;
        case 'allowance':
          expenseAccountCode = '6101'; // مصروف البدلات
          payableAccountCode = '2300'; // رواتب مستحقة الدفع
          break;
        case 'deduction':
          expenseAccountCode = '2500'; // خصومات متنوعة مستحقة
          payableAccountCode = '2300'; // رواتب مستحقة الدفع
          break;
        default:
          throw ValidationException('نوع العملية غير صحيح');
      }

      final expenseAccount = await _accountService.getAccountByCode(
        expenseAccountCode,
      );
      final payableAccount = await _accountService.getAccountByCode(
        payableAccountCode,
      );

      if (expenseAccount == null || payableAccount == null) {
        throw ValidationException('الحسابات المحاسبية المطلوبة غير موجودة');
      }

      // إنشاء القيد المحاسبي
      final entryNumber = await _journalEntryService.generateEntryNumber();
      final journalEntry = JournalEntry(
        entryNumber: entryNumber,
        entryDate: entryDate,
        description: description,
        type: type,
        currencyId: 1,
        referenceType: 'hr_transaction',
        referenceId: employeeId,
      );

      // إنشاء تفاصيل القيد
      final details = <JournalEntryDetail>[];

      if (type == 'deduction') {
        // للخصومات: مدين الخصومات، دائن الرواتب المستحقة
        details.addAll([
          JournalEntryDetail(
            journalEntryId: 0, // سيتم تعيينه لاحقاً
            accountId: expenseAccount.id!,
            debitAmount: amount,
            creditAmount: 0,
            description: description,
          ),
          JournalEntryDetail(
            journalEntryId: 0, // سيتم تعيينه لاحقاً
            accountId: payableAccount.id!,
            debitAmount: 0,
            creditAmount: amount,
            description: description,
          ),
        ]);
      } else {
        // للمكافآت والبدلات: مدين المصروف، دائن الرواتب المستحقة
        details.addAll([
          JournalEntryDetail(
            journalEntryId: 0, // سيتم تعيينه لاحقاً
            accountId: expenseAccount.id!,
            debitAmount: amount,
            creditAmount: 0,
            description: description,
          ),
          JournalEntryDetail(
            journalEntryId: 0, // سيتم تعيينه لاحقاً
            accountId: payableAccount.id!,
            debitAmount: 0,
            creditAmount: amount,
            description: description,
          ),
        ]);
      }

      // إنشاء القيد مع التفاصيل
      final journalEntryWithDetails = journalEntry.copyWith(details: details);
      final entryId = await _journalEntryService.insertJournalEntry(
        journalEntryWithDetails,
      );

      // تسجيل في سجل المراجعة
      await AuditService.log(
        action: 'CREATE',
        entityType: 'HRTransactionJournalEntry',
        entityId: entryId,
        description: 'إنشاء قيد محاسبي لمعاملة HR',
        newValues: {
          'entryId': entryId,
          'employeeId': employeeId,
          'type': type,
          'amount': amount,
        },
      );

      LoggingService.info(
        'تم إنشاء قيد محاسبي لمعاملة HR',
        category: 'HRAccountingIntegration',
        data: {
          'entryId': entryId,
          'employeeId': employeeId,
          'type': type,
          'amount': amount,
        },
      );

      return entryId;
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء قيد محاسبي لمعاملة HR',
        category: 'HRAccountingIntegration',
        data: {'employeeId': employeeId, 'type': type, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// الحصول على تقرير التكامل المحاسبي لـ HR
  Future<Map<String, dynamic>> getHRAccountingReport({
    required DateTime fromDate,
    required DateTime toDate,
  }) async {
    try {
      final db = await _databaseHelper.database;

      // إجمالي مصاريف الرواتب
      final salaryExpensesResult = await db.rawQuery(
        '''
        SELECT
          a.name as account_name,
          SUM(jed.debit_amount) as total_debit,
          SUM(jed.credit_amount) as total_credit
        FROM journal_entry_details jed
        JOIN accounts a ON jed.account_id = a.id
        JOIN journal_entries je ON jed.journal_entry_id = je.id
        WHERE a.code LIKE '61%'
        AND je.entry_date BETWEEN ? AND ?
        AND je.is_posted = 1
        GROUP BY a.id, a.name
        ORDER BY a.code
      ''',
        [fromDate.toIso8601String(), toDate.toIso8601String()],
      );

      // إجمالي الالتزامات المستحقة
      final liabilitiesResult = await db.rawQuery(
        '''
        SELECT
          a.name as account_name,
          SUM(jed.credit_amount - jed.debit_amount) as balance
        FROM journal_entry_details jed
        JOIN accounts a ON jed.account_id = a.id
        JOIN journal_entries je ON jed.journal_entry_id = je.id
        WHERE a.code LIKE '2%'
        AND je.entry_date BETWEEN ? AND ?
        AND je.is_posted = 1
        GROUP BY a.id, a.name
        HAVING balance != 0
        ORDER BY a.code
      ''',
        [fromDate.toIso8601String(), toDate.toIso8601String()],
      );

      // قروض الموظفين
      final loansResult = await db.rawQuery(
        '''
        SELECT
          SUM(jed.debit_amount - jed.credit_amount) as total_loans
        FROM journal_entry_details jed
        JOIN accounts a ON jed.account_id = a.id
        JOIN journal_entries je ON jed.journal_entry_id = je.id
        WHERE a.code = '1300'
        AND je.entry_date BETWEEN ? AND ?
        AND je.is_posted = 1
      ''',
        [fromDate.toIso8601String(), toDate.toIso8601String()],
      );

      return {
        'period': {
          'from': fromDate.toIso8601String().split('T')[0],
          'to': toDate.toIso8601String().split('T')[0],
        },
        'salaryExpenses': salaryExpensesResult,
        'liabilities': liabilitiesResult,
        'totalLoans': loansResult.first['total_loans'] ?? 0.0,
        'generatedAt': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء تقرير التكامل المحاسبي لـ HR',
        category: 'HRAccountingIntegration',
        data: {'error': e.toString()},
      );
      rethrow;
    }
  }
}
