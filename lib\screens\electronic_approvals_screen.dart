/// شاشة الموافقات الإلكترونية
/// توفر واجهة شاملة لإدارة طلبات الموافقة الإلكترونية
library;

import 'package:flutter/material.dart';
import '../models/approval_models.dart';
import '../services/approval_service.dart';
import '../services/logging_service.dart';
import '../widgets/loading_widget.dart';
import '../constants/revolutionary_design_colors.dart';

class ElectronicApprovalsScreen extends StatefulWidget {
  const ElectronicApprovalsScreen({super.key});

  @override
  State<ElectronicApprovalsScreen> createState() => _ElectronicApprovalsScreenState();
}

class _ElectronicApprovalsScreenState extends State<ElectronicApprovalsScreen>
    with TickerProviderStateMixin {
  final ApprovalService _approvalService = ApprovalService();
  final TextEditingController _searchController = TextEditingController();

  late TabController _tabController;
  List<ApprovalRequest> _allRequests = [];
  List<ApprovalRequest> _myRequests = [];
  List<ApprovalRequest> _pendingApprovals = [];
  bool _isLoading = false;
  String _selectedStatus = 'all';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadApprovalRequests();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  /// تحميل طلبات الموافقة
  Future<void> _loadApprovalRequests() async {
    setState(() => _isLoading = true);

    try {
      // تحميل جميع الطلبات
      final allRequests = await _approvalService.getAllApprovalRequests();
      
      // تحميل طلباتي
      final myRequests = await _approvalService.getApprovalRequestsForRequester(1); // TODO: استخدام معرف المستخدم الحالي
      
      // تحميل الطلبات المعلقة للموافقة
      final pendingApprovals = await _approvalService.getApprovalRequestsForApprover(1); // TODO: استخدام معرف المستخدم الحالي

      setState(() {
        _allRequests = allRequests;
        _myRequests = myRequests;
        _pendingApprovals = pendingApprovals;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      LoggingService.error(
        'خطأ في تحميل طلبات الموافقة',
        category: 'ElectronicApprovalsScreen',
        data: {'error': e.toString()},
      );
      _showErrorSnackBar('حدث خطأ في تحميل طلبات الموافقة: $e');
    }
  }

  /// إنشاء طلب موافقة جديد
  Future<void> _createNewApprovalRequest() async {
    final result = await showDialog<Map<String, dynamic>>(
      context: context,
      builder: (context) => _CreateApprovalRequestDialog(),
    );

    if (result != null) {
      try {
        await _approvalService.createEnhancedApprovalRequest(
          title: result['title'],
          description: result['description'],
          requestType: result['type'],
          requestedBy: 1, // TODO: استخدام معرف المستخدم الحالي
          requesterName: 'المستخدم الحالي', // TODO: استخدام اسم المستخدم الحالي
          requestData: result['data'] ?? {},
          priority: result['priority'] ?? 'medium',
          dueDate: result['dueDate'],
        );

        _showSuccessSnackBar('تم إنشاء طلب الموافقة بنجاح');
        _loadApprovalRequests();
      } catch (e) {
        _showErrorSnackBar('حدث خطأ في إنشاء طلب الموافقة: $e');
      }
    }
  }

  /// الموافقة على طلب
  Future<void> _approveRequest(ApprovalRequest request) async {
    final comments = await _showCommentsDialog('تعليقات الموافقة');
    if (comments != null) {
      try {
        await _approvalService.approveEnhancedRequest(
          requestId: request.id!,
          approverId: 1, // TODO: استخدام معرف المستخدم الحالي
          approverName: 'المستخدم الحالي', // TODO: استخدام اسم المستخدم الحالي
          comments: comments,
        );

        _showSuccessSnackBar('تم الموافقة على الطلب بنجاح');
        _loadApprovalRequests();
      } catch (e) {
        _showErrorSnackBar('حدث خطأ في الموافقة على الطلب: $e');
      }
    }
  }

  /// رفض طلب
  Future<void> _rejectRequest(ApprovalRequest request) async {
    final comments = await _showCommentsDialog('سبب الرفض', required: true);
    if (comments != null) {
      try {
        await _approvalService.rejectRequest(
          requestId: request.id!,
          approverId: 1, // TODO: استخدام معرف المستخدم الحالي
          approverName: 'المستخدم الحالي', // TODO: استخدام اسم المستخدم الحالي
          comments: comments,
        );

        _showSuccessSnackBar('تم رفض الطلب');
        _loadApprovalRequests();
      } catch (e) {
        _showErrorSnackBar('حدث خطأ في رفض الطلب: $e');
      }
    }
  }

  /// عرض حوار التعليقات
  Future<String?> _showCommentsDialog(String title, {bool required = false}) async {
    final controller = TextEditingController();
    return showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: TextField(
          controller: controller,
          maxLines: 3,
          decoration: InputDecoration(
            hintText: 'أدخل تعليقاتك هنا...',
            border: const OutlineInputBorder(),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              if (required && controller.text.trim().isEmpty) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('التعليق مطلوب')),
                );
                return;
              }
              Navigator.pop(context, controller.text.trim());
            },
            child: const Text('تأكيد'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('الموافقات الإلكترونية'),
        backgroundColor: RevolutionaryDesignColors.primaryColor,
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(text: 'جميع الطلبات', icon: Icon(Icons.list)),
            Tab(text: 'طلباتي', icon: Icon(Icons.person)),
            Tab(text: 'للموافقة', icon: Icon(Icons.approval)),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: _createNewApprovalRequest,
            tooltip: 'طلب موافقة جديد',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadApprovalRequests,
            tooltip: 'تحديث',
          ),
        ],
      ),
      body: _isLoading
          ? const LoadingWidget()
          : TabBarView(
              controller: _tabController,
              children: [
                _buildRequestsList(_allRequests, showActions: false),
                _buildRequestsList(_myRequests, showActions: false),
                _buildRequestsList(_pendingApprovals, showActions: true),
              ],
            ),
    );
  }

  /// بناء قائمة الطلبات
  Widget _buildRequestsList(List<ApprovalRequest> requests, {required bool showActions}) {
    if (requests.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.inbox, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text('لا توجد طلبات', style: TextStyle(fontSize: 18, color: Colors.grey)),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: requests.length,
      itemBuilder: (context, index) {
        final request = requests[index];
        return _buildRequestCard(request, showActions: showActions);
      },
    );
  }

  /// بناء بطاقة الطلب
  Widget _buildRequestCard(ApprovalRequest request, {required bool showActions}) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    request.title,
                    style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                ),
                _buildStatusChip(request.status),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              request.description,
              style: const TextStyle(color: Colors.grey),
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(Icons.person, size: 16, color: Colors.grey),
                const SizedBox(width: 4),
                Text(request.requesterName, style: const TextStyle(fontSize: 12)),
                const SizedBox(width: 16),
                Icon(Icons.access_time, size: 16, color: Colors.grey),
                const SizedBox(width: 4),
                Text(
                  _formatDate(request.createdAt),
                  style: const TextStyle(fontSize: 12),
                ),
              ],
            ),
            if (showActions && request.status == ApprovalStatus.pending.value) ...[
              const SizedBox(height: 12),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton.icon(
                    onPressed: () => _rejectRequest(request),
                    icon: const Icon(Icons.close, color: Colors.red),
                    label: const Text('رفض', style: TextStyle(color: Colors.red)),
                  ),
                  const SizedBox(width: 8),
                  ElevatedButton.icon(
                    onPressed: () => _approveRequest(request),
                    icon: const Icon(Icons.check),
                    label: const Text('موافقة'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// بناء شريحة الحالة
  Widget _buildStatusChip(String status) {
    Color color;
    String text;
    
    switch (status) {
      case 'pending':
        color = Colors.orange;
        text = 'في الانتظار';
        break;
      case 'approved':
        color = Colors.green;
        text = 'موافق عليه';
        break;
      case 'rejected':
        color = Colors.red;
        text = 'مرفوض';
        break;
      default:
        color = Colors.grey;
        text = status;
    }

    return Chip(
      label: Text(text, style: const TextStyle(color: Colors.white, fontSize: 12)),
      backgroundColor: color,
    );
  }

  /// تنسيق التاريخ
  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  /// عرض رسالة نجاح
  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: Colors.green),
    );
  }

  /// عرض رسالة خطأ
  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: Colors.red),
    );
  }
}

/// حوار إنشاء طلب موافقة جديد
class _CreateApprovalRequestDialog extends StatefulWidget {
  @override
  State<_CreateApprovalRequestDialog> createState() => _CreateApprovalRequestDialogState();
}

class _CreateApprovalRequestDialogState extends State<_CreateApprovalRequestDialog> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  String _selectedType = 'leave';
  String _selectedPriority = 'medium';
  DateTime? _dueDate;

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('طلب موافقة جديد'),
      content: Form(
        key: _formKey,
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextFormField(
                controller: _titleController,
                decoration: const InputDecoration(
                  labelText: 'عنوان الطلب',
                  border: OutlineInputBorder(),
                ),
                validator: (value) => value?.isEmpty == true ? 'العنوان مطلوب' : null,
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _descriptionController,
                decoration: const InputDecoration(
                  labelText: 'وصف الطلب',
                  border: OutlineInputBorder(),
                ),
                maxLines: 3,
                validator: (value) => value?.isEmpty == true ? 'الوصف مطلوب' : null,
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<String>(
                value: _selectedType,
                decoration: const InputDecoration(
                  labelText: 'نوع الطلب',
                  border: OutlineInputBorder(),
                ),
                items: const [
                  DropdownMenuItem(value: 'leave', child: Text('إجازة')),
                  DropdownMenuItem(value: 'expense', child: Text('مصروف')),
                  DropdownMenuItem(value: 'purchase', child: Text('مشتريات')),
                  DropdownMenuItem(value: 'salary_change', child: Text('تغيير راتب')),
                  DropdownMenuItem(value: 'promotion', child: Text('ترقية')),
                ],
                onChanged: (value) => setState(() => _selectedType = value!),
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<String>(
                value: _selectedPriority,
                decoration: const InputDecoration(
                  labelText: 'الأولوية',
                  border: OutlineInputBorder(),
                ),
                items: const [
                  DropdownMenuItem(value: 'low', child: Text('منخفضة')),
                  DropdownMenuItem(value: 'medium', child: Text('متوسطة')),
                  DropdownMenuItem(value: 'high', child: Text('عالية')),
                  DropdownMenuItem(value: 'urgent', child: Text('عاجلة')),
                ],
                onChanged: (value) => setState(() => _selectedPriority = value!),
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: () {
            if (_formKey.currentState!.validate()) {
              Navigator.pop(context, {
                'title': _titleController.text,
                'description': _descriptionController.text,
                'type': _selectedType,
                'priority': _selectedPriority,
                'dueDate': _dueDate,
                'data': {},
              });
            }
          },
          child: const Text('إنشاء'),
        ),
      ],
    );
  }
}
