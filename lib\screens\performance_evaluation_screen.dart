/// شاشة تقييم الأداء
/// توفر واجهة شاملة لإدارة تقييم أداء الموظفين
library;

import 'package:flutter/material.dart';

import '../models/hr_models.dart';
import '../services/performance_evaluation_service.dart';
import '../services/employee_service.dart';
import '../services/logging_service.dart';
import '../widgets/loading_widget.dart';
import '../widgets/custom_error_widget.dart' as custom_widgets;
import '../constants/revolutionary_design_colors.dart';

class PerformanceEvaluationScreen extends StatefulWidget {
  const PerformanceEvaluationScreen({super.key});

  @override
  State<PerformanceEvaluationScreen> createState() =>
      _PerformanceEvaluationScreenState();
}

class _PerformanceEvaluationScreenState
    extends State<PerformanceEvaluationScreen>
    with TickerProviderStateMixin {
  final PerformanceEvaluationService _evaluationService =
      PerformanceEvaluationService();
  final EmployeeService _employeeService = EmployeeService();

  late TabController _tabController;
  List<PerformanceCycle> _cycles = [];
  List<PerformanceCycle> _filteredCycles = [];
  List<PerformanceEvaluation> _evaluations = [];
  List<EvaluationCriteria> _criteria = [];
  List<EvaluationCriteria> _filteredCriteria = [];
  List<Employee> _employees = [];
  Map<String, dynamic> _statistics = {};

  bool _isLoading = true;
  String? _error;
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final results = await Future.wait([
        _evaluationService.getAllEvaluationCycles(),
        _evaluationService.getEmployeeEvaluations(),
        _evaluationService.getAllEvaluationCriteria(),
        _evaluationService.getEvaluationStatistics(),
        _employeeService.getAllEmployees(),
      ]);

      setState(() {
        _cycles = results[0] as List<PerformanceCycle>;
        _filteredCycles = List.from(_cycles);
        _evaluations = results[1] as List<PerformanceEvaluation>;
        _criteria = results[2] as List<EvaluationCriteria>;
        _filteredCriteria = List.from(_criteria);
        _statistics = results[3] as Map<String, dynamic>;
        _employees = results[4] as List<Employee>;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });

      LoggingService.error(
        'خطأ في تحميل بيانات تقييم الأداء',
        category: 'PerformanceEvaluationScreen',
        data: {'error': e.toString()},
      );
    }
  }

  /// تطبيق البحث في دورات التقييم
  void _filterCycles(String query) {
    setState(() {
      _searchQuery = query;
      if (query.isEmpty) {
        _filteredCycles = List.from(_cycles);
      } else {
        _filteredCycles = _cycles.where((cycle) {
          final searchLower = query.toLowerCase();
          return cycle.cycleName.toLowerCase().contains(searchLower) ||
              (cycle.description?.toLowerCase().contains(searchLower) ??
                  false) ||
              cycle.status.toLowerCase().contains(searchLower);
        }).toList();
      }
    });
  }

  /// تطبيق البحث في معايير التقييم
  void _filterCriteria(String query) {
    setState(() {
      if (query.isEmpty) {
        _filteredCriteria = List.from(_criteria);
      } else {
        _filteredCriteria = _criteria.where((criteria) {
          final searchLower = query.toLowerCase();
          return criteria.criteriaName.toLowerCase().contains(searchLower) ||
              (criteria.description?.toLowerCase().contains(searchLower) ??
                  false) ||
              criteria.weight.toString().contains(query) ||
              criteria.maxScore.toString().contains(query);
        }).toList();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تقييم الأداء'),
        backgroundColor: RevolutionaryColors.damascusSky,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadData,
            tooltip: 'تحديث',
          ),
          PopupMenuButton<String>(
            icon: const Icon(Icons.more_vert),
            onSelected: (value) {
              switch (value) {
                case 'add_cycle':
                  _showAddCycleDialog();
                  break;
                case 'add_criteria':
                  _showAddCriteriaDialog();
                  break;
                case 'create_defaults':
                  _createDefaultCriteria();
                  break;
                case 'export':
                  _exportEvaluationData();
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'add_cycle',
                child: Text('إضافة دورة تقييم'),
              ),
              const PopupMenuItem(
                value: 'add_criteria',
                child: Text('إضافة معيار تقييم'),
              ),
              const PopupMenuItem(
                value: 'create_defaults',
                child: Text('إنشاء المعايير الافتراضية'),
              ),
              const PopupMenuItem(
                value: 'export',
                child: Text('تصدير البيانات'),
              ),
            ],
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          isScrollable: true,
          tabs: const [
            Tab(icon: Icon(Icons.dashboard), text: 'نظرة عامة'),
            Tab(icon: Icon(Icons.refresh), text: 'دورات التقييم'),
            Tab(icon: Icon(Icons.assessment), text: 'التقييمات'),
            Tab(icon: Icon(Icons.rule), text: 'معايير التقييم'),
            Tab(icon: Icon(Icons.analytics), text: 'التقارير'),
          ],
        ),
      ),
      body: _buildBody(),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const LoadingWidget();
    }

    if (_error != null) {
      return custom_widgets.ErrorWidget(message: _error!, onRetry: _loadData);
    }

    return TabBarView(
      controller: _tabController,
      children: [
        _buildOverviewTab(),
        _buildCyclesTab(),
        _buildEvaluationsTab(),
        _buildCriteriaTab(),
        _buildReportsTab(),
      ],
    );
  }

  Widget _buildOverviewTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildStatisticsCards(),
          const SizedBox(height: 24),
          _buildQuickActions(),
          const SizedBox(height: 24),
          _buildRecentEvaluations(),
        ],
      ),
    );
  }

  Widget _buildStatisticsCards() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'إحصائيات تقييم الأداء',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
          childAspectRatio: 1.5,
          children: [
            _buildStatCard(
              'دورات التقييم',
              '${_statistics['totalCycles'] ?? 0}',
              Icons.refresh,
              RevolutionaryColors.damascusSky,
            ),
            _buildStatCard(
              'التقييمات المكتملة',
              '${_statistics['completedEvaluations'] ?? 0}',
              Icons.check_circle,
              RevolutionaryColors.successGlow,
            ),
            _buildStatCard(
              'معدل الإكمال',
              '${_statistics['completionRate'] ?? '0.0'}%',
              Icons.trending_up,
              RevolutionaryColors.infoTurquoise,
            ),
            _buildStatCard(
              'متوسط النتائج',
              (_statistics['averageScore'] as double? ?? 0.0).toStringAsFixed(
                1,
              ),
              Icons.star,
              RevolutionaryColors.warningAmber,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: 32, color: color),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: const TextStyle(fontSize: 12),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'الإجراءات السريعة',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 12,
          runSpacing: 12,
          children: [
            _buildActionCard(
              'إضافة دورة تقييم',
              Icons.add_circle,
              RevolutionaryColors.successGlow,
              _showAddCycleDialog,
            ),
            _buildActionCard(
              'إنشاء تقييم',
              Icons.assessment,
              RevolutionaryColors.infoTurquoise,
              _showCreateEvaluationDialog,
            ),
            _buildActionCard(
              'إضافة معيار مخصص',
              Icons.rule,
              RevolutionaryColors.warningAmber,
              _showAddCustomCriteriaDialog,
            ),
            _buildActionCard(
              'تقرير الأداء',
              Icons.analytics,
              RevolutionaryColors.damascusSky,
              _generatePerformanceReport,
            ),
            _buildActionCard(
              'تقرير المقارنة',
              Icons.compare_arrows,
              RevolutionaryColors.successGlow,
              _showComparisonReportDialog,
            ),
            _buildActionCard(
              'التحليل الزمني',
              Icons.timeline,
              RevolutionaryColors.infoTurquoise,
              _showTrendReportDialog,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildActionCard(
    String title,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        width: (MediaQuery.of(context).size.width - 56) / 2,
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: color.withValues(alpha: 0.3)),
        ),
        child: Row(
          children: [
            Icon(icon, color: color, size: 24),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                title,
                style: TextStyle(fontWeight: FontWeight.w600, color: color),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentEvaluations() {
    final recentEvaluations = _evaluations.take(5).toList();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'التقييمات الحديثة',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),
        if (recentEvaluations.isEmpty)
          const Center(
            child: Padding(
              padding: EdgeInsets.all(32),
              child: Text(
                'لا توجد تقييمات',
                style: TextStyle(color: Colors.grey),
              ),
            ),
          )
        else
          ...recentEvaluations.map((evaluation) {
            final employee = _employees.firstWhere(
              (emp) => emp.id == evaluation.employeeId,
              orElse: () => Employee(
                employeeNumber: 'غير معروف',
                nationalId: '',
                firstName: 'غير معروف',
                lastName: '',
                fullName: 'غير معروف',
                email: '',
                phone: '',
                hireDate: DateTime.now(),
                basicSalary: 0,
                createdAt: DateTime.now(),
                updatedAt: DateTime.now(),
              ),
            );

            return Card(
              margin: const EdgeInsets.only(bottom: 8),
              child: ListTile(
                leading: CircleAvatar(
                  backgroundColor: RevolutionaryColors.damascusSky.withValues(
                    alpha: 0.1,
                  ),
                  child: Text(
                    employee.firstName.isNotEmpty ? employee.firstName[0] : '؟',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      color: RevolutionaryColors.damascusSky,
                    ),
                  ),
                ),
                title: Text('${employee.firstName} ${employee.lastName}'),
                subtitle: Text(evaluation.status),
                trailing: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      '${evaluation.totalScore.toStringAsFixed(1)}%',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        color: RevolutionaryColors.successGlow,
                      ),
                    ),
                    Text(
                      _getStatusText(evaluation.status),
                      style: TextStyle(
                        fontSize: 12,
                        color: _getStatusColor(evaluation.status),
                      ),
                    ),
                  ],
                ),
                onTap: () => _showEvaluationDetails(evaluation, employee),
              ),
            );
          }),
      ],
    );
  }

  Widget _buildCyclesTab() {
    return Column(
      children: [
        // شريط البحث والفلترة
        Container(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Expanded(
                child: TextField(
                  decoration: const InputDecoration(
                    hintText: 'البحث في دورات التقييم...',
                    prefixIcon: Icon(Icons.search),
                    border: OutlineInputBorder(),
                  ),
                  onChanged: _filterCycles,
                ),
              ),
              const SizedBox(width: 12),
              ElevatedButton.icon(
                onPressed: _showAddCycleDialog,
                icon: const Icon(Icons.add),
                label: const Text('إضافة دورة'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: RevolutionaryColors.successGlow,
                  foregroundColor: Colors.white,
                ),
              ),
            ],
          ),
        ),
        // قائمة الدورات
        Expanded(
          child: _filteredCycles.isEmpty
              ? Center(
                  child: Text(
                    _searchQuery.isEmpty
                        ? 'لا توجد دورات تقييم'
                        : 'لا توجد نتائج للبحث "$_searchQuery"',
                    style: const TextStyle(color: Colors.grey),
                  ),
                )
              : ListView.builder(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  itemCount: _filteredCycles.length,
                  itemBuilder: (context, index) {
                    final cycle = _filteredCycles[index];
                    return _buildCycleCard(cycle);
                  },
                ),
        ),
      ],
    );
  }

  Widget _buildCycleCard(PerformanceCycle cycle) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ExpansionTile(
        leading: Icon(
          _getCycleIcon(cycle.status),
          color: _getStatusColor(cycle.status),
        ),
        title: Text(
          cycle.cycleName,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Text(
          '${cycle.startDate.day}/${cycle.startDate.month}/${cycle.startDate.year} - ${cycle.endDate.day}/${cycle.endDate.month}/${cycle.endDate.year}',
        ),
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (cycle.description != null)
                  Text(
                    cycle.description!,
                    style: const TextStyle(fontSize: 14),
                  ),
                const SizedBox(height: 12),
                Row(
                  children: [
                    Expanded(child: _buildCycleDetail('الحالة', cycle.status)),
                    Expanded(
                      child: _buildCycleDetail(
                        'المدة',
                        '${cycle.endDate.difference(cycle.startDate).inDays} يوم',
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(
                      child: _buildCycleDetail(
                        'الحالة',
                        _getStatusText(cycle.status),
                      ),
                    ),
                    Expanded(
                      child: _buildCycleDetail(
                        'نشط',
                        cycle.isActive ? 'نعم' : 'لا',
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    ElevatedButton.icon(
                      onPressed: () => _createEvaluationsForCycle(cycle),
                      icon: const Icon(Icons.add),
                      label: const Text('إنشاء تقييمات'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: RevolutionaryColors.successGlow,
                        foregroundColor: Colors.white,
                      ),
                    ),
                    const SizedBox(width: 12),
                    OutlinedButton.icon(
                      onPressed: () => _editCycle(cycle),
                      icon: const Icon(Icons.edit),
                      label: const Text('تعديل'),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCycleDetail(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 2),
        Text(
          value,
          style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
        ),
      ],
    );
  }

  Widget _buildEvaluationsTab() {
    return const Center(child: Text('تبويب التقييمات - قيد التطوير'));
  }

  Widget _buildCriteriaTab() {
    return Column(
      children: [
        // شريط البحث والإضافة
        Container(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Expanded(
                child: TextField(
                  decoration: const InputDecoration(
                    hintText: 'البحث في معايير التقييم...',
                    prefixIcon: Icon(Icons.search),
                    border: OutlineInputBorder(),
                  ),
                  onChanged: _filterCriteria,
                ),
              ),
              const SizedBox(width: 12),
              ElevatedButton.icon(
                onPressed: _showAddCriteriaDialog,
                icon: const Icon(Icons.add),
                label: const Text('إضافة معيار'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: RevolutionaryColors.warningAmber,
                  foregroundColor: Colors.white,
                ),
              ),
            ],
          ),
        ),
        // قائمة المعايير
        Expanded(
          child: _filteredCriteria.isEmpty
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(Icons.rule, size: 64, color: Colors.grey),
                      const SizedBox(height: 16),
                      Text(
                        _criteria.isEmpty
                            ? 'لا توجد معايير تقييم'
                            : 'لا توجد نتائج للبحث',
                        style: const TextStyle(
                          fontSize: 18,
                          color: Colors.grey,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        _criteria.isEmpty
                            ? 'يمكنك إضافة معايير جديدة أو إنشاء المعايير الافتراضية'
                            : 'جرب البحث بكلمات مختلفة',
                        style: const TextStyle(color: Colors.grey),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                )
              : ListView.builder(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  itemCount: _filteredCriteria.length,
                  itemBuilder: (context, index) {
                    final criteria = _filteredCriteria[index];
                    return _buildCriteriaCard(criteria);
                  },
                ),
        ),
      ],
    );
  }

  Widget _buildCriteriaCard(EvaluationCriteria criteria) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ExpansionTile(
        leading: Icon(Icons.rule, color: RevolutionaryColors.warningAmber),
        title: Text(
          criteria.criteriaName,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Text(
          criteria.description ?? '',
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: _buildCriteriaDetail(
                        'الوزن',
                        '${(criteria.weight * 100).toStringAsFixed(1)}%',
                      ),
                    ),
                    Expanded(
                      child: _buildCriteriaDetail('النوع', 'معيار تقييم'),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(
                      child: _buildCriteriaDetail(
                        'أقصى درجة',
                        '${criteria.maxScore}',
                      ),
                    ),
                    Expanded(child: _buildCriteriaDetail('نشط', 'نعم')),
                  ],
                ),
                const SizedBox(height: 8),
                _buildCriteriaDetail('الفئة', 'عام'),
                const SizedBox(height: 16),
                Row(
                  children: [
                    OutlinedButton.icon(
                      onPressed: () => _editCriteria(criteria),
                      icon: const Icon(Icons.edit),
                      label: const Text('تعديل'),
                    ),
                    const SizedBox(width: 12),
                    OutlinedButton.icon(
                      onPressed: () => _toggleCriteriaStatus(criteria),
                      icon: const Icon(Icons.visibility_off),
                      label: const Text('إلغاء التفعيل'),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCriteriaDetail(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 2),
        Text(
          value,
          style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
        ),
      ],
    );
  }

  Widget _buildReportsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildReportFilters(),
          const SizedBox(height: 24),
          _buildReportSummary(),
          const SizedBox(height: 24),
          _buildPerformanceCharts(),
          const SizedBox(height: 24),
          _buildDetailedReports(),
        ],
      ),
    );
  }

  Widget _buildReportFilters() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'فلاتر التقارير',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: DropdownButtonFormField<int>(
                    decoration: const InputDecoration(
                      labelText: 'دورة التقييم',
                      border: OutlineInputBorder(),
                    ),
                    items: _cycles.map((cycle) {
                      return DropdownMenuItem(
                        value: cycle.id,
                        child: Text(cycle.cycleName),
                      );
                    }).toList(),
                    onChanged: (value) {
                      // تطبيق فلتر دورة التقييم
                      _filterEvaluationsByCycle(value);
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: DropdownButtonFormField<int>(
                    decoration: const InputDecoration(
                      labelText: 'الموظف',
                      border: OutlineInputBorder(),
                    ),
                    items: _employees.map((employee) {
                      return DropdownMenuItem(
                        value: employee.id,
                        child: Text(employee.displayName),
                      );
                    }).toList(),
                    onChanged: (value) {
                      // تطبيق فلتر الموظف
                      _filterEvaluationsByEmployee(value);
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                ElevatedButton.icon(
                  onPressed: _generateReport,
                  icon: const Icon(Icons.analytics),
                  label: const Text('إنشاء تقرير'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: RevolutionaryColors.damascusSky,
                  ),
                ),
                const SizedBox(width: 16),
                ElevatedButton.icon(
                  onPressed: _exportReport,
                  icon: const Icon(Icons.download),
                  label: const Text('تصدير'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: RevolutionaryColors.successGlow,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildReportSummary() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'ملخص الأداء',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            GridView.count(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              crossAxisCount: 4,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
              childAspectRatio: 1.5,
              children: [
                _buildSummaryCard(
                  'إجمالي التقييمات',
                  '${_evaluations.length}',
                  Icons.assessment,
                  RevolutionaryColors.damascusSky,
                ),
                _buildSummaryCard(
                  'التقييمات المكتملة',
                  '${_evaluations.where((e) => e.isCompleted).length}',
                  Icons.check_circle,
                  RevolutionaryColors.successGlow,
                ),
                _buildSummaryCard(
                  'متوسط النتائج',
                  _calculateAverageScore().toStringAsFixed(1),
                  Icons.trending_up,
                  RevolutionaryColors.warningAmber,
                ),
                _buildSummaryCard(
                  'دورات نشطة',
                  '${_cycles.where((c) => c.isActive).length}',
                  Icons.play_circle,
                  RevolutionaryColors.infoTurquoise,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, color: color, size: 32),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: const TextStyle(fontSize: 12),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget? _buildFloatingActionButton() {
    switch (_tabController.index) {
      case 1: // دورات التقييم
        return FloatingActionButton(
          onPressed: _showAddCycleDialog,
          backgroundColor: RevolutionaryColors.successGlow,
          child: const Icon(Icons.add, color: Colors.white),
        );
      case 2: // التقييمات
        return FloatingActionButton(
          onPressed: _showCreateEvaluationDialog,
          backgroundColor: RevolutionaryColors.infoTurquoise,
          child: const Icon(Icons.assessment, color: Colors.white),
        );
      case 3: // معايير التقييم
        return FloatingActionButton(
          onPressed: _showAddCriteriaDialog,
          backgroundColor: RevolutionaryColors.warningAmber,
          child: const Icon(Icons.rule, color: Colors.white),
        );
      default:
        return null;
    }
  }

  // دوال مساعدة
  IconData _getCycleIcon(String status) {
    switch (status) {
      case 'active':
        return Icons.play_circle;
      case 'completed':
        return Icons.check_circle;
      case 'cancelled':
        return Icons.cancel;
      default:
        return Icons.description;
    }
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'active':
        return RevolutionaryColors.successGlow;
      case 'completed':
        return RevolutionaryColors.infoTurquoise;
      case 'cancelled':
        return RevolutionaryColors.errorCoral;
      case 'approved':
        return RevolutionaryColors.successGlow;
      case 'submitted':
        return RevolutionaryColors.warningAmber;
      case 'reviewed':
        return RevolutionaryColors.infoTurquoise;
      case 'rejected':
        return RevolutionaryColors.errorCoral;
      default:
        return Colors.grey;
    }
  }

  String _getStatusText(String status) {
    switch (status) {
      case 'draft':
        return 'مسودة';
      case 'active':
        return 'نشط';
      case 'completed':
        return 'مكتمل';
      case 'cancelled':
        return 'ملغي';
      case 'submitted':
        return 'مرسل';
      case 'reviewed':
        return 'تمت المراجعة';
      case 'approved':
        return 'معتمد';
      case 'rejected':
        return 'مرفوض';
      default:
        return status;
    }
  }

  // دوال الأحداث
  void _showAddCycleDialog() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('حوار إضافة دورة تقييم - قيد التطوير'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  void _showAddCriteriaDialog() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('حوار إضافة معيار تقييم - قيد التطوير'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  void _showCreateEvaluationDialog() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('حوار إنشاء تقييم - قيد التطوير'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  void _createDefaultCriteria() async {
    try {
      await _evaluationService.createDefaultCriteria();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إنشاء معايير التقييم الافتراضية بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
        _loadData();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إنشاء المعايير: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _createEvaluationsForCycle(PerformanceCycle cycle) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('إنشاء تقييمات للدورة: ${cycle.cycleName}'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  void _editCycle(PerformanceCycle cycle) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تعديل الدورة: ${cycle.cycleName}'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  void _generatePerformanceReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم تحديث تقرير الأداء'),
        backgroundColor: Colors.blue,
      ),
    );
    _loadData();
  }

  void _exportEvaluationData() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تصدير بيانات التقييم - قيد التطوير'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  void _showEvaluationDetails(
    PerformanceEvaluation evaluation,
    Employee employee,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('تقييم ${employee.firstName} ${employee.lastName}'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildDetailRow(
                'النتيجة الإجمالية',
                '${evaluation.totalScore.toStringAsFixed(1)}%',
              ),
              _buildDetailRow('الحالة', _getStatusText(evaluation.status)),
              if (evaluation.selfEvaluation != null) ...[
                const Divider(),
                const Text(
                  'التقييم الذاتي:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                Text(evaluation.selfEvaluation!),
              ],
              if (evaluation.managerComments != null) ...[
                const Divider(),
                const Text(
                  'تعليقات المدير:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                Text(evaluation.managerComments!),
              ],
              if (evaluation.goalsNextPeriod != null) ...[
                const Divider(),
                const Text(
                  'أهداف الفترة القادمة:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                Text(evaluation.goalsNextPeriod!),
              ],
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label, style: const TextStyle(fontWeight: FontWeight.w500)),
          Text(value, style: const TextStyle(fontWeight: FontWeight.bold)),
        ],
      ),
    );
  }

  void _editCriteria(EvaluationCriteria criteria) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تعديل المعيار: ${criteria.criteriaName}'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  void _toggleCriteriaStatus(EvaluationCriteria criteria) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تبديل حالة المعيار: ${criteria.criteriaName}'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  // دوال التقارير المفقودة

  Widget _buildPerformanceCharts() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'الرسوم البيانية للأداء',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Container(
              height: 300,
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.bar_chart, size: 64, color: Colors.grey),
                    SizedBox(height: 16),
                    Text(
                      'الرسوم البيانية قيد التطوير',
                      style: TextStyle(color: Colors.grey),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailedReports() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'التقارير التفصيلية',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _evaluations.take(5).length,
              itemBuilder: (context, index) {
                final evaluation = _evaluations[index];
                final employee = _employees.firstWhere(
                  (e) => e.id == evaluation.employeeId,
                  orElse: () => Employee(
                    employeeNumber: '',
                    nationalId: '',
                    firstName: 'غير معروف',
                    lastName: '',
                    fullName: 'غير معروف',
                    hireDate: DateTime.now(),
                    createdAt: DateTime.now(),
                    updatedAt: DateTime.now(),
                  ),
                );

                return ListTile(
                  leading: CircleAvatar(
                    backgroundColor: _getScoreColor(evaluation.totalScore),
                    child: Text(
                      evaluation.totalScore.toStringAsFixed(0),
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  title: Text(employee.displayName),
                  subtitle: Text(
                    'تاريخ الإنشاء: ${evaluation.createdAt.day}/${evaluation.createdAt.month}/${evaluation.createdAt.year}',
                  ),
                  trailing: Chip(
                    label: Text(_getStatusText(evaluation.status)),
                    backgroundColor: _getStatusColor(evaluation.status),
                  ),
                  onTap: () => _showEvaluationDetails(evaluation, employee),
                );
              },
            ),
            if (_evaluations.length > 5) ...[
              const SizedBox(height: 16),
              Center(
                child: TextButton(
                  onPressed: () {
                    // عرض جميع التقييمات
                  },
                  child: const Text('عرض جميع التقييمات'),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  void _filterEvaluationsByCycle(int? cycleId) {
    setState(() {
      if (cycleId == null) {
        // عرض جميع التقييمات
        _loadEvaluations();
      } else {
        // فلترة التقييمات حسب دورة التقييم
        _loadEvaluations(cycleId: cycleId);
      }
    });
  }

  void _filterEvaluationsByEmployee(int? employeeId) {
    setState(() {
      if (employeeId == null) {
        // عرض جميع التقييمات
        _loadEvaluations();
      } else {
        // فلترة التقييمات حسب الموظف
        _loadEvaluations(employeeId: employeeId);
      }
    });
  }

  void _generateReport() async {
    try {
      final report = await _evaluationService.generateEvaluationReport();

      if (mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('تقرير الأداء'),
            content: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    'إجمالي التقييمات: ${report['statistics']['totalEvaluations']}',
                  ),
                  Text(
                    'متوسط النتائج: ${report['statistics']['averageScore']?.toStringAsFixed(1)}',
                  ),
                  Text(
                    'أعلى نتيجة: ${report['statistics']['highestScore']?.toStringAsFixed(1)}',
                  ),
                  Text(
                    'أقل نتيجة: ${report['statistics']['lowestScore']?.toStringAsFixed(1)}',
                  ),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('إغلاق'),
              ),
            ],
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إنشاء التقرير: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _exportReport() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تصدير التقرير'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('اختر صيغة التصدير:'),
            const SizedBox(height: 16),
            ListTile(
              leading: const Icon(Icons.picture_as_pdf, color: Colors.red),
              title: const Text('PDF'),
              onTap: () {
                Navigator.of(context).pop();
                _exportToPDF();
              },
            ),
            ListTile(
              leading: const Icon(Icons.table_chart, color: Colors.green),
              title: const Text('Excel'),
              onTap: () {
                Navigator.of(context).pop();
                _exportToExcel();
              },
            ),
            ListTile(
              leading: const Icon(Icons.text_snippet, color: Colors.blue),
              title: const Text('CSV'),
              onTap: () {
                Navigator.of(context).pop();
                _exportToCSV();
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }

  void _exportToPDF() async {
    try {
      final report = await _evaluationService.generateEvaluationReport();

      // هنا يمكن إضافة منطق تصدير PDF
      LoggingService.info(
        'تصدير تقرير PDF',
        category: 'PerformanceEvaluationScreen',
        data: {'report_size': report.length},
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تصدير التقرير بصيغة PDF بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تصدير PDF: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _exportToExcel() async {
    try {
      final report = await _evaluationService.generateEvaluationReport();

      // هنا يمكن إضافة منطق تصدير Excel
      LoggingService.info(
        'تصدير تقرير Excel',
        category: 'PerformanceEvaluationScreen',
        data: {'report_size': report.length},
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تصدير التقرير بصيغة Excel بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تصدير Excel: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _exportToCSV() async {
    try {
      final report = await _evaluationService.generateEvaluationReport();

      // هنا يمكن إضافة منطق تصدير CSV
      LoggingService.info(
        'تصدير تقرير CSV',
        category: 'PerformanceEvaluationScreen',
        data: {'report_size': report.length},
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تصدير التقرير بصيغة CSV بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تصدير CSV: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  double _calculateAverageScore() {
    if (_evaluations.isEmpty) return 0.0;
    final total = _evaluations.fold<double>(
      0.0,
      (sum, evaluation) => sum + evaluation.totalScore,
    );
    return total / _evaluations.length;
  }

  Color _getScoreColor(double score) {
    if (score >= 90) return RevolutionaryColors.successGlow;
    if (score >= 80) return RevolutionaryColors.warningAmber;
    if (score >= 70) return RevolutionaryColors.infoTurquoise;
    return RevolutionaryColors.errorCoral;
  }

  Future<void> _loadEvaluations({int? cycleId, int? employeeId}) async {
    try {
      final evaluations = await _evaluationService.getEmployeeEvaluations();

      if (mounted) {
        setState(() {
          _evaluations = evaluations;
        });
      }
    } catch (e) {
      LoggingService.error(
        'خطأ في تحميل التقييمات',
        category: 'PerformanceEvaluationScreen',
        data: {'error': e.toString()},
      );
    }
  }

  void _showAddCustomCriteriaDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إضافة معيار تقييم مخصص'),
        content: const Text('هذه الميزة قيد التطوير'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  void _showComparisonReportDialog() async {
    try {
      // جلب قائمة الموظفين للمقارنة
      final employees = await _employeeService.getAllEmployees();

      if (employees.isEmpty) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('لا يوجد موظفين للمقارنة'),
              backgroundColor: Colors.orange,
            ),
          );
        }
        return;
      }

      if (mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('تقرير مقارنة الأداء'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text('اختر الموظفين للمقارنة:'),
                const SizedBox(height: 16),
                SizedBox(
                  height: 200,
                  width: double.maxFinite,
                  child: ListView.builder(
                    itemCount: employees.length,
                    itemBuilder: (context, index) {
                      final employee = employees[index];
                      return CheckboxListTile(
                        title: Text(
                          '${employee.firstName} ${employee.lastName}',
                        ),
                        value: false,
                        onChanged: (value) {
                          // يمكن إضافة منطق اختيار الموظفين هنا
                        },
                      );
                    },
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('إلغاء'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  _generateComparisonReport([]);
                },
                child: const Text('إنشاء التقرير'),
              ),
            ],
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في عرض حوار المقارنة: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _generateComparisonReport(List<int> employeeIds) async {
    try {
      final report = await _evaluationService
          .generatePerformanceComparisonReport(
            employeeIds: employeeIds.isNotEmpty ? employeeIds : [1, 2], // مثال
          );

      if (mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('تقرير مقارنة الأداء'),
            content: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text('عدد الموظفين: ${report['employee_count']}'),
                  const SizedBox(height: 8),
                  const Text('بيانات المقارنة متاحة في التقرير المفصل'),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('إغلاق'),
              ),
            ],
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إنشاء تقرير المقارنة: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showTrendReportDialog() async {
    try {
      final report = await _evaluationService.generatePerformanceTrendReport();

      if (mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('تقرير التحليل الزمني'),
            content: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text('عدد الفترات: ${report['period_count']}'),
                  const SizedBox(height: 8),
                  const Text('بيانات التحليل الزمني متاحة في التقرير المفصل'),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('إغلاق'),
              ),
            ],
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إنشاء تقرير التحليل الزمني: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
