/// نماذج نظام التوقيع الإلكتروني
/// يوفر جميع النماذج المطلوبة لإدارة التوقيعات الإلكترونية والوثائق
library;

/// نموذج الوثيقة القابلة للتوقيع
class SignableDocument {
  final int? id;
  final String title;
  final String description;
  final String documentType; // contract, agreement, certificate, etc.
  final String filePath;
  final String fileHash; // SHA-256 hash للتحقق من سلامة الملف
  final int createdBy;
  final String creatorName;
  final String status; // draft, pending_signature, signed, cancelled
  final DateTime expiryDate;
  final bool requiresMultipleSignatures;
  final int requiredSignatures;
  final int currentSignatures;
  final Map<String, dynamic> metadata;
  final DateTime createdAt;
  final DateTime updatedAt;

  const SignableDocument({
    this.id,
    required this.title,
    required this.description,
    required this.documentType,
    required this.filePath,
    required this.fileHash,
    required this.createdBy,
    required this.creatorName,
    required this.status,
    required this.expiryDate,
    required this.requiresMultipleSignatures,
    required this.requiredSignatures,
    required this.currentSignatures,
    required this.metadata,
    required this.createdAt,
    required this.updatedAt,
  });

  factory SignableDocument.fromMap(Map<String, dynamic> map) {
    return SignableDocument(
      id: map['id'] as int?,
      title: map['title'] as String,
      description: map['description'] as String,
      documentType: map['document_type'] as String,
      filePath: map['file_path'] as String,
      fileHash: map['file_hash'] as String,
      createdBy: map['created_by'] as int,
      creatorName: map['creator_name'] as String,
      status: map['status'] as String,
      expiryDate: DateTime.parse(map['expiry_date'] as String),
      requiresMultipleSignatures:
          (map['requires_multiple_signatures'] as int?) == 1,
      requiredSignatures: map['required_signatures'] as int,
      currentSignatures: map['current_signatures'] as int,
      metadata: Map<String, dynamic>.from(map['metadata'] ?? {}),
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'document_type': documentType,
      'file_path': filePath,
      'file_hash': fileHash,
      'created_by': createdBy,
      'creator_name': creatorName,
      'status': status,
      'expiry_date': expiryDate.toIso8601String(),
      'requires_multiple_signatures': requiresMultipleSignatures ? 1 : 0,
      'required_signatures': requiredSignatures,
      'current_signatures': currentSignatures,
      'metadata': metadata,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  SignableDocument copyWith({
    int? id,
    String? title,
    String? description,
    String? documentType,
    String? filePath,
    String? fileHash,
    int? createdBy,
    String? creatorName,
    String? status,
    DateTime? expiryDate,
    bool? requiresMultipleSignatures,
    int? requiredSignatures,
    int? currentSignatures,
    Map<String, dynamic>? metadata,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return SignableDocument(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      documentType: documentType ?? this.documentType,
      filePath: filePath ?? this.filePath,
      fileHash: fileHash ?? this.fileHash,
      createdBy: createdBy ?? this.createdBy,
      creatorName: creatorName ?? this.creatorName,
      status: status ?? this.status,
      expiryDate: expiryDate ?? this.expiryDate,
      requiresMultipleSignatures:
          requiresMultipleSignatures ?? this.requiresMultipleSignatures,
      requiredSignatures: requiredSignatures ?? this.requiredSignatures,
      currentSignatures: currentSignatures ?? this.currentSignatures,
      metadata: metadata ?? this.metadata,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

/// نموذج التوقيع الإلكتروني
class DigitalSignature {
  final int? id;
  final int documentId;
  final int signerId;
  final String signerName;
  final String signerEmail;
  final String signatureData; // البيانات المشفرة للتوقيع
  final String signatureHash; // hash التوقيع
  final String certificateData; // بيانات الشهادة الرقمية
  final String signatureMethod; // RSA, ECDSA, etc.
  final String ipAddress;
  final String userAgent;
  final String location; // الموقع الجغرافي
  final DateTime signedAt;
  final bool isValid;
  final String? invalidationReason;
  final Map<String, dynamic> verificationData;
  final DateTime createdAt;

  const DigitalSignature({
    this.id,
    required this.documentId,
    required this.signerId,
    required this.signerName,
    required this.signerEmail,
    required this.signatureData,
    required this.signatureHash,
    required this.certificateData,
    required this.signatureMethod,
    required this.ipAddress,
    required this.userAgent,
    required this.location,
    required this.signedAt,
    required this.isValid,
    this.invalidationReason,
    required this.verificationData,
    required this.createdAt,
  });

  factory DigitalSignature.fromMap(Map<String, dynamic> map) {
    return DigitalSignature(
      id: map['id'] as int?,
      documentId: map['document_id'] as int,
      signerId: map['signer_id'] as int,
      signerName: map['signer_name'] as String,
      signerEmail: map['signer_email'] as String,
      signatureData: map['signature_data'] as String,
      signatureHash: map['signature_hash'] as String,
      certificateData: map['certificate_data'] as String,
      signatureMethod: map['signature_method'] as String,
      ipAddress: map['ip_address'] as String,
      userAgent: map['user_agent'] as String,
      location: map['location'] as String,
      signedAt: DateTime.parse(map['signed_at'] as String),
      isValid: (map['is_valid'] as int?) == 1,
      invalidationReason: map['invalidation_reason'] as String?,
      verificationData: Map<String, dynamic>.from(
        map['verification_data'] ?? {},
      ),
      createdAt: DateTime.parse(map['created_at'] as String),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'document_id': documentId,
      'signer_id': signerId,
      'signer_name': signerName,
      'signer_email': signerEmail,
      'signature_data': signatureData,
      'signature_hash': signatureHash,
      'certificate_data': certificateData,
      'signature_method': signatureMethod,
      'ip_address': ipAddress,
      'user_agent': userAgent,
      'location': location,
      'signed_at': signedAt.toIso8601String(),
      'is_valid': isValid ? 1 : 0,
      'invalidation_reason': invalidationReason,
      'verification_data': verificationData,
      'created_at': createdAt.toIso8601String(),
    };
  }

  DigitalSignature copyWith({
    int? id,
    int? documentId,
    int? signerId,
    String? signerName,
    String? signerEmail,
    String? signatureData,
    String? signatureHash,
    String? certificateData,
    String? signatureMethod,
    String? ipAddress,
    String? userAgent,
    String? location,
    DateTime? signedAt,
    bool? isValid,
    String? invalidationReason,
    Map<String, dynamic>? verificationData,
    DateTime? createdAt,
  }) {
    return DigitalSignature(
      id: id ?? this.id,
      documentId: documentId ?? this.documentId,
      signerId: signerId ?? this.signerId,
      signerName: signerName ?? this.signerName,
      signerEmail: signerEmail ?? this.signerEmail,
      signatureData: signatureData ?? this.signatureData,
      signatureHash: signatureHash ?? this.signatureHash,
      certificateData: certificateData ?? this.certificateData,
      signatureMethod: signatureMethod ?? this.signatureMethod,
      ipAddress: ipAddress ?? this.ipAddress,
      userAgent: userAgent ?? this.userAgent,
      location: location ?? this.location,
      signedAt: signedAt ?? this.signedAt,
      isValid: isValid ?? this.isValid,
      invalidationReason: invalidationReason ?? this.invalidationReason,
      verificationData: verificationData ?? this.verificationData,
      createdAt: createdAt ?? this.createdAt,
    );
  }
}

/// نموذج الشهادة الرقمية
class DigitalCertificate {
  final int? id;
  final int userId;
  final String userName;
  final String certificateType; // self_signed, ca_signed, government
  final String publicKey;
  final String privateKeyHash; // hash للمفتاح الخاص (لا نحفظ المفتاح نفسه)
  final String issuer;
  final String subject;
  final DateTime validFrom;
  final DateTime validTo;
  final String serialNumber;
  final String fingerprint;
  final String algorithm;
  final int keySize;
  final bool isActive;
  final bool isRevoked;
  final String? revocationReason;
  final DateTime? revokedAt;
  final Map<String, dynamic> extensions;
  final DateTime createdAt;
  final DateTime updatedAt;

  const DigitalCertificate({
    this.id,
    required this.userId,
    required this.userName,
    required this.certificateType,
    required this.publicKey,
    required this.privateKeyHash,
    required this.issuer,
    required this.subject,
    required this.validFrom,
    required this.validTo,
    required this.serialNumber,
    required this.fingerprint,
    required this.algorithm,
    required this.keySize,
    required this.isActive,
    required this.isRevoked,
    this.revocationReason,
    this.revokedAt,
    required this.extensions,
    required this.createdAt,
    required this.updatedAt,
  });

  factory DigitalCertificate.fromMap(Map<String, dynamic> map) {
    return DigitalCertificate(
      id: map['id'] as int?,
      userId: map['user_id'] as int,
      userName: map['user_name'] as String,
      certificateType: map['certificate_type'] as String,
      publicKey: map['public_key'] as String,
      privateKeyHash: map['private_key_hash'] as String,
      issuer: map['issuer'] as String,
      subject: map['subject'] as String,
      validFrom: DateTime.parse(map['valid_from'] as String),
      validTo: DateTime.parse(map['valid_to'] as String),
      serialNumber: map['serial_number'] as String,
      fingerprint: map['fingerprint'] as String,
      algorithm: map['algorithm'] as String,
      keySize: map['key_size'] as int,
      isActive: (map['is_active'] as int?) == 1,
      isRevoked: (map['is_revoked'] as int?) == 1,
      revocationReason: map['revocation_reason'] as String?,
      revokedAt: map['revoked_at'] != null
          ? DateTime.parse(map['revoked_at'] as String)
          : null,
      extensions: Map<String, dynamic>.from(map['extensions'] ?? {}),
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'user_id': userId,
      'user_name': userName,
      'certificate_type': certificateType,
      'public_key': publicKey,
      'private_key_hash': privateKeyHash,
      'issuer': issuer,
      'subject': subject,
      'valid_from': validFrom.toIso8601String(),
      'valid_to': validTo.toIso8601String(),
      'serial_number': serialNumber,
      'fingerprint': fingerprint,
      'algorithm': algorithm,
      'key_size': keySize,
      'is_active': isActive ? 1 : 0,
      'is_revoked': isRevoked ? 1 : 0,
      'revocation_reason': revocationReason,
      'revoked_at': revokedAt?.toIso8601String(),
      'extensions': extensions,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  DigitalCertificate copyWith({
    int? id,
    int? userId,
    String? userName,
    String? certificateType,
    String? publicKey,
    String? privateKeyHash,
    String? issuer,
    String? subject,
    DateTime? validFrom,
    DateTime? validTo,
    String? serialNumber,
    String? fingerprint,
    String? algorithm,
    int? keySize,
    bool? isActive,
    bool? isRevoked,
    String? revocationReason,
    DateTime? revokedAt,
    Map<String, dynamic>? extensions,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return DigitalCertificate(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      userName: userName ?? this.userName,
      certificateType: certificateType ?? this.certificateType,
      publicKey: publicKey ?? this.publicKey,
      privateKeyHash: privateKeyHash ?? this.privateKeyHash,
      issuer: issuer ?? this.issuer,
      subject: subject ?? this.subject,
      validFrom: validFrom ?? this.validFrom,
      validTo: validTo ?? this.validTo,
      serialNumber: serialNumber ?? this.serialNumber,
      fingerprint: fingerprint ?? this.fingerprint,
      algorithm: algorithm ?? this.algorithm,
      keySize: keySize ?? this.keySize,
      isActive: isActive ?? this.isActive,
      isRevoked: isRevoked ?? this.isRevoked,
      revocationReason: revocationReason ?? this.revocationReason,
      revokedAt: revokedAt ?? this.revokedAt,
      extensions: extensions ?? this.extensions,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

/// أنواع الوثائق
enum DocumentType {
  contract('contract', 'عقد'),
  agreement('agreement', 'اتفاقية'),
  certificate('certificate', 'شهادة'),
  invoice('invoice', 'فاتورة'),
  receipt('receipt', 'إيصال'),
  report('report', 'تقرير'),
  policy('policy', 'سياسة'),
  other('other', 'أخرى');

  const DocumentType(this.value, this.displayName);
  final String value;
  final String displayName;
}

/// حالات الوثيقة
enum DocumentStatus {
  draft('draft', 'مسودة'),
  pendingSignature('pending_signature', 'في انتظار التوقيع'),
  signed('signed', 'موقعة'),
  expired('expired', 'منتهية الصلاحية'),
  cancelled('cancelled', 'ملغية');

  const DocumentStatus(this.value, this.displayName);
  final String value;
  final String displayName;
}

/// طرق التوقيع
enum SignatureMethod {
  rsa('RSA', 'RSA'),
  ecdsa('ECDSA', 'ECDSA'),
  dsa('DSA', 'DSA');

  const SignatureMethod(this.value, this.displayName);
  final String value;
  final String displayName;
}
