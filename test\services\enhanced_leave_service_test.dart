/// اختبارات نظام الإجازات المحسن
/// للتأكد من عمل أنواع الإجازات الجديدة والميزات المحسنة
library;

import 'package:flutter/foundation.dart' show kDebugMode;
import 'package:flutter_test/flutter_test.dart';
import 'package:smart_ledger/services/leave_service.dart';
import 'package:smart_ledger/services/employee_service.dart';
import 'package:smart_ledger/models/hr_models.dart';
import 'package:smart_ledger/constants/app_constants.dart';

void main() {
  group('Enhanced Leave Service Tests', () {
    late LeaveService leaveService;
    late EmployeeService employeeService;

    setUpAll(() async {
      leaveService = LeaveService();
      employeeService = EmployeeService();
    });

    test('يجب أن يعيد جميع أنواع الإجازات الجديدة', () async {
      final leaveTypes = leaveService.getAllLeaveTypes();

      expect(leaveTypes.length, greaterThanOrEqualTo(13)); // 13 نوع إجازة على الأقل

      // التحقق من وجود الأنواع الجديدة
      final typeNames = leaveTypes
          .map((type) => type['type'] as String)
          .toList();

      expect(typeNames, contains(AppConstants.leaveTypePilgrimage));
      expect(typeNames, contains(AppConstants.leaveTypeMarriage));
      expect(typeNames, contains(AppConstants.leaveTypeDeath));
      expect(typeNames, contains(AppConstants.leaveTypeStudy));
      expect(typeNames, contains(AppConstants.leaveTypeTraining));
      expect(typeNames, contains(AppConstants.leaveTypePersonal));
      expect(typeNames, contains(AppConstants.leaveTypeCompensatory));

      if (kDebugMode) {
        print('✅ تم العثور على ${leaveTypes.length} نوع إجازة');
      }
      for (final type in leaveTypes) {
        if (kDebugMode) {
          if (kDebugMode) {
        }
          print('   - ${type['displayName']}: ${type['maxDays']} يوم');
        }
      }
    });

    test('يجب أن يعطي معلومات صحيحة لكل نوع إجازة', () async {
      // اختبار إجازة الحج
      final pilgrimageInfo = leaveService
          .getLeaveTypeInfo(AppConstants.leaveTypePilgrimage);
      expect(pilgrimageInfo['displayName'], equals('إجازة حج'));
      expect(pilgrimageInfo['maxDays'], equals(30));
      expect(pilgrimageInfo['isPaid'], isTrue);
      expect(pilgrimageInfo['needsSpecialApproval'], isTrue);

      // اختبار إجازة الزواج
      final marriageInfo = leaveService
          .getLeaveTypeInfo(AppConstants.leaveTypeMarriage);
      expect(marriageInfo['displayName'], equals('إجازة زواج'));
      expect(marriageInfo['maxDays'], equals(7));
      expect(marriageInfo['isPaid'], isTrue);
      expect(marriageInfo['needsSpecialApproval'], isFalse);

      // اختبار إجازة دراسية
      final studyInfo = leaveService.getLeaveTypeInfo(AppConstants.leaveTypeStudy);
      expect(studyInfo['displayName'], equals('إجازة دراسية'));
      expect(studyInfo['maxDays'], equals(365));
      expect(studyInfo['isPaid'], isFalse);
      expect(studyInfo['needsSpecialApproval'], isTrue);

      if (kDebugMode) {
        print('✅ معلومات أنواع الإجازات صحيحة');
      }
    });

    test('يجب أن يتحقق من رصيد الإجازات للأنواع المناسبة', () async {
      // إنشاء موظف للاختبار
      final employee = Employee(
        employeeNumber: 'TEST_LEAVE_001',
        nationalId: '12345678901',
        firstName: 'أحمد',
        lastName: 'محمد',
        fullName: 'أحمد محمد',
        hireDate: DateTime.now().subtract(const Duration(days: 365)),
        basicSalary: 1000000,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final savedEmployee = await employeeService.addEmployee(employee);

      // اختبار إجازة سنوية (تحتاج رصيد)
      try {
        await leaveService.createLeaveRequest(
          employeeId: savedEmployee.id!,
          leaveType: AppConstants.leaveTypeAnnual,
          startDate: DateTime.now().add(const Duration(days: 1)),
          endDate: DateTime.now().add(const Duration(days: 35)), // أكثر من 30 يوم
          reason: 'اختبار رصيد الإجازات',
        );
        fail('يجب أن يرفض الطلب لتجاوز الرصيد');
      } catch (e) {
        expect(e.toString(), contains('رصيد الإجازات غير كافي'));
        if (kDebugMode) {
          print('✅ تم رفض الطلب بسبب عدم كفاية الرصيد');
        }
      }

      // اختبار إجازة وفاة (لا تحتاج رصيد)
      final deathLeave = await leaveService.createLeaveRequest(
        employeeId: savedEmployee.id!,
        leaveType: AppConstants.leaveTypeDeath,
        startDate: DateTime.now().add(const Duration(days: 1)),
        endDate: DateTime.now().add(const Duration(days: 3)),
        reason: 'وفاة أحد الأقارب',
      );

      expect(deathLeave.leaveType, equals(AppConstants.leaveTypeDeath));
      expect(deathLeave.totalDays, equals(3));
      if (kDebugMode) {
        print('✅ تم قبول إجازة الوفاة بدون فحص الرصيد');
      }
    });

    test('يجب أن يتعامل مع الإجازات التي تحتاج موافقة خاصة', () async {
      // إنشاء موظف للاختبار
      final employee = Employee(
        employeeNumber: 'TEST_SPECIAL_001',
        nationalId: '98765432101',
        firstName: 'فاطمة',
        lastName: 'أحمد',
        fullName: 'فاطمة أحمد',
        hireDate: DateTime.now().subtract(const Duration(days: 365)),
        basicSalary: 1500000,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final savedEmployee = await employeeService.addEmployee(employee);

      // اختبار إجازة حج (تحتاج موافقة خاصة)
      final pilgrimageLeave = await leaveService.createLeaveRequest(
        employeeId: savedEmployee.id!,
        leaveType: AppConstants.leaveTypePilgrimage,
        startDate: DateTime.now().add(const Duration(days: 30)),
        endDate: DateTime.now().add(const Duration(days: 59)), // 30 يوم
        reason: 'أداء فريضة الحج',
      );

      expect(pilgrimageLeave.leaveType, equals(AppConstants.leaveTypePilgrimage));
      expect(pilgrimageLeave.status, equals(AppConstants.leaveStatusPending));
      
      final pilgrimageInfo = leaveService.getLeaveTypeInfo(AppConstants.leaveTypePilgrimage);
      expect(pilgrimageInfo['needsSpecialApproval'], isTrue);

      if (kDebugMode) {
        print('✅ تم إنشاء طلب إجازة حج يحتاج موافقة خاصة');
      }
    });

    test('يجب أن يلغي طلبات الإجازات المعلقة', () async {
      // إنشاء موظف للاختبار
      final employee = Employee(
        employeeNumber: 'TEST_CANCEL_001',
        nationalId: '11111111111',
        firstName: 'خالد',
        lastName: 'سعد',
        fullName: 'خالد سعد',
        hireDate: DateTime.now().subtract(const Duration(days: 365)),
        basicSalary: 1200000,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final savedEmployee = await employeeService.addEmployee(employee);

      // إنشاء طلب إجازة
      final leave = await leaveService.createLeaveRequest(
        employeeId: savedEmployee.id!,
        leaveType: AppConstants.leaveTypePersonal,
        startDate: DateTime.now().add(const Duration(days: 10)),
        endDate: DateTime.now().add(const Duration(days: 14)), // 5 أيام
        reason: 'ظروف شخصية',
      );

      expect(leave.status, equals(AppConstants.leaveStatusPending));

      // إلغاء الطلب
      await leaveService.cancelLeaveRequest(leave.id!, 'تغيير في الخطط');

      // التحقق من الإلغاء
      final cancelledLeave = await leaveService.getLeaveById(leave.id!);
      expect(cancelledLeave?.status, equals(AppConstants.leaveStatusCancelled));
      expect(cancelledLeave?.rejectionReason, equals('تغيير في الخطط'));

      if (kDebugMode) {
        print('✅ تم إلغاء طلب الإجازة بنجاح');
      }
    });

    test('يجب أن يحسب الحد الأقصى للأيام بشكل صحيح', () async {
      // التحقق من الحدود القصوى
      expect(AppConstants.leaveTypeMaxDays[AppConstants.leaveTypeAnnual], equals(30));
      expect(AppConstants.leaveTypeMaxDays[AppConstants.leaveTypeSick], equals(90));
      expect(AppConstants.leaveTypeMaxDays[AppConstants.leaveTypeMaternity], equals(120));
      expect(AppConstants.leaveTypeMaxDays[AppConstants.leaveTypeMarriage], equals(7));
      expect(AppConstants.leaveTypeMaxDays[AppConstants.leaveTypeDeath], equals(3));
      expect(AppConstants.leaveTypeMaxDays[AppConstants.leaveTypePilgrimage], equals(30));
      expect(AppConstants.leaveTypeMaxDays[AppConstants.leaveTypeStudy], equals(365));

      if (kDebugMode) {
        print('✅ الحدود القصوى للإجازات محددة بشكل صحيح');
      }
    });

    test('يجب أن يميز بين الإجازات المدفوعة وغير المدفوعة', () async {
      // الإجازات المدفوعة
      expect(AppConstants.paidLeaveTypes, contains(AppConstants.leaveTypeAnnual));
      expect(AppConstants.paidLeaveTypes, contains(AppConstants.leaveTypeMaternity));
      expect(AppConstants.paidLeaveTypes, contains(AppConstants.leaveTypeMarriage));
      expect(AppConstants.paidLeaveTypes, contains(AppConstants.leaveTypePilgrimage));

      // الإجازات غير المدفوعة
      expect(AppConstants.paidLeaveTypes, isNot(contains(AppConstants.leaveTypeUnpaid)));
      expect(AppConstants.paidLeaveTypes, isNot(contains(AppConstants.leaveTypePersonal)));

      // التحقق من معلومات الإجازات
      final annualInfo = leaveService.getLeaveTypeInfo(AppConstants.leaveTypeAnnual);
      expect(annualInfo['isPaid'], isTrue);

      final unpaidInfo = leaveService.getLeaveTypeInfo(AppConstants.leaveTypeUnpaid);
      expect(unpaidInfo['isPaid'], isFalse);

      if (kDebugMode) {
        print('✅ تم التمييز بين الإجازات المدفوعة وغير المدفوعة بشكل صحيح');
      }
    });
  });
}
