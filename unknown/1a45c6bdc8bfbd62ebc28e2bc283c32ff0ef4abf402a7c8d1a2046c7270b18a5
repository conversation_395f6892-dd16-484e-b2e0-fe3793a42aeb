/// اختبارات نظام الرواتب المحسن
/// للتأكد من عمل تكامل مكونات الراتب مع حساب الرواتب
library;

import 'package:flutter/foundation.dart' show kDebugMode;
import 'package:flutter_test/flutter_test.dart';
import 'package:smart_ledger/services/payroll_service.dart';
import 'package:smart_ledger/services/salary_components_service.dart';
import 'package:smart_ledger/services/employee_service.dart';
import 'package:smart_ledger/models/hr_models.dart';

void main() {
  group('Enhanced Payroll Service Tests', () {
    late PayrollService payrollService;
    late SalaryComponentsService salaryComponentsService;
    late EmployeeService employeeService;

    setUpAll(() async {
      payrollService = PayrollService();
      salaryComponentsService = SalaryComponentsService();
      employeeService = EmployeeService();
      
      // إنشاء مكونات راتب افتراضية للاختبار
      await salaryComponentsService.createDefaultSalaryComponents();
    });

    test('يجب أن يحسب الراتب باستخدام مكونات الراتب المخصصة', () async {
      // إنشاء موظف للاختبار
      final employee = Employee(
        employeeNumber: 'TEST001',
        nationalId: '12345678901',
        firstName: 'أحمد',
        lastName: 'محمد',
        fullName: 'أحمد محمد',
        hireDate: DateTime.now().subtract(const Duration(days: 365)),
        basicSalary: 1000000, // مليون ليرة سورية
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final savedEmployee = await employeeService.addEmployee(employee);

      // تطبيق مكونات راتب مخصصة
      final components = await salaryComponentsService.getAllSalaryComponents(activeOnly: true);
      await payrollService.applySalaryTemplateToEmployee(
        employeeId: savedEmployee.id!,
        components: components,
        basicSalary: savedEmployee.basicSalary,
      );

      // حساب الراتب
      final payrollRecord = await payrollService.calculatePayroll(
        employeeId: savedEmployee.id!,
        month: DateTime.now().month,
        year: DateTime.now().year,
      );

      // التحقق من النتائج
      expect(payrollRecord.basicSalary, greaterThan(0));
      expect(payrollRecord.allowances, greaterThan(0)); // يجب أن تكون هناك بدلات
      expect(payrollRecord.netSalary, greaterThan(0));
      expect(payrollRecord.incomeTax, greaterThan(0)); // يجب أن تكون هناك ضرائب
      expect(payrollRecord.socialInsurance, greaterThan(0)); // يجب أن تكون هناك تأمينات

      if (kDebugMode) {
        print('✅ تم حساب الراتب بنجاح:');
      }
      if (kDebugMode) {
        print('   الراتب الأساسي: ${payrollRecord.basicSalary}');
      }
      if (kDebugMode) {
        print('   البدلات: ${payrollRecord.allowances}');
      }
      if (kDebugMode) {
        print('   الحوافز: ${payrollRecord.bonuses}');
      }
      if (kDebugMode) {
        print('   الاستقطاعات: ${payrollRecord.totalDeductions}');
      }
      if (kDebugMode) {
        print('   صافي الراتب: ${payrollRecord.netSalary}');
      }
    });

    test('يجب أن يحسب الضرائب المتدرجة بشكل صحيح', () async {
      // اختبار حساب الضرائب للرواتب المختلفة
      final testSalaries = [500000, 2000000, 5000000, 10000000]; // رواتب مختلفة

      for (final salary in testSalaries) {
        final employee = Employee(
          employeeNumber: 'TEST_TAX_$salary',
          nationalId: '${salary}123',
          firstName: 'موظف',
          lastName: 'اختبار',
          fullName: 'موظف اختبار $salary',
          hireDate: DateTime.now().subtract(const Duration(days: 365)),
          basicSalary: salary.toDouble(),
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        final savedEmployee = await employeeService.addEmployee(employee);

        final salaryCalculation = await payrollService.calculateAdvancedSalary(
          employeeId: savedEmployee.id!,
          month: DateTime.now().month,
          year: DateTime.now().year,
        );

        // التحقق من أن الضرائب محسوبة بشكل متدرج
        expect(salaryCalculation.taxAmount, greaterThanOrEqualTo(0));
        
        // الرواتب الأعلى يجب أن تدفع ضرائب أكثر (نسبياً)
        final taxRate = salaryCalculation.taxAmount / salaryCalculation.grossSalary;
        
        if (kDebugMode) {
          print('راتب: $salary - ضريبة: ${salaryCalculation.taxAmount} - معدل: ${(taxRate * 100).toStringAsFixed(2)}%');
        }
      }
    });

    test('يجب أن يطبق مكونات الراتب بالنسب المئوية بشكل صحيح', () async {
      // إنشاء مكون راتب بنسبة مئوية
      final percentageComponent = SalaryComponent(
        name: 'بدل أداء',
        code: 'PERF_ALLOW',
        type: 'allowance',
        description: 'بدل الأداء 10% من الراتب الأساسي',
        defaultAmount: 10, // 10%
        isPercentage: true,
        percentageOf: 'basic_salary',
        isTaxable: true,
        isActive: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final savedComponent = await salaryComponentsService.createSalaryComponent(percentageComponent);

      // إنشاء موظف
      final employee = Employee(
        employeeNumber: 'TEST_PERCENT',
        nationalId: '98765432101',
        firstName: 'سارة',
        lastName: 'أحمد',
        fullName: 'سارة أحمد',
        hireDate: DateTime.now().subtract(const Duration(days: 365)),
        basicSalary: 2000000, // 2 مليون ليرة
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final savedEmployee = await employeeService.addEmployee(employee);

      // تطبيق المكون
      await payrollService.applySalaryTemplateToEmployee(
        employeeId: savedEmployee.id!,
        components: [savedComponent],
        basicSalary: savedEmployee.basicSalary,
      );

      // حساب الراتب
      final payrollRecord = await payrollService.calculatePayroll(
        employeeId: savedEmployee.id!,
        month: DateTime.now().month,
        year: DateTime.now().year,
      );

      // التحقق من أن البدل محسوب بشكل صحيح (10% من 2 مليون = 200 ألف)
      expect(payrollRecord.allowances, closeTo(200000, 1000)); // مع هامش خطأ صغير

      if (kDebugMode) {
        print('✅ تم حساب النسبة المئوية بشكل صحيح:');
      }
      if (kDebugMode) {
        print('   الراتب الأساسي: ${payrollRecord.basicSalary}');
      }
      if (kDebugMode) {
        print('   البدل (10%): ${payrollRecord.allowances}');
      }
    });

    test('يجب أن يتعامل مع الموظفين بدون مكونات راتب مخصصة', () async {
      // إنشاء موظف بدون تطبيق مكونات مخصصة
      final employee = Employee(
        employeeNumber: 'TEST_DEFAULT',
        nationalId: '11111111111',
        firstName: 'خالد',
        lastName: 'سعد',
        fullName: 'خالد سعد',
        hireDate: DateTime.now().subtract(const Duration(days: 365)),
        basicSalary: 1500000,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final savedEmployee = await employeeService.addEmployee(employee);

      // حساب الراتب بدون تطبيق مكونات مخصصة
      final payrollRecord = await payrollService.calculatePayroll(
        employeeId: savedEmployee.id!,
        month: DateTime.now().month,
        year: DateTime.now().year,
      );

      // يجب أن يستخدم المكونات الافتراضية
      expect(payrollRecord.basicSalary, greaterThan(0));
      expect(payrollRecord.netSalary, greaterThan(0));

      if (kDebugMode) {
        print('✅ تم حساب الراتب باستخدام المكونات الافتراضية:');
      }
      if (kDebugMode) {
        print('   الراتب الأساسي: ${payrollRecord.basicSalary}');
      }
      if (kDebugMode) {
        print('   صافي الراتب: ${payrollRecord.netSalary}');
      }
    });
  });
}
