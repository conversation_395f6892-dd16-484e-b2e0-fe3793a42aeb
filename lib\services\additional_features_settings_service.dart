/// خدمة إعدادات الميزات الإضافية
/// توفر إدارة شاملة لإعدادات الميزات الإضافية في Smart Ledger
library;

import 'package:shared_preferences/shared_preferences.dart';
import '../database/database_helper.dart';
import '../services/logging_service.dart';

class AdditionalFeaturesSettingsService {
  static final AdditionalFeaturesSettingsService _instance = 
      AdditionalFeaturesSettingsService._internal();
  factory AdditionalFeaturesSettingsService() => _instance;
  AdditionalFeaturesSettingsService._internal();

  final DatabaseHelper _databaseHelper = DatabaseHelper();

  // مفاتيح الإعدادات
  static const String _keyElectronicApprovals = 'enable_electronic_approvals';
  static const String _keyDigitalSignature = 'enable_digital_signature';
  static const String _keyExternalApi = 'enable_external_api';
  static const String _keyDataExport = 'enable_data_export';
  static const String _keyNotifications = 'enable_notifications';

  /// تفعيل/إلغاء الموافقات الإلكترونية
  Future<bool> setElectronicApprovalsEnabled(bool enabled) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_keyElectronicApprovals, enabled);
      
      // حفظ في قاعدة البيانات أيضاً
      await _saveSettingToDatabase(_keyElectronicApprovals, enabled.toString());
      
      LoggingService.info(
        'تم ${enabled ? 'تفعيل' : 'إلغاء'} الموافقات الإلكترونية',
        category: 'AdditionalFeaturesSettings',
        data: {'enabled': enabled},
      );
      
      return true;
    } catch (e) {
      LoggingService.error(
        'خطأ في تحديث إعداد الموافقات الإلكترونية',
        category: 'AdditionalFeaturesSettings',
        data: {'enabled': enabled, 'error': e.toString()},
      );
      return false;
    }
  }

  /// الحصول على حالة الموافقات الإلكترونية
  Future<bool> isElectronicApprovalsEnabled() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool(_keyElectronicApprovals) ?? true; // افتراضياً مفعل
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب إعداد الموافقات الإلكترونية',
        category: 'AdditionalFeaturesSettings',
        data: {'error': e.toString()},
      );
      return true; // افتراضياً مفعل
    }
  }

  /// تفعيل/إلغاء التوقيع الإلكتروني
  Future<bool> setDigitalSignatureEnabled(bool enabled) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_keyDigitalSignature, enabled);
      
      await _saveSettingToDatabase(_keyDigitalSignature, enabled.toString());
      
      LoggingService.info(
        'تم ${enabled ? 'تفعيل' : 'إلغاء'} التوقيع الإلكتروني',
        category: 'AdditionalFeaturesSettings',
        data: {'enabled': enabled},
      );
      
      return true;
    } catch (e) {
      LoggingService.error(
        'خطأ في تحديث إعداد التوقيع الإلكتروني',
        category: 'AdditionalFeaturesSettings',
        data: {'enabled': enabled, 'error': e.toString()},
      );
      return false;
    }
  }

  /// الحصول على حالة التوقيع الإلكتروني
  Future<bool> isDigitalSignatureEnabled() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool(_keyDigitalSignature) ?? true; // افتراضياً مفعل
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب إعداد التوقيع الإلكتروني',
        category: 'AdditionalFeaturesSettings',
        data: {'error': e.toString()},
      );
      return true;
    }
  }

  /// تفعيل/إلغاء API الخارجي
  Future<bool> setExternalApiEnabled(bool enabled) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_keyExternalApi, enabled);
      
      await _saveSettingToDatabase(_keyExternalApi, enabled.toString());
      
      LoggingService.info(
        'تم ${enabled ? 'تفعيل' : 'إلغاء'} API الخارجي',
        category: 'AdditionalFeaturesSettings',
        data: {'enabled': enabled},
      );
      
      return true;
    } catch (e) {
      LoggingService.error(
        'خطأ في تحديث إعداد API الخارجي',
        category: 'AdditionalFeaturesSettings',
        data: {'enabled': enabled, 'error': e.toString()},
      );
      return false;
    }
  }

  /// الحصول على حالة API الخارجي
  Future<bool> isExternalApiEnabled() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool(_keyExternalApi) ?? false; // افتراضياً معطل للأمان
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب إعداد API الخارجي',
        category: 'AdditionalFeaturesSettings',
        data: {'error': e.toString()},
      );
      return false;
    }
  }

  /// تفعيل/إلغاء تصدير البيانات
  Future<bool> setDataExportEnabled(bool enabled) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_keyDataExport, enabled);
      
      await _saveSettingToDatabase(_keyDataExport, enabled.toString());
      
      LoggingService.info(
        'تم ${enabled ? 'تفعيل' : 'إلغاء'} تصدير البيانات',
        category: 'AdditionalFeaturesSettings',
        data: {'enabled': enabled},
      );
      
      return true;
    } catch (e) {
      LoggingService.error(
        'خطأ في تحديث إعداد تصدير البيانات',
        category: 'AdditionalFeaturesSettings',
        data: {'enabled': enabled, 'error': e.toString()},
      );
      return false;
    }
  }

  /// الحصول على حالة تصدير البيانات
  Future<bool> isDataExportEnabled() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool(_keyDataExport) ?? true; // افتراضياً مفعل
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب إعداد تصدير البيانات',
        category: 'AdditionalFeaturesSettings',
        data: {'error': e.toString()},
      );
      return true;
    }
  }

  /// تفعيل/إلغاء الإشعارات
  Future<bool> setNotificationsEnabled(bool enabled) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_keyNotifications, enabled);
      
      await _saveSettingToDatabase(_keyNotifications, enabled.toString());
      
      LoggingService.info(
        'تم ${enabled ? 'تفعيل' : 'إلغاء'} الإشعارات',
        category: 'AdditionalFeaturesSettings',
        data: {'enabled': enabled},
      );
      
      return true;
    } catch (e) {
      LoggingService.error(
        'خطأ في تحديث إعداد الإشعارات',
        category: 'AdditionalFeaturesSettings',
        data: {'enabled': enabled, 'error': e.toString()},
      );
      return false;
    }
  }

  /// الحصول على حالة الإشعارات
  Future<bool> isNotificationsEnabled() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool(_keyNotifications) ?? true; // افتراضياً مفعل
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب إعداد الإشعارات',
        category: 'AdditionalFeaturesSettings',
        data: {'error': e.toString()},
      );
      return true;
    }
  }

  /// الحصول على جميع الإعدادات
  Future<Map<String, bool>> getAllSettings() async {
    return {
      'electronic_approvals': await isElectronicApprovalsEnabled(),
      'digital_signature': await isDigitalSignatureEnabled(),
      'external_api': await isExternalApiEnabled(),
      'data_export': await isDataExportEnabled(),
      'notifications': await isNotificationsEnabled(),
    };
  }

  /// إعادة تعيين جميع الإعدادات للقيم الافتراضية
  Future<bool> resetToDefaults() async {
    try {
      await setElectronicApprovalsEnabled(true);
      await setDigitalSignatureEnabled(true);
      await setExternalApiEnabled(false);
      await setDataExportEnabled(true);
      await setNotificationsEnabled(true);
      
      LoggingService.info(
        'تم إعادة تعيين إعدادات الميزات الإضافية للقيم الافتراضية',
        category: 'AdditionalFeaturesSettings',
      );
      
      return true;
    } catch (e) {
      LoggingService.error(
        'خطأ في إعادة تعيين الإعدادات',
        category: 'AdditionalFeaturesSettings',
        data: {'error': e.toString()},
      );
      return false;
    }
  }

  /// حفظ الإعداد في قاعدة البيانات
  Future<void> _saveSettingToDatabase(String key, String value) async {
    try {
      final db = await _databaseHelper.database;
      
      // التحقق من وجود الإعداد
      final existing = await db.query(
        'settings',
        where: 'key = ?',
        whereArgs: [key],
        limit: 1,
      );
      
      if (existing.isNotEmpty) {
        // تحديث الإعداد الموجود
        await db.update(
          'settings',
          {
            'value': value,
            'updated_at': DateTime.now().toIso8601String(),
          },
          where: 'key = ?',
          whereArgs: [key],
        );
      } else {
        // إضافة إعداد جديد
        await db.insert('settings', {
          'key': key,
          'value': value,
          'created_at': DateTime.now().toIso8601String(),
          'updated_at': DateTime.now().toIso8601String(),
        });
      }
    } catch (e) {
      LoggingService.error(
        'خطأ في حفظ الإعداد في قاعدة البيانات',
        category: 'AdditionalFeaturesSettings',
        data: {'key': key, 'value': value, 'error': e.toString()},
      );
    }
  }

  /// تحميل الإعدادات من قاعدة البيانات
  Future<void> loadSettingsFromDatabase() async {
    try {
      final db = await _databaseHelper.database;
      final results = await db.query('settings');
      
      final prefs = await SharedPreferences.getInstance();
      
      for (final row in results) {
        final key = row['key'] as String;
        final value = row['value'] as String;
        
        // تحويل القيمة النصية إلى boolean إذا كانت من إعدادات الميزات
        if ([_keyElectronicApprovals, _keyDigitalSignature, _keyExternalApi, 
             _keyDataExport, _keyNotifications].contains(key)) {
          await prefs.setBool(key, value.toLowerCase() == 'true');
        }
      }
      
      LoggingService.info(
        'تم تحميل إعدادات الميزات الإضافية من قاعدة البيانات',
        category: 'AdditionalFeaturesSettings',
        data: {'settings_count': results.length},
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في تحميل الإعدادات من قاعدة البيانات',
        category: 'AdditionalFeaturesSettings',
        data: {'error': e.toString()},
      );
    }
  }
}
