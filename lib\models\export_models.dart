/// نماذج نظام تصدير البيانات
/// يوفر جميع النماذج المطلوبة لإدارة تصدير البيانات بصيغ متعددة
library;

/// نموذج طلب التصدير
class ExportRequest {
  final int? id;
  final String title;
  final String description;
  final String dataSource; // employees, payroll, invoices, etc.
  final String exportFormat; // excel, pdf, csv, json, xml
  final Map<String, dynamic> filters;
  final List<String> selectedFields;
  final Map<String, dynamic> formatOptions;
  final int requestedBy;
  final String requesterName;
  final String status; // pending, processing, completed, failed
  final String? filePath;
  final int? fileSize;
  final DateTime? completedAt;
  final String? errorMessage;
  final Map<String, dynamic> metadata;
  final DateTime createdAt;
  final DateTime updatedAt;

  const ExportRequest({
    this.id,
    required this.title,
    required this.description,
    required this.dataSource,
    required this.exportFormat,
    required this.filters,
    required this.selectedFields,
    required this.formatOptions,
    required this.requestedBy,
    required this.requesterName,
    required this.status,
    this.filePath,
    this.fileSize,
    this.completedAt,
    this.errorMessage,
    required this.metadata,
    required this.createdAt,
    required this.updatedAt,
  });

  factory ExportRequest.fromMap(Map<String, dynamic> map) {
    return ExportRequest(
      id: map['id'] as int?,
      title: map['title'] as String,
      description: map['description'] as String,
      dataSource: map['data_source'] as String,
      exportFormat: map['export_format'] as String,
      filters: Map<String, dynamic>.from(map['filters'] ?? {}),
      selectedFields: List<String>.from(map['selected_fields'] ?? []),
      formatOptions: Map<String, dynamic>.from(map['format_options'] ?? {}),
      requestedBy: map['requested_by'] as int,
      requesterName: map['requester_name'] as String,
      status: map['status'] as String,
      filePath: map['file_path'] as String?,
      fileSize: map['file_size'] as int?,
      completedAt: map['completed_at'] != null ? DateTime.parse(map['completed_at'] as String) : null,
      errorMessage: map['error_message'] as String?,
      metadata: Map<String, dynamic>.from(map['metadata'] ?? {}),
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'data_source': dataSource,
      'export_format': exportFormat,
      'filters': filters,
      'selected_fields': selectedFields,
      'format_options': formatOptions,
      'requested_by': requestedBy,
      'requester_name': requesterName,
      'status': status,
      'file_path': filePath,
      'file_size': fileSize,
      'completed_at': completedAt?.toIso8601String(),
      'error_message': errorMessage,
      'metadata': metadata,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  ExportRequest copyWith({
    int? id,
    String? title,
    String? description,
    String? dataSource,
    String? exportFormat,
    Map<String, dynamic>? filters,
    List<String>? selectedFields,
    Map<String, dynamic>? formatOptions,
    int? requestedBy,
    String? requesterName,
    String? status,
    String? filePath,
    int? fileSize,
    DateTime? completedAt,
    String? errorMessage,
    Map<String, dynamic>? metadata,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return ExportRequest(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      dataSource: dataSource ?? this.dataSource,
      exportFormat: exportFormat ?? this.exportFormat,
      filters: filters ?? this.filters,
      selectedFields: selectedFields ?? this.selectedFields,
      formatOptions: formatOptions ?? this.formatOptions,
      requestedBy: requestedBy ?? this.requestedBy,
      requesterName: requesterName ?? this.requesterName,
      status: status ?? this.status,
      filePath: filePath ?? this.filePath,
      fileSize: fileSize ?? this.fileSize,
      completedAt: completedAt ?? this.completedAt,
      errorMessage: errorMessage ?? this.errorMessage,
      metadata: metadata ?? this.metadata,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

/// نموذج قالب التصدير
class ExportTemplate {
  final int? id;
  final String name;
  final String description;
  final String dataSource;
  final String exportFormat;
  final List<String> defaultFields;
  final Map<String, dynamic> defaultFilters;
  final Map<String, dynamic> formatSettings;
  final bool isPublic;
  final int createdBy;
  final String creatorName;
  final bool isActive;
  final int usageCount;
  final DateTime createdAt;
  final DateTime updatedAt;

  const ExportTemplate({
    this.id,
    required this.name,
    required this.description,
    required this.dataSource,
    required this.exportFormat,
    required this.defaultFields,
    required this.defaultFilters,
    required this.formatSettings,
    required this.isPublic,
    required this.createdBy,
    required this.creatorName,
    required this.isActive,
    required this.usageCount,
    required this.createdAt,
    required this.updatedAt,
  });

  factory ExportTemplate.fromMap(Map<String, dynamic> map) {
    return ExportTemplate(
      id: map['id'] as int?,
      name: map['name'] as String,
      description: map['description'] as String,
      dataSource: map['data_source'] as String,
      exportFormat: map['export_format'] as String,
      defaultFields: List<String>.from(map['default_fields'] ?? []),
      defaultFilters: Map<String, dynamic>.from(map['default_filters'] ?? {}),
      formatSettings: Map<String, dynamic>.from(map['format_settings'] ?? {}),
      isPublic: (map['is_public'] as int?) == 1,
      createdBy: map['created_by'] as int,
      creatorName: map['creator_name'] as String,
      isActive: (map['is_active'] as int?) == 1,
      usageCount: map['usage_count'] as int,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'data_source': dataSource,
      'export_format': exportFormat,
      'default_fields': defaultFields,
      'default_filters': defaultFilters,
      'format_settings': formatSettings,
      'is_public': isPublic ? 1 : 0,
      'created_by': createdBy,
      'creator_name': creatorName,
      'is_active': isActive ? 1 : 0,
      'usage_count': usageCount,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }
}

/// نموذج إعدادات التصدير
class ExportSettings {
  final String dateFormat;
  final String numberFormat;
  final String currencySymbol;
  final String encoding;
  final String delimiter; // للـ CSV
  final bool includeHeaders;
  final bool includeFooters;
  final String pageOrientation; // portrait, landscape
  final String pageSize; // A4, A3, Letter
  final Map<String, String> columnHeaders;
  final Map<String, int> columnWidths;
  final Map<String, dynamic> styling;

  const ExportSettings({
    required this.dateFormat,
    required this.numberFormat,
    required this.currencySymbol,
    required this.encoding,
    required this.delimiter,
    required this.includeHeaders,
    required this.includeFooters,
    required this.pageOrientation,
    required this.pageSize,
    required this.columnHeaders,
    required this.columnWidths,
    required this.styling,
  });

  factory ExportSettings.fromMap(Map<String, dynamic> map) {
    return ExportSettings(
      dateFormat: map['dateFormat'] as String,
      numberFormat: map['numberFormat'] as String,
      currencySymbol: map['currencySymbol'] as String,
      encoding: map['encoding'] as String,
      delimiter: map['delimiter'] as String,
      includeHeaders: map['includeHeaders'] as bool,
      includeFooters: map['includeFooters'] as bool,
      pageOrientation: map['pageOrientation'] as String,
      pageSize: map['pageSize'] as String,
      columnHeaders: Map<String, String>.from(map['columnHeaders'] ?? {}),
      columnWidths: Map<String, int>.from(map['columnWidths'] ?? {}),
      styling: Map<String, dynamic>.from(map['styling'] ?? {}),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'dateFormat': dateFormat,
      'numberFormat': numberFormat,
      'currencySymbol': currencySymbol,
      'encoding': encoding,
      'delimiter': delimiter,
      'includeHeaders': includeHeaders,
      'includeFooters': includeFooters,
      'pageOrientation': pageOrientation,
      'pageSize': pageSize,
      'columnHeaders': columnHeaders,
      'columnWidths': columnWidths,
      'styling': styling,
    };
  }
}

/// أنواع مصادر البيانات
enum DataSource {
  employees('employees', 'الموظفين'),
  payroll('payroll', 'الرواتب'),
  attendance('attendance', 'الحضور والانصراف'),
  leaves('leaves', 'الإجازات'),
  training('training', 'التدريب'),
  performance('performance', 'تقييم الأداء'),
  invoices('invoices', 'الفواتير'),
  customers('customers', 'العملاء'),
  suppliers('suppliers', 'الموردين'),
  inventory('inventory', 'المخزون'),
  accounts('accounts', 'الحسابات'),
  reports('reports', 'التقارير');

  const DataSource(this.value, this.displayName);
  final String value;
  final String displayName;
}

/// صيغ التصدير
enum ExportFormat {
  excel('excel', 'Excel (.xlsx)', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'),
  pdf('pdf', 'PDF (.pdf)', 'application/pdf'),
  csv('csv', 'CSV (.csv)', 'text/csv'),
  json('json', 'JSON (.json)', 'application/json'),
  xml('xml', 'XML (.xml)', 'application/xml'),
  word('word', 'Word (.docx)', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document');

  const ExportFormat(this.value, this.displayName, this.mimeType);
  final String value;
  final String displayName;
  final String mimeType;
}

/// حالات التصدير
enum ExportStatus {
  pending('pending', 'في الانتظار'),
  processing('processing', 'قيد المعالجة'),
  completed('completed', 'مكتمل'),
  failed('failed', 'فشل'),
  cancelled('cancelled', 'ملغي');

  const ExportStatus(this.value, this.displayName);
  final String value;
  final String displayName;
}

/// أحجام الصفحات
enum PageSize {
  a4('A4', 'A4'),
  a3('A3', 'A3'),
  letter('Letter', 'Letter'),
  legal('Legal', 'Legal');

  const PageSize(this.value, this.displayName);
  final String value;
  final String displayName;
}

/// اتجاهات الصفحة
enum PageOrientation {
  portrait('portrait', 'عمودي'),
  landscape('landscape', 'أفقي');

  const PageOrientation(this.value, this.displayName);
  final String value;
  final String displayName;
}
