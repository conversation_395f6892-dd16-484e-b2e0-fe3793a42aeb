import 'package:flutter/material.dart';
import 'constants/revolutionary_theme.dart';
import 'constants/app_constants.dart';
import 'screens/revolutionary_splash_screen.dart';
import 'screens/revolutionary_home_screen.dart';
import 'screens/revolutionary_demo_screen.dart';
import 'services/current_user_service.dart';
import 'services/additional_features_settings_service.dart';
import 'services/database_migration_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // تهيئة المستخدم الحالي
  final currentUserService = CurrentUserService();
  await currentUserService.loadSavedUser();

  // تهيئة إعدادات الميزات الإضافية
  final additionalFeaturesService = AdditionalFeaturesSettingsService();
  await additionalFeaturesService.loadSettingsFromDatabase();

  // إصلاح مشاكل قاعدة البيانات الشائعة
  final migrationService = DatabaseMigrationService();
  await migrationService.fixCommonDatabaseIssues();

  runApp(const SmartLedgerApp());
}

class SmartLedgerApp extends StatelessWidget {
  const SmartLedgerApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: AppConstants.appName,
      theme: RevolutionaryTheme.revolutionaryTheme,
      home: const RevolutionarySplashScreen(),
      debugShowCheckedModeBanner: false,
      locale: const Locale('ar', 'SY'),
      builder: (context, child) {
        return Directionality(textDirection: TextDirection.rtl, child: child!);
      },

      // إضافة مسارات للصفحات الجديدة
      routes: {
        '/home': (context) => const RevolutionaryHomeScreen(),
        '/demo': (context) => const RevolutionaryDemoScreen(),
        '/splash': (context) => const RevolutionarySplashScreen(),
      },
    );
  }
}
