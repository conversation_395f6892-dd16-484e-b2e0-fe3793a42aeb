/// حوار إنشاء خطة تطوير مهني جديدة
/// يوفر واجهة شاملة لإنشاء خطط التطوير المهني مع الأهداف والمعالم
library;

import 'package:flutter/material.dart';
import '../models/hr_models.dart';
import '../constants/revolutionary_design_colors.dart';

class CreateDevelopmentPlanDialog extends StatefulWidget {
  final List<CareerPath> careerPaths;
  final Function(CareerDevelopmentPlan) onSave;

  const CreateDevelopmentPlanDialog({
    super.key,
    required this.careerPaths,
    required this.onSave,
  });

  @override
  State<CreateDevelopmentPlanDialog> createState() =>
      _CreateDevelopmentPlanDialogState();
}

class _CreateDevelopmentPlanDialogState
    extends State<CreateDevelopmentPlanDialog> {
  final _formKey = GlobalKey<FormState>();
  final _employeeIdController = TextEditingController();
  final _progressNotesController = TextEditingController();

  int? _selectedCareerPathId;
  int? _selectedTargetLevelId;
  DateTime _startDate = DateTime.now();
  DateTime _targetDate = DateTime.now().add(const Duration(days: 365));
  String _status = 'draft';
  // ملاحظة: هذه الخصائص ستكون متاحة في إصدار مستقبلي من النموذج
  final List<String> _developmentGoals = [];
  final List<String> _skillGaps = [];
  final List<String> _developmentActions = [];
  final List<String> _milestones = [];
  double _progressPercentage = 0.0;

  final List<String> _statusOptions = [
    'draft',
    'active',
    'completed',
    'cancelled',
  ];

  final Map<String, String> _statusLabels = {
    'draft': 'مسودة',
    'active': 'نشطة',
    'completed': 'مكتملة',
    'cancelled': 'ملغية',
  };

  @override
  void dispose() {
    _employeeIdController.dispose();
    _progressNotesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.9,
        padding: const EdgeInsets.all(24),
        child: Column(
          children: [
            _buildHeader(),
            const SizedBox(height: 24),
            Expanded(
              child: Form(
                key: _formKey,
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildBasicInfo(),
                      const SizedBox(height: 24),
                      _buildCareerPathSection(),
                      const SizedBox(height: 24),
                      _buildDatesSection(),
                      const SizedBox(height: 24),
                      _buildGoalsSection(),
                      const SizedBox(height: 24),
                      _buildSkillGapsSection(),
                      const SizedBox(height: 24),
                      _buildActionsSection(),
                      const SizedBox(height: 24),
                      _buildMilestonesSection(),
                      const SizedBox(height: 24),
                      _buildProgressSection(),
                    ],
                  ),
                ),
              ),
            ),
            const SizedBox(height: 24),
            _buildActionButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: RevolutionaryColors.infoTurquoise.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(
            Icons.trending_up,
            color: RevolutionaryColors.infoTurquoise,
            size: 24,
          ),
        ),
        const SizedBox(width: 16),
        const Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'إنشاء خطة تطوير مهني',
                style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
              ),
              Text(
                'إنشاء خطة تطوير مخصصة للموظف',
                style: TextStyle(fontSize: 14, color: Colors.grey),
              ),
            ],
          ),
        ),
        IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: const Icon(Icons.close),
        ),
      ],
    );
  }

  Widget _buildBasicInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'معلومات الموظف',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: TextFormField(
                controller: _employeeIdController,
                decoration: InputDecoration(
                  labelText: 'رقم الموظف *',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  prefixIcon: const Icon(Icons.person),
                ),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'يرجى إدخال رقم الموظف';
                  }
                  if (int.tryParse(value) == null) {
                    return 'رقم غير صحيح';
                  }
                  return null;
                },
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: DropdownButtonFormField<String>(
                value: _status,
                decoration: InputDecoration(
                  labelText: 'حالة الخطة',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  prefixIcon: const Icon(Icons.info),
                ),
                items: _statusOptions.map((status) {
                  return DropdownMenuItem(
                    value: status,
                    child: Text(_statusLabels[status] ?? status),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _status = value ?? 'draft';
                  });
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildCareerPathSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'المسار الوظيفي',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        DropdownButtonFormField<int>(
          value: _selectedCareerPathId,
          decoration: InputDecoration(
            labelText: 'المسار الوظيفي المستهدف *',
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
            prefixIcon: const Icon(Icons.work),
          ),
          items: widget.careerPaths.map((path) {
            return DropdownMenuItem(value: path.id, child: Text(path.pathName));
          }).toList(),
          onChanged: (value) {
            setState(() {
              _selectedCareerPathId = value;
              _selectedTargetLevelId = null; // إعادة تعيين المستوى المستهدف
            });
          },
          validator: (value) {
            if (value == null) {
              return 'يرجى اختيار المسار الوظيفي';
            }
            return null;
          },
        ),
        if (_selectedCareerPathId != null) ...[
          const SizedBox(height: 16),
          DropdownButtonFormField<int>(
            value: _selectedTargetLevelId,
            decoration: InputDecoration(
              labelText: 'المستوى المستهدف',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              prefixIcon: const Icon(Icons.trending_up),
            ),
            items: _getSelectedPathLevels().asMap().entries.map((entry) {
              final index = entry.key;
              final level = entry.value;
              return DropdownMenuItem(
                value: index,
                child: Text('${level.title} (مستوى ${level.level})'),
              );
            }).toList(),
            onChanged: (value) {
              setState(() {
                _selectedTargetLevelId = value;
              });
            },
          ),
        ],
      ],
    );
  }

  Widget _buildDatesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'التواريخ',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'تاريخ البداية *',
                    style: TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
                  ),
                  const SizedBox(height: 8),
                  InkWell(
                    onTap: _selectStartDate,
                    child: Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Row(
                        children: [
                          const Icon(Icons.calendar_today),
                          const SizedBox(width: 12),
                          Text(
                            '${_startDate.day}/${_startDate.month}/${_startDate.year}',
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'التاريخ المستهدف *',
                    style: TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
                  ),
                  const SizedBox(height: 8),
                  InkWell(
                    onTap: _selectTargetDate,
                    child: Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Row(
                        children: [
                          const Icon(Icons.event),
                          const SizedBox(width: 12),
                          Text(
                            '${_targetDate.day}/${_targetDate.month}/${_targetDate.year}',
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildGoalsSection() {
    return _buildListSection(
      title: 'أهداف التطوير',
      items: _developmentGoals,
      onAdd: _addGoal,
      onRemove: (index) => setState(() => _developmentGoals.removeAt(index)),
      icon: Icons.flag,
      color: RevolutionaryColors.successGlow,
    );
  }

  Widget _buildSkillGapsSection() {
    return _buildListSection(
      title: 'فجوات المهارات',
      items: _skillGaps,
      onAdd: _addSkillGap,
      onRemove: (index) => setState(() => _skillGaps.removeAt(index)),
      icon: Icons.warning,
      color: RevolutionaryColors.warningAmber,
    );
  }

  Widget _buildActionsSection() {
    return _buildListSection(
      title: 'إجراءات التطوير',
      items: _developmentActions,
      onAdd: _addAction,
      onRemove: (index) => setState(() => _developmentActions.removeAt(index)),
      icon: Icons.assignment,
      color: RevolutionaryColors.infoTurquoise,
    );
  }

  Widget _buildMilestonesSection() {
    return _buildListSection(
      title: 'المعالم والإنجازات',
      items: _milestones,
      onAdd: _addMilestone,
      onRemove: (index) => setState(() => _milestones.removeAt(index)),
      icon: Icons.emoji_events,
      color: RevolutionaryColors.damascusSky,
    );
  }

  Widget _buildListSection({
    required String title,
    required List<String> items,
    required VoidCallback onAdd,
    required Function(int) onRemove,
    required IconData icon,
    required Color color,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(
              child: Text(
                title,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            ElevatedButton.icon(
              onPressed: onAdd,
              icon: Icon(Icons.add, size: 16),
              label: const Text('إضافة'),
              style: ElevatedButton.styleFrom(backgroundColor: color),
            ),
          ],
        ),
        const SizedBox(height: 16),
        if (items.isEmpty)
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey[300]!),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Center(
              child: Text(
                'لا توجد عناصر\nاضغط "إضافة" لإضافة عنصر جديد',
                textAlign: TextAlign.center,
                style: TextStyle(color: Colors.grey),
              ),
            ),
          )
        else
          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: items.length,
            itemBuilder: (context, index) {
              return Card(
                margin: const EdgeInsets.only(bottom: 8),
                child: ListTile(
                  leading: Icon(icon, color: color),
                  title: Text(items[index]),
                  trailing: IconButton(
                    onPressed: () => onRemove(index),
                    icon: const Icon(Icons.delete, color: Colors.red),
                  ),
                ),
              );
            },
          ),
      ],
    );
  }

  Widget _buildProgressSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'التقدم والملاحظات',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            const Text('نسبة التقدم: '),
            Expanded(
              child: Slider(
                value: _progressPercentage,
                min: 0,
                max: 100,
                divisions: 20,
                label: '${_progressPercentage.round()}%',
                onChanged: (value) {
                  setState(() {
                    _progressPercentage = value;
                  });
                },
              ),
            ),
            Text('${_progressPercentage.round()}%'),
          ],
        ),
        const SizedBox(height: 16),
        TextFormField(
          controller: _progressNotesController,
          maxLines: 3,
          decoration: InputDecoration(
            labelText: 'ملاحظات التقدم',
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
            prefixIcon: const Icon(Icons.note),
          ),
        ),
      ],
    );
  }

  Widget _buildActionButtons() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('إلغاء'),
        ),
        const SizedBox(width: 16),
        ElevatedButton(
          onPressed: _savePlan,
          style: ElevatedButton.styleFrom(
            backgroundColor: RevolutionaryColors.infoTurquoise,
            padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 12),
          ),
          child: const Text('حفظ الخطة', style: TextStyle(color: Colors.white)),
        ),
      ],
    );
  }

  List<CareerLevel> _getSelectedPathLevels() {
    if (_selectedCareerPathId == null) return [];
    final path = widget.careerPaths.firstWhere(
      (p) => p.id == _selectedCareerPathId,
    );
    return path.levels;
  }

  void _selectStartDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _startDate,
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365 * 5)),
    );
    if (date != null) {
      setState(() {
        _startDate = date;
        if (_targetDate.isBefore(_startDate)) {
          _targetDate = _startDate.add(const Duration(days: 365));
        }
      });
    }
  }

  void _selectTargetDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _targetDate,
      firstDate: _startDate,
      lastDate: DateTime.now().add(const Duration(days: 365 * 5)),
    );
    if (date != null) {
      setState(() {
        _targetDate = date;
      });
    }
  }

  void _addGoal() => _showAddItemDialog('إضافة هدف تطوير', _developmentGoals);
  void _addSkillGap() => _showAddItemDialog('إضافة فجوة مهارة', _skillGaps);
  void _addAction() =>
      _showAddItemDialog('إضافة إجراء تطوير', _developmentActions);
  void _addMilestone() => _showAddItemDialog('إضافة معلم', _milestones);

  void _showAddItemDialog(String title, List<String> list) {
    showDialog(
      context: context,
      builder: (context) {
        final controller = TextEditingController();
        return AlertDialog(
          title: Text(title),
          content: TextFormField(
            controller: controller,
            decoration: const InputDecoration(
              labelText: 'النص',
              border: OutlineInputBorder(),
            ),
            autofocus: true,
            maxLines: 2,
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () {
                if (controller.text.trim().isNotEmpty) {
                  setState(() {
                    list.add(controller.text.trim());
                  });
                  Navigator.of(context).pop();
                }
              },
              child: const Text('إضافة'),
            ),
          ],
        );
      },
    );
  }

  void _savePlan() {
    if (_formKey.currentState!.validate()) {
      final plan = CareerDevelopmentPlan(
        employeeId: int.parse(_employeeIdController.text),
        careerPathId: _selectedCareerPathId,
        targetDate: _targetDate,
        status: _status,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      widget.onSave(plan);
      Navigator.of(context).pop();
    }
  }
}
