/// مكون الرسم البياني المحسن للرواتب
/// يوفر رسوم بيانية تفاعلية لبيانات الرواتب مع إمكانيات متقدمة
library;

import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import '../constants/revolutionary_design_colors.dart';
import '../models/hr_models.dart';

class EnhancedPayrollChart extends StatefulWidget {
  final List<PayrollRecord> payrollData;
  final PayrollChartType chartType;
  final String title;
  final bool showLegend;
  final bool showTooltips;
  final bool showAnimation;
  final Function(PayrollRecord)? onDataPointTap;

  const EnhancedPayrollChart({
    super.key,
    required this.payrollData,
    required this.chartType,
    required this.title,
    this.showLegend = true,
    this.showTooltips = true,
    this.showAnimation = true,
    this.onDataPointTap,
  });

  @override
  State<EnhancedPayrollChart> createState() => _EnhancedPayrollChartState();
}

class _EnhancedPayrollChartState extends State<EnhancedPayrollChart>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;
  int _touchedIndex = -1;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOutCubic,
    );

    if (widget.showAnimation) {
      _animationController.forward();
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 8,
      shadowColor: RevolutionaryColors.syrianGold.withValues(alpha: 0.3),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.white,
              RevolutionaryColors.syrianGold.withValues(alpha: 0.02),
            ],
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildChartHeader(),
              const SizedBox(height: 20),
              Expanded(
                child: AnimatedBuilder(
                  animation: _animation,
                  builder: (context, child) {
                    return Transform.scale(
                      scale: widget.showAnimation ? _animation.value : 1.0,
                      child: Opacity(
                        opacity: widget.showAnimation ? _animation.value : 1.0,
                        child: _buildChart(),
                      ),
                    );
                  },
                ),
              ),
              if (widget.showLegend) ...[
                const SizedBox(height: 16),
                _buildLegend(),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildChartHeader() {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: RevolutionaryColors.syrianGold.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            _getChartIcon(),
            color: RevolutionaryColors.syrianGold,
            size: 24,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                widget.title,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: RevolutionaryColors.textPrimary,
                ),
              ),
              Text(
                '${widget.payrollData.length} موظف',
                style: TextStyle(fontSize: 14, color: Colors.grey[600]),
              ),
            ],
          ),
        ),
        _buildChartTypeSelector(),
      ],
    );
  }

  Widget _buildChartTypeSelector() {
    return Container(
      padding: const EdgeInsets.all(4),
      decoration: BoxDecoration(
        color: Colors.grey.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: PayrollChartType.values.map((type) {
          final isSelected = type == widget.chartType;
          return GestureDetector(
            onTap: () {
              // يمكن إضافة callback لتغيير نوع الرسم البياني
            },
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: isSelected
                    ? RevolutionaryColors.syrianGold
                    : Colors.transparent,
                borderRadius: BorderRadius.circular(6),
              ),
              child: Icon(
                _getTypeIcon(type),
                size: 16,
                color: isSelected ? Colors.white : Colors.grey[600],
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildChart() {
    switch (widget.chartType) {
      case PayrollChartType.bar:
        return _buildBarChart();
      case PayrollChartType.pie:
        return _buildPieChart();
      case PayrollChartType.line:
        return _buildLineChart();
      case PayrollChartType.area:
        return _buildAreaChart();
    }
  }

  Widget _buildBarChart() {
    if (widget.payrollData.isEmpty) {
      return _buildEmptyState();
    }

    final maxSalary = widget.payrollData
        .map((e) => e.netSalary)
        .reduce((a, b) => a > b ? a : b);

    return BarChart(
      BarChartData(
        alignment: BarChartAlignment.spaceAround,
        maxY: maxSalary * 1.2,
        barTouchData: BarTouchData(
          enabled: widget.showTooltips,
          touchTooltipData: BarTouchTooltipData(
            getTooltipItem: (group, groupIndex, rod, rodIndex) {
              final record = widget.payrollData[groupIndex];
              return BarTooltipItem(
                'صافي الراتب\n${record.netSalary.toStringAsFixed(0)} ل.س',
                const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              );
            },
          ),
          touchCallback: (FlTouchEvent event, barTouchResponse) {
            if (event is FlTapUpEvent &&
                barTouchResponse?.spot != null &&
                widget.onDataPointTap != null) {
              final index = barTouchResponse!.spot!.touchedBarGroupIndex;
              widget.onDataPointTap!(widget.payrollData[index]);
            }
          },
        ),
        titlesData: FlTitlesData(
          show: true,
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              getTitlesWidget: (value, meta) {
                if (value.toInt() >= 0 &&
                    value.toInt() < widget.payrollData.length) {
                  return Padding(
                    padding: const EdgeInsets.only(top: 8),
                    child: Text(
                      'موظف ${value.toInt() + 1}',
                      style: const TextStyle(fontSize: 10),
                    ),
                  );
                }
                return const Text('');
              },
            ),
          ),
          leftTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 60,
              getTitlesWidget: (value, meta) {
                return Text(
                  '${(value / 1000).toStringAsFixed(0)}ك',
                  style: const TextStyle(fontSize: 10),
                );
              },
            ),
          ),
          topTitles: const AxisTitles(
            sideTitles: SideTitles(showTitles: false),
          ),
          rightTitles: const AxisTitles(
            sideTitles: SideTitles(showTitles: false),
          ),
        ),
        borderData: FlBorderData(show: false),
        barGroups: widget.payrollData.asMap().entries.map((entry) {
          final index = entry.key;
          final record = entry.value;
          return BarChartGroupData(
            x: index,
            barRods: [
              BarChartRodData(
                toY: record.netSalary,
                gradient: LinearGradient(
                  begin: Alignment.bottomCenter,
                  end: Alignment.topCenter,
                  colors: [
                    RevolutionaryColors.syrianGold,
                    RevolutionaryColors.syrianGold.withValues(alpha: 0.7),
                  ],
                ),
                width: 20,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(6),
                  topRight: Radius.circular(6),
                ),
              ),
            ],
          );
        }).toList(),
      ),
    );
  }

  Widget _buildPieChart() {
    if (widget.payrollData.isEmpty) {
      return _buildEmptyState();
    }

    final totalSalary = widget.payrollData
        .map((e) => e.netSalary)
        .reduce((a, b) => a + b);

    return PieChart(
      PieChartData(
        pieTouchData: PieTouchData(
          touchCallback: (FlTouchEvent event, pieTouchResponse) {
            setState(() {
              if (!event.isInterestedForInteractions ||
                  pieTouchResponse == null ||
                  pieTouchResponse.touchedSection == null) {
                _touchedIndex = -1;
                return;
              }
              _touchedIndex =
                  pieTouchResponse.touchedSection!.touchedSectionIndex;
            });
          },
        ),
        borderData: FlBorderData(show: false),
        sectionsSpace: 2,
        centerSpaceRadius: 40,
        sections: widget.payrollData.asMap().entries.map((entry) {
          final index = entry.key;
          final record = entry.value;
          final isTouched = index == _touchedIndex;
          final fontSize = isTouched ? 16.0 : 12.0;
          final radius = isTouched ? 80.0 : 70.0;

          return PieChartSectionData(
            color: _getColorForIndex(index),
            value: record.netSalary,
            title:
                '${((record.netSalary / totalSalary) * 100).toStringAsFixed(1)}%',
            radius: radius,
            titleStyle: TextStyle(
              fontSize: fontSize,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildLineChart() {
    // تنفيذ الرسم البياني الخطي
    return const Center(child: Text('الرسم البياني الخطي قيد التطوير'));
  }

  Widget _buildAreaChart() {
    // تنفيذ رسم المنطقة
    return const Center(child: Text('رسم المنطقة قيد التطوير'));
  }

  Widget _buildLegend() {
    return Wrap(
      spacing: 16,
      runSpacing: 8,
      children: widget.payrollData.asMap().entries.map((entry) {
        final index = entry.key;
        return Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 12,
              height: 12,
              decoration: BoxDecoration(
                color: _getColorForIndex(index),
                shape: BoxShape.circle,
              ),
            ),
            const SizedBox(width: 6),
            Text('موظف ${index + 1}', style: const TextStyle(fontSize: 12)),
          ],
        );
      }).toList(),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.bar_chart, size: 64, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text(
            'لا توجد بيانات للعرض',
            style: TextStyle(fontSize: 16, color: Colors.grey[600]),
          ),
        ],
      ),
    );
  }

  IconData _getChartIcon() {
    switch (widget.chartType) {
      case PayrollChartType.bar:
        return Icons.bar_chart;
      case PayrollChartType.pie:
        return Icons.pie_chart;
      case PayrollChartType.line:
        return Icons.show_chart;
      case PayrollChartType.area:
        return Icons.area_chart;
    }
  }

  IconData _getTypeIcon(PayrollChartType type) {
    switch (type) {
      case PayrollChartType.bar:
        return Icons.bar_chart;
      case PayrollChartType.pie:
        return Icons.pie_chart;
      case PayrollChartType.line:
        return Icons.show_chart;
      case PayrollChartType.area:
        return Icons.area_chart;
    }
  }

  Color _getColorForIndex(int index) {
    final colors = [
      RevolutionaryColors.syrianGold,
      RevolutionaryColors.successGlow,
      RevolutionaryColors.damascusSky,
      RevolutionaryColors.warningAmber,
      RevolutionaryColors.errorCoral,
    ];
    return colors[index % colors.length];
  }
}

enum PayrollChartType { bar, pie, line, area }
