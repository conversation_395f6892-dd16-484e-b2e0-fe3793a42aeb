/// خدمة إدارة الموظفين
/// توفر عمليات CRUD للموظفين مع التكامل مع المحاسبة
library;

import 'package:sqflite_sqlcipher/sqflite.dart';
import '../database/database_helper.dart';
import '../models/hr_models.dart';
import '../constants/app_constants.dart';
import '../services/logging_service.dart';
import '../services/audit_service.dart';
import '../exceptions/validation_exception.dart';

class EmployeeService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  /// التحقق من وجود جداول الموارد البشرية وإنشاؤها إذا لم تكن موجودة
  Future<void> ensureHRTablesExist() async {
    try {
      final db = await _databaseHelper.database;

      // التحقق من وجود جدول الموظفين
      final result = await db.rawQuery(
        "SELECT name FROM sqlite_master WHERE type='table' AND name='employees'",
      );

      if (result.isEmpty) {
        LoggingService.warning(
          'جدول الموظفين غير موجود، سيتم إنشاؤه الآن',
          category: 'EmployeeService',
        );

        // إنشاء جداول الموارد البشرية
        await _createHRTablesManually(db);

        LoggingService.info(
          'تم إنشاء جداول الموارد البشرية بنجاح',
          category: 'EmployeeService',
        );
      }
    } catch (e) {
      LoggingService.error(
        'خطأ في التحقق من جداول الموارد البشرية',
        category: 'EmployeeService',
        data: {'error': e.toString()},
      );
      rethrow;
    }
  }

  /// إنشاء جداول الموارد البشرية يدوياً
  Future<void> _createHRTablesManually(Database db) async {
    // جدول الأقسام
    await db.execute('''
      CREATE TABLE IF NOT EXISTS ${AppConstants.departmentsTable} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL UNIQUE,
        description TEXT,
        manager_id INTEGER,
        budget REAL DEFAULT 0,
        is_active INTEGER NOT NULL DEFAULT 1,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    ''');

    // جدول المناصب
    await db.execute('''
      CREATE TABLE IF NOT EXISTS ${AppConstants.positionsTable} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        title TEXT NOT NULL,
        department_id INTEGER,
        description TEXT,
        min_salary REAL DEFAULT 0,
        max_salary REAL DEFAULT 0,
        requirements TEXT,
        is_active INTEGER NOT NULL DEFAULT 1,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        FOREIGN KEY (department_id) REFERENCES ${AppConstants.departmentsTable} (id) ON DELETE SET NULL
      )
    ''');

    // جدول الموظفين
    await db.execute('''
      CREATE TABLE IF NOT EXISTS ${AppConstants.employeesTable} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        employee_number TEXT NOT NULL UNIQUE,
        national_id TEXT NOT NULL UNIQUE,
        first_name TEXT NOT NULL,
        last_name TEXT NOT NULL,
        full_name TEXT NOT NULL,
        date_of_birth TEXT,
        gender TEXT,
        marital_status TEXT,
        phone TEXT,
        email TEXT,
        address TEXT,
        emergency_contact_name TEXT,
        emergency_contact_phone TEXT,
        department_id INTEGER,
        position_id INTEGER,
        hire_date TEXT NOT NULL,
        termination_date TEXT,
        status TEXT NOT NULL DEFAULT '${AppConstants.employeeStatusActive}',
        basic_salary REAL NOT NULL DEFAULT 0,
        cost_center_account_id INTEGER,
        bank_account_number TEXT,
        bank_name TEXT,
        social_insurance_number TEXT,
        tax_number TEXT,
        photo_path TEXT,
        notes TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        FOREIGN KEY (department_id) REFERENCES ${AppConstants.departmentsTable} (id) ON DELETE SET NULL,
        FOREIGN KEY (position_id) REFERENCES ${AppConstants.positionsTable} (id) ON DELETE SET NULL
      )
    ''');

    // إدراج بيانات افتراضية للأقسام
    await db.execute('''
      INSERT OR IGNORE INTO ${AppConstants.departmentsTable}
      (id, name, description, budget, is_active, created_at, updated_at)
      VALUES
      (1, 'الإدارة العامة', 'قسم الإدارة العامة والتخطيط', 0, 1, datetime('now'), datetime('now')),
      (2, 'المحاسبة', 'قسم المحاسبة والشؤون المالية', 0, 1, datetime('now'), datetime('now')),
      (3, 'الموارد البشرية', 'قسم الموارد البشرية والتطوير', 0, 1, datetime('now'), datetime('now')),
      (4, 'المبيعات', 'قسم المبيعات والتسويق', 0, 1, datetime('now'), datetime('now')),
      (5, 'المشتريات', 'قسم المشتريات والمخازن', 0, 1, datetime('now'), datetime('now'))
    ''');

    // إدراج بيانات افتراضية للمناصب
    await db.execute('''
      INSERT OR IGNORE INTO ${AppConstants.positionsTable}
      (id, title, department_id, description, min_salary, max_salary, is_active, created_at, updated_at)
      VALUES
      (1, 'مدير عام', 1, 'المدير العام للشركة', 100000, 200000, 1, datetime('now'), datetime('now')),
      (2, 'مدير مالي', 2, 'مدير الشؤون المالية والمحاسبة', 80000, 120000, 1, datetime('now'), datetime('now')),
      (3, 'محاسب', 2, 'محاسب عام', 40000, 60000, 1, datetime('now'), datetime('now')),
      (4, 'مدير موارد بشرية', 3, 'مدير الموارد البشرية', 70000, 100000, 1, datetime('now'), datetime('now')),
      (5, 'مدير مبيعات', 4, 'مدير المبيعات والتسويق', 70000, 100000, 1, datetime('now'), datetime('now'))
    ''');

    // إنشاء موظف افتراضي إذا لم يوجد أي موظف
    await _createDefaultEmployeeIfNeeded(db);
  }

  /// إنشاء موظف افتراضي إذا لم يوجد أي موظف
  Future<void> _createDefaultEmployeeIfNeeded(Database db) async {
    try {
      // التحقق من وجود موظفين
      final result = await db.rawQuery(
        'SELECT COUNT(*) as count FROM ${AppConstants.employeesTable}',
      );

      final count = result.first['count'] as int;

      if (count == 0) {
        // إنشاء موظف افتراضي
        await db.execute('''
          INSERT INTO ${AppConstants.employeesTable}
          (employee_number, national_id, first_name, last_name, full_name,
           department_id, position_id, hire_date, basic_salary, status,
           created_at, updated_at)
          VALUES
          ('EMP001', '12345678901', 'المدير', 'العام', 'المدير العام',
           1, 1, datetime('now'), 100000, '${AppConstants.employeeStatusActive}',
           datetime('now'), datetime('now'))
        ''');

        LoggingService.info(
          'تم إنشاء موظف افتراضي',
          category: 'EmployeeService',
        );
      }
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء الموظف الافتراضي',
        category: 'EmployeeService',
        data: {'error': e.toString()},
      );
    }
  }

  /// الحصول على جميع الموظفين
  Future<List<Employee>> getAllEmployees({
    bool activeOnly = false,
    int? departmentId,
    String? searchQuery,
  }) async {
    try {
      // التحقق من وجود الجداول أولاً
      await ensureHRTablesExist();

      final db = await _databaseHelper.database;

      String whereClause = '1=1';
      List<dynamic> whereArgs = [];

      if (activeOnly) {
        whereClause += ' AND status = ?';
        whereArgs.add(AppConstants.employeeStatusActive);
      }

      if (departmentId != null) {
        whereClause += ' AND department_id = ?';
        whereArgs.add(departmentId);
      }

      if (searchQuery != null && searchQuery.isNotEmpty) {
        whereClause +=
            ' AND (full_name LIKE ? OR employee_number LIKE ? OR national_id LIKE ?)';
        final searchPattern = '%$searchQuery%';
        whereArgs.addAll([searchPattern, searchPattern, searchPattern]);
      }

      final result = await db.query(
        AppConstants.employeesTable,
        where: whereClause,
        whereArgs: whereArgs.isNotEmpty ? whereArgs : null,
        orderBy: 'full_name ASC',
      );

      return result.map((map) => Employee.fromMap(map)).toList();
    } catch (e) {
      LoggingService.error(
        'خطأ في الحصول على قائمة الموظفين',
        category: 'EmployeeService',
        data: {'error': e.toString()},
      );
      rethrow;
    }
  }

  /// الحصول على موظف بالمعرف
  Future<Employee?> getEmployeeById(int id) async {
    try {
      await ensureHRTablesExist();
      final db = await _databaseHelper.database;

      final result = await db.query(
        AppConstants.employeesTable,
        where: 'id = ?',
        whereArgs: [id],
        limit: 1,
      );

      if (result.isEmpty) return null;
      return Employee.fromMap(result.first);
    } catch (e) {
      LoggingService.error(
        'خطأ في الحصول على الموظف',
        category: 'EmployeeService',
        data: {'id': id, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// الحصول على موظف برقم الموظف
  Future<Employee?> getEmployeeByNumber(String employeeNumber) async {
    try {
      final db = await _databaseHelper.database;

      final result = await db.query(
        AppConstants.employeesTable,
        where: 'employee_number = ?',
        whereArgs: [employeeNumber],
        limit: 1,
      );

      if (result.isEmpty) return null;
      return Employee.fromMap(result.first);
    } catch (e) {
      LoggingService.error(
        'خطأ في الحصول على الموظف برقم الموظف',
        category: 'EmployeeService',
        data: {'employeeNumber': employeeNumber, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// إضافة موظف جديد
  Future<Employee> addEmployee(Employee employee) async {
    try {
      await ensureHRTablesExist();

      // التحقق من صحة البيانات
      await _validateEmployee(employee);

      final db = await _databaseHelper.database;

      // التحقق من عدم تكرار رقم الموظف
      final existingByNumber = await getEmployeeByNumber(
        employee.employeeNumber,
      );
      if (existingByNumber != null) {
        throw ValidationException('رقم الموظف موجود مسبقاً');
      }

      // التحقق من عدم تكرار الرقم الوطني
      final existingByNationalId = await db.query(
        AppConstants.employeesTable,
        where: 'national_id = ?',
        whereArgs: [employee.nationalId],
        limit: 1,
      );

      if (existingByNationalId.isNotEmpty) {
        throw ValidationException('الرقم الوطني موجود مسبقاً');
      }

      final now = DateTime.now();
      final employeeData = employee.copyWith(createdAt: now, updatedAt: now);

      final id = await db.insert(
        AppConstants.employeesTable,
        employeeData.toMap(),
      );

      final newEmployee = employeeData.copyWith(id: id);

      // تسجيل في سجل المراجعة
      await AuditService.log(
        action: 'CREATE',
        entityType: 'Employee',
        entityId: id,
        description: 'إضافة موظف جديد: ${employee.fullName}',
        newValues: newEmployee.toMap(),
      );

      LoggingService.info(
        'تم إضافة موظف جديد بنجاح',
        category: 'EmployeeService',
        data: {'id': id, 'name': employee.fullName},
      );

      return newEmployee;
    } catch (e) {
      LoggingService.error(
        'خطأ في إضافة الموظف',
        category: 'EmployeeService',
        data: {'employee': employee.toMap(), 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// تحديث بيانات موظف
  Future<Employee> updateEmployee(Employee employee) async {
    try {
      if (employee.id == null) {
        throw ValidationException('معرف الموظف مطلوب للتحديث');
      }

      // التحقق من صحة البيانات
      await _validateEmployee(employee);

      final db = await _databaseHelper.database;

      // الحصول على البيانات القديمة
      final oldEmployee = await getEmployeeById(employee.id!);
      if (oldEmployee == null) {
        throw ValidationException('الموظف غير موجود');
      }

      // التحقق من عدم تكرار رقم الموظف (إذا تم تغييره)
      if (employee.employeeNumber != oldEmployee.employeeNumber) {
        final existingByNumber = await getEmployeeByNumber(
          employee.employeeNumber,
        );
        if (existingByNumber != null && existingByNumber.id != employee.id) {
          throw ValidationException('رقم الموظف موجود مسبقاً');
        }
      }

      // التحقق من عدم تكرار الرقم الوطني (إذا تم تغييره)
      if (employee.nationalId != oldEmployee.nationalId) {
        final existingByNationalId = await db.query(
          AppConstants.employeesTable,
          where: 'national_id = ? AND id != ?',
          whereArgs: [employee.nationalId, employee.id],
          limit: 1,
        );

        if (existingByNationalId.isNotEmpty) {
          throw ValidationException('الرقم الوطني موجود مسبقاً');
        }
      }

      final updatedEmployee = employee.copyWith(updatedAt: DateTime.now());

      await db.update(
        AppConstants.employeesTable,
        updatedEmployee.toMap(),
        where: 'id = ?',
        whereArgs: [employee.id],
      );

      // تسجيل في سجل المراجعة
      await AuditService.log(
        action: 'UPDATE',
        entityType: 'Employee',
        entityId: employee.id,
        description: 'تحديث بيانات الموظف: ${employee.fullName}',
        oldValues: oldEmployee.toMap(),
        newValues: updatedEmployee.toMap(),
      );

      LoggingService.info(
        'تم تحديث بيانات الموظف بنجاح',
        category: 'EmployeeService',
        data: {'id': employee.id, 'name': employee.fullName},
      );

      return updatedEmployee;
    } catch (e) {
      LoggingService.error(
        'خطأ في تحديث الموظف',
        category: 'EmployeeService',
        data: {'employee': employee.toMap(), 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// حذف موظف
  Future<void> deleteEmployee(int id) async {
    try {
      final db = await _databaseHelper.database;

      // الحصول على بيانات الموظف قبل الحذف
      final employee = await getEmployeeById(id);
      if (employee == null) {
        throw ValidationException('الموظف غير موجود');
      }

      // التحقق من عدم وجود بيانات مرتبطة
      await _checkEmployeeDependencies(id);

      await db.delete(
        AppConstants.employeesTable,
        where: 'id = ?',
        whereArgs: [id],
      );

      // تسجيل في سجل المراجعة
      await AuditService.log(
        action: 'DELETE',
        entityType: 'Employee',
        entityId: id,
        description: 'حذف الموظف: ${employee.fullName}',
        oldValues: employee.toMap(),
      );

      LoggingService.info(
        'تم حذف الموظف بنجاح',
        category: 'EmployeeService',
        data: {'id': id, 'name': employee.fullName},
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في حذف الموظف',
        category: 'EmployeeService',
        data: {'id': id, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// التحقق من صحة بيانات الموظف
  Future<void> _validateEmployee(Employee employee) async {
    if (employee.employeeNumber.trim().isEmpty) {
      throw ValidationException('رقم الموظف مطلوب');
    }

    if (employee.nationalId.trim().isEmpty) {
      throw ValidationException('الرقم الوطني مطلوب');
    }

    if (employee.firstName.trim().isEmpty) {
      throw ValidationException('الاسم الأول مطلوب');
    }

    if (employee.lastName.trim().isEmpty) {
      throw ValidationException('الاسم الأخير مطلوب');
    }

    if (employee.basicSalary < 0) {
      throw ValidationException('الراتب الأساسي لا يمكن أن يكون سالباً');
    }

    // التحقق من صحة الرقم الوطني السوري (11 رقم)
    if (employee.nationalId.length != 11 ||
        !RegExp(r'^\d{11}$').hasMatch(employee.nationalId)) {
      throw ValidationException('الرقم الوطني يجب أن يكون 11 رقماً');
    }

    // التحقق من صحة رقم الهاتف إذا تم إدخاله
    if (employee.phone != null && employee.phone!.isNotEmpty) {
      if (!RegExp(r'^[+]?[0-9\s\-\(\)]+$').hasMatch(employee.phone!)) {
        throw ValidationException('رقم الهاتف غير صحيح');
      }
    }

    // التحقق من صحة البريد الإلكتروني إذا تم إدخاله
    if (employee.email != null && employee.email!.isNotEmpty) {
      if (!RegExp(
        r'^[\w\-\.]+@([\w\-]+\.)+[\w\-]{2,4}$',
      ).hasMatch(employee.email!)) {
        throw ValidationException('البريد الإلكتروني غير صحيح');
      }
    }
  }

  /// التحقق من وجود بيانات مرتبطة بالموظف
  Future<void> _checkEmployeeDependencies(int employeeId) async {
    final db = await _databaseHelper.database;

    // التحقق من وجود سجلات حضور
    final attendanceCount = await db.rawQuery(
      'SELECT COUNT(*) as count FROM ${AppConstants.attendanceTable} WHERE employee_id = ?',
      [employeeId],
    );

    if ((attendanceCount.first['count'] as int) > 0) {
      throw ValidationException(
        'لا يمكن حذف الموظف لوجود سجلات حضور مرتبطة به',
      );
    }

    // التحقق من وجود قروض
    final loansCount = await db.rawQuery(
      'SELECT COUNT(*) as count FROM ${AppConstants.loansTable} WHERE employee_id = ?',
      [employeeId],
    );

    if ((loansCount.first['count'] as int) > 0) {
      throw ValidationException('لا يمكن حذف الموظف لوجود قروض مرتبطة به');
    }

    // التحقق من وجود كشوف رواتب
    final payrollCount = await db.rawQuery(
      'SELECT COUNT(*) as count FROM ${AppConstants.payrollDetailsTable} WHERE employee_id = ?',
      [employeeId],
    );

    if ((payrollCount.first['count'] as int) > 0) {
      throw ValidationException(
        'لا يمكن حذف الموظف لوجود كشوف رواتب مرتبطة به',
      );
    }
  }

  /// توليد رقم موظف تلقائي
  Future<String> generateEmployeeNumber() async {
    try {
      final db = await _databaseHelper.database;

      final result = await db.rawQuery(
        'SELECT MAX(CAST(SUBSTR(employee_number, 4) AS INTEGER)) as max_num FROM ${AppConstants.employeesTable} WHERE employee_number LIKE "EMP%"',
      );

      int nextNumber = 1;
      if (result.isNotEmpty && result.first['max_num'] != null) {
        nextNumber = (result.first['max_num'] as int) + 1;
      }

      return 'EMP${nextNumber.toString().padLeft(4, '0')}';
    } catch (e) {
      LoggingService.error(
        'خطأ في توليد رقم الموظف',
        category: 'EmployeeService',
        data: {'error': e.toString()},
      );
      // في حالة الخطأ، إرجاع رقم افتراضي
      return 'EMP${DateTime.now().millisecondsSinceEpoch.toString().substring(8)}';
    }
  }
}
