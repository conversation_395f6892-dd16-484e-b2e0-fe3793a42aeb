/// خدمة إدارة المستخدم الحالي
/// توفر معلومات المستخدم المسجل دخوله حالياً
library;

import 'package:shared_preferences/shared_preferences.dart';
import '../database/database_helper.dart';
import '../services/logging_service.dart';
import '../models/hr_models.dart';

class CurrentUserService {
  static final CurrentUserService _instance = CurrentUserService._internal();
  factory CurrentUserService() => _instance;
  CurrentUserService._internal();

  final DatabaseHelper _databaseHelper = DatabaseHelper();

  // معلومات المستخدم الحالي المخزنة في الذاكرة
  Employee? _currentUser;
  int? _currentUserId;
  String? _currentUserName;

  /// الحصول على معرف المستخدم الحالي
  int get currentUserId => _currentUserId ?? 1; // افتراضي للتطوير

  /// الحصول على اسم المستخدم الحالي
  String get currentUserName =>
      _currentUserName ?? 'المدير العام'; // افتراضي للتطوير

  /// الحصول على المستخدم الحالي
  Employee? get currentUser => _currentUser;

  /// تسجيل دخول المستخدم
  Future<bool> login(String username, String password) async {
    try {
      // البحث عن المستخدم في قاعدة البيانات
      final db = await _databaseHelper.database;
      final result = await db.query(
        'employees',
        where: 'email = ? OR employee_number = ?',
        whereArgs: [username, username],
        limit: 1,
      );

      if (result.isEmpty) {
        LoggingService.warning(
          'محاولة تسجيل دخول بمستخدم غير موجود',
          category: 'CurrentUserService',
          data: {'username': username},
        );
        return false;
      }

      final employee = Employee.fromMap(result.first);

      // في التطبيق الحقيقي، يجب التحقق من كلمة المرور المشفرة
      // هنا سنقبل أي كلمة مرور للتطوير
      if (password.isNotEmpty) {
        await _setCurrentUser(employee);

        LoggingService.info(
          'تم تسجيل دخول المستخدم بنجاح',
          category: 'CurrentUserService',
          data: {'userId': employee.id, 'userName': employee.fullName},
        );

        return true;
      }

      return false;
    } catch (e) {
      LoggingService.error(
        'خطأ في تسجيل دخول المستخدم',
        category: 'CurrentUserService',
        data: {'username': username, 'error': e.toString()},
      );
      return false;
    }
  }

  /// تعيين المستخدم الحالي
  Future<void> _setCurrentUser(Employee employee) async {
    try {
      _currentUser = employee;
      _currentUserId = employee.id;
      _currentUserName = employee.fullName;

      // حفظ معلومات المستخدم في SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt('current_user_id', employee.id!);
      await prefs.setString('current_user_name', employee.fullName);
      await prefs.setString('current_user_email', employee.email ?? '');
    } catch (e) {
      LoggingService.error(
        'خطأ في تعيين المستخدم الحالي',
        category: 'CurrentUserService',
        data: {'employeeId': employee.id, 'error': e.toString()},
      );
    }
  }

  /// تحميل معلومات المستخدم المحفوظة
  Future<void> loadSavedUser() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userId = prefs.getInt('current_user_id');
      final userName = prefs.getString('current_user_name');

      if (userId != null && userName != null) {
        _currentUserId = userId;
        _currentUserName = userName;

        // تحميل بيانات المستخدم الكاملة من قاعدة البيانات
        await _loadUserFromDatabase(userId);
      } else {
        // إذا لم توجد بيانات محفوظة، استخدم المستخدم الافتراضي
        await _setDefaultUser();
      }
    } catch (e) {
      LoggingService.error(
        'خطأ في تحميل المستخدم المحفوظ',
        category: 'CurrentUserService',
        data: {'error': e.toString()},
      );
      // في حالة الخطأ، استخدم المستخدم الافتراضي
      await _setDefaultUser();
    }
  }

  /// تحميل المستخدم من قاعدة البيانات
  Future<void> _loadUserFromDatabase(int userId) async {
    try {
      final db = await _databaseHelper.database;
      final result = await db.query(
        'employees',
        where: 'id = ?',
        whereArgs: [userId],
        limit: 1,
      );

      if (result.isNotEmpty) {
        _currentUser = Employee.fromMap(result.first);
      }
    } catch (e) {
      LoggingService.error(
        'خطأ في تحميل المستخدم من قاعدة البيانات',
        category: 'CurrentUserService',
        data: {'userId': userId, 'error': e.toString()},
      );
    }
  }

  /// تعيين المستخدم الافتراضي
  Future<void> _setDefaultUser() async {
    try {
      // البحث عن أول مستخدم في قاعدة البيانات
      final db = await _databaseHelper.database;
      final result = await db.query('employees', limit: 1, orderBy: 'id ASC');

      if (result.isNotEmpty) {
        final employee = Employee.fromMap(result.first);
        await _setCurrentUser(employee);
      } else {
        // إنشاء مستخدم افتراضي إذا لم يوجد أي مستخدم
        await _createDefaultUser();
      }
    } catch (e) {
      LoggingService.error(
        'خطأ في تعيين المستخدم الافتراضي',
        category: 'CurrentUserService',
        data: {'error': e.toString()},
      );
      // في حالة الخطأ، استخدم قيم افتراضية
      _currentUserId = 1;
      _currentUserName = 'المدير العام';
    }
  }

  /// إنشاء مستخدم افتراضي
  Future<void> _createDefaultUser() async {
    try {
      final db = await _databaseHelper.database;

      final defaultEmployee = Employee(
        employeeNumber: 'EMP001',
        nationalId: '12345678901',
        firstName: 'المدير',
        lastName: 'العام',
        fullName: 'المدير العام',
        email: '<EMAIL>',
        phone: '123456789',
        departmentId: 1,
        positionId: 1,
        hireDate: DateTime.now(),
        basicSalary: 100000,
        status: 'active',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final id = await db.insert('employees', defaultEmployee.toMap());
      final newEmployee = defaultEmployee.copyWith(id: id);

      await _setCurrentUser(newEmployee);

      LoggingService.info(
        'تم إنشاء المستخدم الافتراضي',
        category: 'CurrentUserService',
        data: {'userId': id, 'userName': defaultEmployee.fullName},
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء المستخدم الافتراضي',
        category: 'CurrentUserService',
        data: {'error': e.toString()},
      );
      // في حالة الخطأ، استخدم قيم افتراضية
      _currentUserId = 1;
      _currentUserName = 'المدير العام';
    }
  }

  /// تسجيل خروج المستخدم
  Future<void> logout() async {
    try {
      // مسح معلومات المستخدم من الذاكرة
      _currentUser = null;
      _currentUserId = null;
      _currentUserName = null;

      // مسح معلومات المستخدم من SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('current_user_id');
      await prefs.remove('current_user_name');
      await prefs.remove('current_user_email');

      LoggingService.info(
        'تم تسجيل خروج المستخدم',
        category: 'CurrentUserService',
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في تسجيل خروج المستخدم',
        category: 'CurrentUserService',
        data: {'error': e.toString()},
      );
    }
  }

  /// التحقق من صلاحية المستخدم
  bool hasPermission(String permission) {
    // في التطبيق الحقيقي، يجب التحقق من صلاحيات المستخدم
    // هنا سنعطي جميع الصلاحيات للمستخدم الحالي
    return true;
  }

  /// الحصول على اسم الموظف بالمعرف
  Future<String> getEmployeeNameById(int employeeId) async {
    try {
      if (employeeId == currentUserId) {
        return currentUserName;
      }

      final db = await _databaseHelper.database;
      final result = await db.query(
        'employees',
        columns: ['full_name'],
        where: 'id = ?',
        whereArgs: [employeeId],
        limit: 1,
      );

      if (result.isNotEmpty) {
        return result.first['full_name'] as String;
      }

      return 'موظف $employeeId';
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب اسم الموظف',
        category: 'CurrentUserService',
        data: {'employeeId': employeeId, 'error': e.toString()},
      );
      return 'موظف $employeeId';
    }
  }

  /// تحديث معلومات المستخدم الحالي
  Future<void> refreshCurrentUser() async {
    if (_currentUserId != null) {
      await _loadUserFromDatabase(_currentUserId!);
    }
  }
}
