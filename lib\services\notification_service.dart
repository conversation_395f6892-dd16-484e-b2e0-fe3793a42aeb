/// خدمة إدارة التنبيهات
/// توفر وظائف شاملة لإدارة التنبيهات والتذكيرات التلقائية
library;

import 'dart:convert';
import '../database/database_helper.dart';
import '../models/hr_models.dart';
import '../constants/app_constants.dart';
import '../services/logging_service.dart';
import '../exceptions/validation_exception.dart';

class NotificationService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  /// الحصول على جميع التنبيهات
  Future<List<Notification>> getAllNotifications({
    int? employeeId,
    String? type,
    bool? isRead,
    bool? isImportant,
    DateTime? fromDate,
    DateTime? toDate,
    bool includeSystemWide = true,
  }) async {
    try {
      final db = await _databaseHelper.database;

      String whereClause = '1=1';
      List<dynamic> whereArgs = [];

      if (employeeId != null) {
        if (includeSystemWide) {
          whereClause += ' AND (employee_id = ? OR employee_id IS NULL)';
        } else {
          whereClause += ' AND employee_id = ?';
        }
        whereArgs.add(employeeId);
      }

      if (type != null && type.isNotEmpty) {
        whereClause += ' AND type = ?';
        whereArgs.add(type);
      }

      if (isRead != null) {
        whereClause += ' AND is_read = ?';
        whereArgs.add(isRead ? 1 : 0);
      }

      if (isImportant != null) {
        whereClause += ' AND is_important = ?';
        whereArgs.add(isImportant ? 1 : 0);
      }

      if (fromDate != null) {
        whereClause += ' AND created_at >= ?';
        whereArgs.add(fromDate.toIso8601String());
      }

      if (toDate != null) {
        whereClause += ' AND created_at <= ?';
        whereArgs.add(toDate.toIso8601String());
      }

      final result = await db.query(
        AppConstants.notificationsTable,
        where: whereClause,
        whereArgs: whereArgs.isNotEmpty ? whereArgs : null,
        orderBy: 'is_important DESC, created_at DESC',
      );

      return result.map((map) => Notification.fromMap(map)).toList();
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب التنبيهات',
        category: 'NotificationService',
        data: {'error': e.toString()},
      );
      return [];
    }
  }

  /// الحصول على تنبيه بالمعرف
  Future<Notification?> getNotificationById(int id) async {
    try {
      final db = await _databaseHelper.database;

      final result = await db.query(
        AppConstants.notificationsTable,
        where: 'id = ?',
        whereArgs: [id],
      );

      if (result.isNotEmpty) {
        return Notification.fromMap(result.first);
      }
      return null;
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب التنبيه',
        category: 'NotificationService',
        data: {'error': e.toString(), 'id': id},
      );
      return null;
    }
  }

  /// إنشاء تنبيه جديد
  Future<Notification> createNotification({
    int? employeeId,
    required String type,
    required String title,
    required String message,
    String? actionType,
    int? actionId,
    Map<String, dynamic>? actionData,
    bool isImportant = false,
    DateTime? scheduledAt,
  }) async {
    try {
      // التحقق من صحة البيانات
      await _validateNotification(
        type: type,
        title: title,
        message: message,
        employeeId: employeeId,
      );

      final db = await _databaseHelper.database;
      final now = DateTime.now();

      final notification = Notification(
        employeeId: employeeId,
        type: type,
        title: title,
        message: message,
        actionType: actionType,
        actionId: actionId,
        actionData: actionData != null ? jsonEncode(actionData) : null,
        isImportant: isImportant,
        scheduledAt: scheduledAt,
        createdAt: now,
        updatedAt: now,
      );

      final id = await db.insert(
        AppConstants.notificationsTable,
        notification.toMap(),
      );

      final newNotification = notification.copyWith(id: id);

      LoggingService.info(
        'تم إنشاء تنبيه جديد',
        category: 'NotificationService',
        data: {
          'notificationId': id,
          'employeeId': employeeId,
          'type': type,
          'title': title,
        },
      );

      return newNotification;
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء التنبيه',
        category: 'NotificationService',
        data: {'error': e.toString()},
      );
      rethrow;
    }
  }

  /// تحديد تنبيه كمقروء
  Future<Notification> markAsRead(int notificationId) async {
    try {
      final notification = await getNotificationById(notificationId);
      if (notification == null) {
        throw ValidationException('التنبيه غير موجود');
      }

      if (notification.isRead) {
        return notification; // مقروء مسبقاً
      }

      final db = await _databaseHelper.database;
      final now = DateTime.now();

      final updatedNotification = notification.copyWith(
        isRead: true,
        readAt: now,
        updatedAt: now,
      );

      await db.update(
        AppConstants.notificationsTable,
        updatedNotification.toMap(),
        where: 'id = ?',
        whereArgs: [notificationId],
      );

      LoggingService.info(
        'تم تحديد التنبيه كمقروء',
        category: 'NotificationService',
        data: {'notificationId': notificationId},
      );

      return updatedNotification;
    } catch (e) {
      LoggingService.error(
        'خطأ في تحديد التنبيه كمقروء',
        category: 'NotificationService',
        data: {'error': e.toString(), 'notificationId': notificationId},
      );
      rethrow;
    }
  }

  /// تحديد جميع التنبيهات كمقروءة لموظف
  Future<void> markAllAsRead(int employeeId) async {
    try {
      final db = await _databaseHelper.database;
      final now = DateTime.now();

      await db.update(
        AppConstants.notificationsTable,
        {
          'is_read': 1,
          'read_at': now.toIso8601String(),
          'updated_at': now.toIso8601String(),
        },
        where: 'employee_id = ? AND is_read = 0',
        whereArgs: [employeeId],
      );

      LoggingService.info(
        'تم تحديد جميع التنبيهات كمقروءة',
        category: 'NotificationService',
        data: {'employeeId': employeeId},
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في تحديد جميع التنبيهات كمقروءة',
        category: 'NotificationService',
        data: {'error': e.toString(), 'employeeId': employeeId},
      );
      rethrow;
    }
  }

  /// حذف تنبيه
  Future<void> deleteNotification(int notificationId) async {
    try {
      final notification = await getNotificationById(notificationId);
      if (notification == null) {
        throw ValidationException('التنبيه غير موجود');
      }

      final db = await _databaseHelper.database;

      await db.delete(
        AppConstants.notificationsTable,
        where: 'id = ?',
        whereArgs: [notificationId],
      );

      LoggingService.info(
        'تم حذف التنبيه',
        category: 'NotificationService',
        data: {'notificationId': notificationId, 'title': notification.title},
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في حذف التنبيه',
        category: 'NotificationService',
        data: {'error': e.toString(), 'notificationId': notificationId},
      );
      rethrow;
    }
  }

  /// الحصول على التنبيهات غير المقروءة
  Future<List<Notification>> getUnreadNotifications({
    int? employeeId,
    bool includeSystemWide = true,
  }) async {
    return getAllNotifications(
      employeeId: employeeId,
      isRead: false,
      includeSystemWide: includeSystemWide,
    );
  }

  /// الحصول على عدد التنبيهات غير المقروءة
  Future<int> getUnreadCount({
    int? employeeId,
    bool includeSystemWide = true,
  }) async {
    final unreadNotifications = await getUnreadNotifications(
      employeeId: employeeId,
      includeSystemWide: includeSystemWide,
    );
    return unreadNotifications.length;
  }

  /// الحصول على التنبيهات المهمة
  Future<List<Notification>> getImportantNotifications({
    int? employeeId,
    bool includeSystemWide = true,
  }) async {
    return getAllNotifications(
      employeeId: employeeId,
      isImportant: true,
      includeSystemWide: includeSystemWide,
    );
  }

  /// التحقق من صحة البيانات
  Future<void> _validateNotification({
    required String type,
    required String title,
    required String message,
    int? employeeId,
  }) async {
    if (title.trim().isEmpty) {
      throw ValidationException('عنوان التنبيه مطلوب');
    }

    if (message.trim().isEmpty) {
      throw ValidationException('رسالة التنبيه مطلوبة');
    }

    if (!['reminder', 'alert', 'info', 'warning', 'error'].contains(type)) {
      throw ValidationException('نوع التنبيه غير صحيح');
    }

    // التحقق من وجود الموظف إذا كان محدداً
    if (employeeId != null) {
      final db = await _databaseHelper.database;
      final employeeResult = await db.query(
        AppConstants.employeesTable,
        where: 'id = ?',
        whereArgs: [employeeId],
      );

      if (employeeResult.isEmpty) {
        throw ValidationException('الموظف غير موجود');
      }
    }
  }

  /// إنشاء تنبيه لانتهاء صلاحية الوثائق
  Future<void> createDocumentExpiryNotification({
    required int employeeId,
    required String documentName,
    required DateTime expiryDate,
    required int documentId,
  }) async {
    final daysUntilExpiry = expiryDate.difference(DateTime.now()).inDays;

    String title;
    String message;
    String type;

    if (daysUntilExpiry <= 0) {
      title = 'وثيقة منتهية الصلاحية';
      message = 'انتهت صلاحية الوثيقة: $documentName';
      type = 'error';
    } else if (daysUntilExpiry <= 7) {
      title = 'وثيقة قريبة الانتهاء';
      message =
          'ستنتهي صلاحية الوثيقة: $documentName خلال $daysUntilExpiry أيام';
      type = 'warning';
    } else if (daysUntilExpiry <= 30) {
      title = 'تذكير: انتهاء صلاحية وثيقة';
      message =
          'ستنتهي صلاحية الوثيقة: $documentName خلال $daysUntilExpiry يوم';
      type = 'reminder';
    } else {
      return; // لا حاجة لتنبيه
    }

    await createNotification(
      employeeId: employeeId,
      type: type,
      title: title,
      message: message,
      actionType: 'view_document',
      actionId: documentId,
      isImportant: daysUntilExpiry <= 7,
    );
  }

  /// إنشاء تنبيه لطلب موافقة
  Future<void> createApprovalNotification({
    required int approverId,
    required String requestType,
    required String requestTitle,
    required int approvalId,
  }) async {
    await createNotification(
      employeeId: approverId,
      type: 'alert',
      title: 'طلب موافقة جديد',
      message: 'يوجد طلب $requestType جديد: $requestTitle',
      actionType: 'approve_request',
      actionId: approvalId,
      isImportant: true,
    );
  }

  /// إنشاء تنبيه لنتيجة الموافقة
  Future<void> createApprovalResultNotification({
    required int employeeId,
    required String requestType,
    required String requestTitle,
    required bool isApproved,
    String? notes,
  }) async {
    final title = isApproved ? 'تم قبول طلبك' : 'تم رفض طلبك';
    final message = isApproved
        ? 'تم قبول طلب $requestType: $requestTitle'
        : 'تم رفض طلب $requestType: $requestTitle${notes != null ? '\nالسبب: $notes' : ''}';

    await createNotification(
      employeeId: employeeId,
      type: isApproved ? 'info' : 'warning',
      title: title,
      message: message,
      isImportant: true,
    );
  }

  /// حذف التنبيهات القديمة
  Future<void> cleanupOldNotifications({int daysToKeep = 30}) async {
    try {
      final db = await _databaseHelper.database;
      final cutoffDate = DateTime.now().subtract(Duration(days: daysToKeep));

      final deletedCount = await db.delete(
        AppConstants.notificationsTable,
        where: 'created_at < ? AND is_read = 1',
        whereArgs: [cutoffDate.toIso8601String()],
      );

      LoggingService.info(
        'تم حذف التنبيهات القديمة',
        category: 'NotificationService',
        data: {'deletedCount': deletedCount, 'daysToKeep': daysToKeep},
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في حذف التنبيهات القديمة',
        category: 'NotificationService',
        data: {'error': e.toString()},
      );
    }
  }

  /// إرسال إشعار جديد (للتوافق مع الخدمات الأخرى)
  Future<void> sendNotification({
    required int userId,
    required String title,
    required String message,
    required String type,
    Map<String, dynamic> data = const {},
  }) async {
    try {
      await createNotification(
        employeeId: userId,
        title: title,
        message: message,
        type: type,
        actionData: data,
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في إرسال الإشعار',
        category: 'NotificationService',
        data: {'userId': userId, 'title': title, 'error': e.toString()},
      );
      rethrow;
    }
  }
}
