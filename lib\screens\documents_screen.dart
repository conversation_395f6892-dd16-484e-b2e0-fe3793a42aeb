/// شاشة إدارة الوثائق
/// واجهة شاملة لرفع وإدارة وثائق الموظفين
library;

import 'package:flutter/material.dart';
import '../models/hr_models.dart';
import '../services/document_service.dart';
import '../services/employee_service.dart';
import '../constants/revolutionary_design_colors.dart';
import '../widgets/loading_widget.dart';

class DocumentsScreen extends StatefulWidget {
  final int? employeeId;

  const DocumentsScreen({super.key, this.employeeId});

  @override
  State<DocumentsScreen> createState() => _DocumentsScreenState();
}

class _DocumentsScreenState extends State<DocumentsScreen>
    with TickerProviderStateMixin {
  final DocumentService _documentService = DocumentService();
  final EmployeeService _employeeService = EmployeeService();

  late TabController _tabController;

  List<Document> _allDocuments = [];
  List<Document> _contractDocuments = [];
  List<Document> _certificateDocuments = [];
  List<Document> _personalDocuments = [];
  List<Document> _expiredDocuments = [];

  List<Employee> _employees = [];
  Map<String, dynamic> _statistics = {};
  bool _isLoading = true;
  String? _error;

  String _searchQuery = '';
  String? _selectedType;
  int? _selectedEmployeeId;

  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);
    _selectedEmployeeId = widget.employeeId;
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      // تحميل الموظفين
      final employees = await _employeeService.getAllEmployees();

      // تحميل الوثائق
      final documents = _selectedEmployeeId != null
          ? await _documentService.getEmployeeDocuments(
              employeeId: _selectedEmployeeId!,
            )
          : <Document>[];

      // تحميل الإحصائيات
      final statistics = await _documentService.getDocumentStatistics(
        employeeId: _selectedEmployeeId,
      );

      setState(() {
        _employees = employees;
        _allDocuments = documents;
        _contractDocuments = documents
            .where((d) => d.documentType == 'contract')
            .toList();
        _certificateDocuments = documents
            .where((d) => d.documentType == 'certificate')
            .toList();
        _personalDocuments = documents
            .where((d) => ['id_copy', 'photo', 'cv'].contains(d.documentType))
            .toList();
        _expiredDocuments = documents
            .where((d) => d.isExpired || d.isExpiringSoon)
            .toList();
        _statistics = statistics;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  Future<void> _searchDocuments() async {
    if (_searchQuery.trim().isEmpty) {
      await _loadData();
      return;
    }

    setState(() => _isLoading = true);

    try {
      final results = await _documentService.searchDocuments(
        searchQuery: _searchQuery,
        employeeId: _selectedEmployeeId,
        documentType: _selectedType,
      );

      setState(() {
        _allDocuments = results;
        _contractDocuments = results
            .where((d) => d.documentType == 'contract')
            .toList();
        _certificateDocuments = results
            .where((d) => d.documentType == 'certificate')
            .toList();
        _personalDocuments = results
            .where((d) => ['id_copy', 'photo', 'cv'].contains(d.documentType))
            .toList();
        _expiredDocuments = results
            .where((d) => d.isExpired || d.isExpiringSoon)
            .toList();
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة الوثائق'),
        backgroundColor: RevolutionaryColors.damascusSky,
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          isScrollable: true,
          tabs: [
            Tab(
              text: 'الكل (${_allDocuments.length})',
              icon: const Icon(Icons.folder),
            ),
            Tab(
              text: 'العقود (${_contractDocuments.length})',
              icon: const Icon(Icons.description),
            ),
            Tab(
              text: 'الشهادات (${_certificateDocuments.length})',
              icon: const Icon(Icons.school),
            ),
            Tab(
              text: 'الشخصية (${_personalDocuments.length})',
              icon: const Icon(Icons.person),
            ),
            Tab(
              text: 'منتهية الصلاحية (${_expiredDocuments.length})',
              icon: const Icon(Icons.warning),
            ),
          ],
        ),
      ),
      body: Column(
        children: [
          _buildSearchAndFilters(),
          _buildStatisticsCards(),
          Expanded(
            child: _isLoading
                ? const LoadingWidget()
                : _error != null
                ? _buildErrorWidget()
                : TabBarView(
                    controller: _tabController,
                    children: [
                      _buildDocumentsList(_allDocuments),
                      _buildDocumentsList(_contractDocuments),
                      _buildDocumentsList(_certificateDocuments),
                      _buildDocumentsList(_personalDocuments),
                      _buildDocumentsList(_expiredDocuments),
                    ],
                  ),
          ),
        ],
      ),
      floatingActionButton: _selectedEmployeeId != null
          ? FloatingActionButton(
              onPressed: _showUploadDialog,
              backgroundColor: RevolutionaryColors.successGlow,
              child: const Icon(Icons.upload_file),
            )
          : null,
    );
  }

  Widget _buildErrorWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error, size: 64, color: Colors.red),
          const SizedBox(height: 16),
          Text(
            'حدث خطأ',
            style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          Text(_error ?? 'خطأ غير معروف'),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _loadData,
            child: const Text('إعادة المحاولة'),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchAndFilters() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        children: [
          // اختيار الموظف
          DropdownButtonFormField<int>(
            value: _selectedEmployeeId,
            decoration: const InputDecoration(
              labelText: 'الموظف',
              border: OutlineInputBorder(),
              prefixIcon: Icon(Icons.person),
            ),
            items: [
              const DropdownMenuItem(value: null, child: Text('اختر موظف')),
              ..._employees.map((employee) {
                return DropdownMenuItem(
                  value: employee.id,
                  child: Text('${employee.firstName} ${employee.lastName}'),
                );
              }),
            ],
            onChanged: (value) {
              setState(() => _selectedEmployeeId = value);
              _loadData();
            },
          ),
          const SizedBox(height: 12),

          // شريط البحث
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'البحث في الوثائق...',
              prefixIcon: const Icon(Icons.search),
              suffixIcon: _searchQuery.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: () {
                        _searchController.clear();
                        setState(() => _searchQuery = '');
                        _loadData();
                      },
                    )
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            onChanged: (value) {
              setState(() => _searchQuery = value);
              if (value.isEmpty) {
                _loadData();
              }
            },
            onSubmitted: (_) => _searchDocuments(),
          ),
          const SizedBox(height: 12),

          // فلتر نوع الوثيقة
          DropdownButtonFormField<String>(
            value: _selectedType,
            decoration: const InputDecoration(
              labelText: 'نوع الوثيقة',
              border: OutlineInputBorder(),
            ),
            items: [
              const DropdownMenuItem(value: null, child: Text('الكل')),
              ...DocumentService.getSupportedDocumentTypes().map((type) {
                return DropdownMenuItem(
                  value: type['value'],
                  child: Text(type['label']!),
                );
              }),
            ],
            onChanged: (value) {
              setState(() => _selectedType = value);
              _searchDocuments();
            },
          ),
        ],
      ),
    );
  }

  Widget _buildStatisticsCards() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Expanded(
            child: _buildStatCard(
              'المجموع',
              _statistics['total'] ?? 0,
              RevolutionaryColors.damascusSky,
              Icons.folder,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: _buildStatCard(
              'السرية',
              _statistics['confidential'] ?? 0,
              RevolutionaryColors.warningAmber,
              Icons.lock,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: _buildStatCard(
              'منتهية',
              _statistics['expired'] ?? 0,
              RevolutionaryColors.errorCoral,
              Icons.warning,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(String title, int count, Color color, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            count.toString(),
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(title, style: TextStyle(fontSize: 12, color: color)),
        ],
      ),
    );
  }

  Widget _buildDocumentsList(List<Document> documents) {
    if (documents.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.folder_open, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'لا توجد وثائق',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text(
              'لم يتم العثور على أي وثائق',
              style: TextStyle(color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadData,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: documents.length,
        itemBuilder: (context, index) {
          final document = documents[index];
          return _buildDocumentCard(document);
        },
      ),
    );
  }

  Widget _buildDocumentCard(Document document) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: () => _showDocumentDetails(document),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // العنوان والنوع
              Row(
                children: [
                  _buildFileIcon(document.documentName),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          document.documentName,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          document.documentTypeInArabic,
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ),
                  if (document.isConfidential)
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: RevolutionaryColors.warningAmber.withValues(
                          alpha: 0.1,
                        ),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.lock,
                            size: 14,
                            color: RevolutionaryColors.warningAmber,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            'سري',
                            style: TextStyle(
                              color: RevolutionaryColors.warningAmber,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                ],
              ),

              if (document.description != null) ...[
                const SizedBox(height: 8),
                Text(
                  document.description!,
                  style: TextStyle(color: Colors.grey[600], fontSize: 14),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],

              const SizedBox(height: 12),

              // معلومات إضافية
              Row(
                children: [
                  Icon(Icons.storage, size: 16, color: Colors.grey[600]),
                  const SizedBox(width: 4),
                  Text(
                    document.fileSizeFormatted,
                    style: TextStyle(color: Colors.grey[600], fontSize: 12),
                  ),
                  const Spacer(),
                  Icon(Icons.access_time, size: 16, color: Colors.grey[600]),
                  const SizedBox(width: 4),
                  Text(
                    _formatDate(document.createdAt),
                    style: TextStyle(color: Colors.grey[600], fontSize: 12),
                  ),
                ],
              ),

              // أزرار الإجراءات
              const SizedBox(height: 12),
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () => _downloadDocument(document),
                      icon: const Icon(Icons.download, size: 16),
                      label: const Text('تحميل'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: RevolutionaryColors.damascusSky,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 8),
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () => _editDocument(document),
                      icon: const Icon(Icons.edit, size: 16),
                      label: const Text('تعديل'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: RevolutionaryColors.warningAmber,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 8),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFileIcon(String fileName) {
    final iconType = DocumentService.getFileIcon(fileName);
    IconData icon;
    Color color;

    switch (iconType) {
      case 'pdf':
        icon = Icons.picture_as_pdf;
        color = RevolutionaryColors.errorCoral;
        break;
      case 'word':
        icon = Icons.description;
        color = RevolutionaryColors.damascusSky;
        break;
      case 'excel':
        icon = Icons.table_chart;
        color = RevolutionaryColors.successGlow;
        break;
      case 'image':
        icon = Icons.image;
        color = RevolutionaryColors.warningAmber;
        break;
      default:
        icon = Icons.insert_drive_file;
        color = Colors.grey;
    }

    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Icon(icon, color: color, size: 24),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  // دوال الإجراءات

  void _showDocumentDetails(Document document) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(document.documentName),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildDetailRow('النوع', document.documentTypeInArabic),
              _buildDetailRow('الحجم', document.fileSizeFormatted),
              _buildDetailRow('تاريخ الرفع', _formatDate(document.createdAt)),

              if (document.description != null)
                _buildDetailRow('الوصف', document.description!),

              if (document.expiryDate != null)
                _buildDetailRow(
                  'تاريخ الانتهاء',
                  _formatDate(document.expiryDate!),
                ),

              _buildDetailRow('سري', document.isConfidential ? 'نعم' : 'لا'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _downloadDocument(document);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: RevolutionaryColors.damascusSky,
            ),
            child: const Text('تحميل'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  void _showUploadDialog() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('رفع وثيقة جديدة - قيد التطوير'),
        backgroundColor: RevolutionaryColors.warningAmber,
      ),
    );
  }

  Future<void> _downloadDocument(Document document) async {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تحميل الوثيقة - قيد التطوير'),
        backgroundColor: RevolutionaryColors.damascusSky,
      ),
    );
  }

  Future<void> _editDocument(Document document) async {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تعديل الوثيقة - قيد التطوير'),
        backgroundColor: RevolutionaryColors.warningAmber,
      ),
    );
  }
}
