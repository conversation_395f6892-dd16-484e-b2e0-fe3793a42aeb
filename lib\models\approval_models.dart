/// نماذج نظام الموافقات الإلكترونية
/// يوفر جميع النماذج المطلوبة لإدارة الموافقات وسير العمل
library;

/// نموذج طلب الموافقة
class ApprovalRequest {
  final int? id;
  final String requestType; // leave, expense, purchase, salary_change, etc.
  final String title;
  final String description;
  final int requesterId; // معرف مقدم الطلب
  final String requesterName;
  final int? entityId; // معرف الكيان المرتبط (إجازة، مصروف، إلخ)
  final Map<String, dynamic> requestData; // بيانات الطلب
  final double? amount; // المبلغ إن وجد
  final String priority; // high, medium, low
  final String status; // pending, approved, rejected, cancelled
  final String currentStage; // المرحلة الحالية
  final int? currentApproverId; // المعتمد الحالي
  final DateTime requestDate;
  final DateTime? dueDate;
  final DateTime? completedDate;
  final String? rejectionReason;
  final List<String> attachments;
  final Map<String, dynamic> metadata;
  final DateTime createdAt;
  final DateTime updatedAt;

  const ApprovalRequest({
    this.id,
    required this.requestType,
    required this.title,
    required this.description,
    required this.requesterId,
    required this.requesterName,
    this.entityId,
    required this.requestData,
    this.amount,
    required this.priority,
    required this.status,
    required this.currentStage,
    this.currentApproverId,
    required this.requestDate,
    this.dueDate,
    this.completedDate,
    this.rejectionReason,
    required this.attachments,
    required this.metadata,
    required this.createdAt,
    required this.updatedAt,
  });

  factory ApprovalRequest.fromMap(Map<String, dynamic> map) {
    return ApprovalRequest(
      id: map['id'] as int?,
      requestType: map['request_type'] as String,
      title: map['title'] as String,
      description: map['description'] as String,
      requesterId: map['requester_id'] as int,
      requesterName: map['requester_name'] as String,
      entityId: map['entity_id'] as int?,
      requestData: Map<String, dynamic>.from(map['request_data'] ?? {}),
      amount: map['amount'] as double?,
      priority: map['priority'] as String,
      status: map['status'] as String,
      currentStage: map['current_stage'] as String,
      currentApproverId: map['current_approver_id'] as int?,
      requestDate: DateTime.parse(map['request_date'] as String),
      dueDate: map['due_date'] != null ? DateTime.parse(map['due_date'] as String) : null,
      completedDate: map['completed_date'] != null ? DateTime.parse(map['completed_date'] as String) : null,
      rejectionReason: map['rejection_reason'] as String?,
      attachments: List<String>.from(map['attachments'] ?? []),
      metadata: Map<String, dynamic>.from(map['metadata'] ?? {}),
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'request_type': requestType,
      'title': title,
      'description': description,
      'requester_id': requesterId,
      'requester_name': requesterName,
      'entity_id': entityId,
      'request_data': requestData,
      'amount': amount,
      'priority': priority,
      'status': status,
      'current_stage': currentStage,
      'current_approver_id': currentApproverId,
      'request_date': requestDate.toIso8601String(),
      'due_date': dueDate?.toIso8601String(),
      'completed_date': completedDate?.toIso8601String(),
      'rejection_reason': rejectionReason,
      'attachments': attachments,
      'metadata': metadata,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  ApprovalRequest copyWith({
    int? id,
    String? requestType,
    String? title,
    String? description,
    int? requesterId,
    String? requesterName,
    int? entityId,
    Map<String, dynamic>? requestData,
    double? amount,
    String? priority,
    String? status,
    String? currentStage,
    int? currentApproverId,
    DateTime? requestDate,
    DateTime? dueDate,
    DateTime? completedDate,
    String? rejectionReason,
    List<String>? attachments,
    Map<String, dynamic>? metadata,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return ApprovalRequest(
      id: id ?? this.id,
      requestType: requestType ?? this.requestType,
      title: title ?? this.title,
      description: description ?? this.description,
      requesterId: requesterId ?? this.requesterId,
      requesterName: requesterName ?? this.requesterName,
      entityId: entityId ?? this.entityId,
      requestData: requestData ?? this.requestData,
      amount: amount ?? this.amount,
      priority: priority ?? this.priority,
      status: status ?? this.status,
      currentStage: currentStage ?? this.currentStage,
      currentApproverId: currentApproverId ?? this.currentApproverId,
      requestDate: requestDate ?? this.requestDate,
      dueDate: dueDate ?? this.dueDate,
      completedDate: completedDate ?? this.completedDate,
      rejectionReason: rejectionReason ?? this.rejectionReason,
      attachments: attachments ?? this.attachments,
      metadata: metadata ?? this.metadata,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

/// نموذج مرحلة الموافقة
class ApprovalStage {
  final int? id;
  final int workflowId;
  final String stageName;
  final String stageType; // individual, group, automatic
  final int order;
  final bool isRequired;
  final int? approverId;
  final String? approverRole;
  final int? groupId;
  final String? conditions; // شروط الموافقة
  final int timeoutHours; // مهلة الموافقة بالساعات
  final String? escalationAction; // إجراء التصعيد
  final int? escalationApproverId;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  const ApprovalStage({
    this.id,
    required this.workflowId,
    required this.stageName,
    required this.stageType,
    required this.order,
    required this.isRequired,
    this.approverId,
    this.approverRole,
    this.groupId,
    this.conditions,
    required this.timeoutHours,
    this.escalationAction,
    this.escalationApproverId,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
  });

  factory ApprovalStage.fromMap(Map<String, dynamic> map) {
    return ApprovalStage(
      id: map['id'] as int?,
      workflowId: map['workflow_id'] as int,
      stageName: map['stage_name'] as String,
      stageType: map['stage_type'] as String,
      order: map['order'] as int,
      isRequired: (map['is_required'] as int?) == 1,
      approverId: map['approver_id'] as int?,
      approverRole: map['approver_role'] as String?,
      groupId: map['group_id'] as int?,
      conditions: map['conditions'] as String?,
      timeoutHours: map['timeout_hours'] as int,
      escalationAction: map['escalation_action'] as String?,
      escalationApproverId: map['escalation_approver_id'] as int?,
      isActive: (map['is_active'] as int?) == 1,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'workflow_id': workflowId,
      'stage_name': stageName,
      'stage_type': stageType,
      'order': order,
      'is_required': isRequired ? 1 : 0,
      'approver_id': approverId,
      'approver_role': approverRole,
      'group_id': groupId,
      'conditions': conditions,
      'timeout_hours': timeoutHours,
      'escalation_action': escalationAction,
      'escalation_approver_id': escalationApproverId,
      'is_active': isActive ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }
}

/// نموذج سجل الموافقة
class ApprovalRecord {
  final int? id;
  final int requestId;
  final int stageId;
  final String stageName;
  final int approverId;
  final String approverName;
  final String action; // approved, rejected, delegated, escalated
  final String? comments;
  final DateTime actionDate;
  final String? delegatedTo;
  final Map<String, dynamic> actionData;
  final DateTime createdAt;

  const ApprovalRecord({
    this.id,
    required this.requestId,
    required this.stageId,
    required this.stageName,
    required this.approverId,
    required this.approverName,
    required this.action,
    this.comments,
    required this.actionDate,
    this.delegatedTo,
    required this.actionData,
    required this.createdAt,
  });

  factory ApprovalRecord.fromMap(Map<String, dynamic> map) {
    return ApprovalRecord(
      id: map['id'] as int?,
      requestId: map['request_id'] as int,
      stageId: map['stage_id'] as int,
      stageName: map['stage_name'] as String,
      approverId: map['approver_id'] as int,
      approverName: map['approver_name'] as String,
      action: map['action'] as String,
      comments: map['comments'] as String?,
      actionDate: DateTime.parse(map['action_date'] as String),
      delegatedTo: map['delegated_to'] as String?,
      actionData: Map<String, dynamic>.from(map['action_data'] ?? {}),
      createdAt: DateTime.parse(map['created_at'] as String),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'request_id': requestId,
      'stage_id': stageId,
      'stage_name': stageName,
      'approver_id': approverId,
      'approver_name': approverName,
      'action': action,
      'comments': comments,
      'action_date': actionDate.toIso8601String(),
      'delegated_to': delegatedTo,
      'action_data': actionData,
      'created_at': createdAt.toIso8601String(),
    };
  }
}

/// أنواع الطلبات
enum ApprovalRequestType {
  leave('leave', 'طلب إجازة'),
  expense('expense', 'طلب مصروف'),
  purchase('purchase', 'طلب شراء'),
  salaryChange('salary_change', 'تغيير راتب'),
  promotion('promotion', 'ترقية'),
  transfer('transfer', 'نقل'),
  overtime('overtime', 'عمل إضافي'),
  training('training', 'طلب تدريب'),
  document('document', 'طلب وثيقة'),
  other('other', 'أخرى');

  const ApprovalRequestType(this.value, this.displayName);
  final String value;
  final String displayName;
}

/// حالات الطلب
enum ApprovalStatus {
  pending('pending', 'قيد الانتظار'),
  approved('approved', 'معتمد'),
  rejected('rejected', 'مرفوض'),
  cancelled('cancelled', 'ملغي'),
  expired('expired', 'منتهي الصلاحية');

  const ApprovalStatus(this.value, this.displayName);
  final String value;
  final String displayName;
}

/// أولويات الطلب
enum ApprovalPriority {
  low('low', 'منخفضة'),
  medium('medium', 'متوسطة'),
  high('high', 'عالية'),
  urgent('urgent', 'عاجلة');

  const ApprovalPriority(this.value, this.displayName);
  final String value;
  final String displayName;
}
