/// شاشة نظام الموافقات
/// واجهة شاملة لإدارة جميع طلبات الموافقة في النظام
library;

import 'package:flutter/material.dart';
import '../models/hr_models.dart';
import '../services/approval_service.dart';
import '../constants/revolutionary_design_colors.dart';
import '../widgets/loading_widget.dart';
import '../widgets/custom_error_widget.dart' as custom_widgets;

class ApprovalsScreen extends StatefulWidget {
  const ApprovalsScreen({super.key});

  @override
  State<ApprovalsScreen> createState() => _ApprovalsScreenState();
}

class _ApprovalsScreenState extends State<ApprovalsScreen>
    with TickerProviderStateMixin {
  final ApprovalService _approvalService = ApprovalService();

  late TabController _tabController;

  List<Approval> _allApprovals = [];
  List<Approval> _pendingApprovals = [];
  List<Approval> _approvedApprovals = [];
  List<Approval> _rejectedApprovals = [];

  Map<String, dynamic> _statistics = {};
  bool _isLoading = true;
  String? _error;

  String _searchQuery = '';
  String? _selectedType;
  String? _selectedPriority;

  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      // تحميل جميع الموافقات
      final allApprovals = await _approvalService.getAllApprovals();

      // تحميل الإحصائيات
      final statistics = await _approvalService.getApprovalStatistics();

      setState(() {
        _allApprovals = allApprovals;
        _pendingApprovals = allApprovals.where((a) => a.isPending).toList();
        _approvedApprovals = allApprovals.where((a) => a.isApproved).toList();
        _rejectedApprovals = allApprovals.where((a) => a.isRejected).toList();
        _statistics = statistics;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  Future<void> _searchApprovals() async {
    if (_searchQuery.trim().isEmpty) {
      await _loadData();
      return;
    }

    setState(() => _isLoading = true);

    try {
      final results = await _approvalService.searchApprovals(
        searchQuery: _searchQuery,
        requestType: _selectedType,
      );

      setState(() {
        _allApprovals = results;
        _pendingApprovals = results.where((a) => a.isPending).toList();
        _approvedApprovals = results.where((a) => a.isApproved).toList();
        _rejectedApprovals = results.where((a) => a.isRejected).toList();
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('نظام الموافقات'),
        backgroundColor: RevolutionaryColors.damascusSky,
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          tabs: [
            Tab(
              text: 'الكل (${_statistics['total'] ?? 0})',
              icon: const Icon(Icons.list_alt),
            ),
            Tab(
              text: 'معلقة (${_statistics['pending'] ?? 0})',
              icon: const Icon(Icons.pending_actions),
            ),
            Tab(
              text: 'موافق عليها (${_statistics['approved'] ?? 0})',
              icon: const Icon(Icons.check_circle),
            ),
            Tab(
              text: 'مرفوضة (${_statistics['rejected'] ?? 0})',
              icon: const Icon(Icons.cancel),
            ),
          ],
        ),
      ),
      body: Column(
        children: [
          _buildSearchAndFilters(),
          _buildStatisticsCards(),
          Expanded(
            child: _isLoading
                ? const LoadingWidget()
                : _error != null
                ? custom_widgets.ErrorWidget(
                    message: _error!,
                    onRetry: _loadData,
                  )
                : TabBarView(
                    controller: _tabController,
                    children: [
                      _buildApprovalsList(_allApprovals),
                      _buildApprovalsList(_pendingApprovals),
                      _buildApprovalsList(_approvedApprovals),
                      _buildApprovalsList(_rejectedApprovals),
                    ],
                  ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _showCreateApprovalDialog,
        backgroundColor: RevolutionaryColors.successGlow,
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildSearchAndFilters() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        children: [
          // شريط البحث
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'البحث في الطلبات...',
              prefixIcon: const Icon(Icons.search),
              suffixIcon: _searchQuery.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: () {
                        _searchController.clear();
                        setState(() => _searchQuery = '');
                        _loadData();
                      },
                    )
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            onChanged: (value) {
              setState(() => _searchQuery = value);
              if (value.isEmpty) {
                _loadData();
              }
            },
            onSubmitted: (_) => _searchApprovals(),
          ),
          const SizedBox(height: 12),
          // فلاتر
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<String>(
                  value: _selectedType,
                  decoration: const InputDecoration(
                    labelText: 'نوع الطلب',
                    border: OutlineInputBorder(),
                  ),
                  items: [
                    const DropdownMenuItem(value: null, child: Text('الكل')),
                    const DropdownMenuItem(
                      value: 'leave',
                      child: Text('إجازة'),
                    ),
                    const DropdownMenuItem(value: 'loan', child: Text('قرض')),
                    const DropdownMenuItem(
                      value: 'overtime',
                      child: Text('ساعات إضافية'),
                    ),
                    const DropdownMenuItem(
                      value: 'expense',
                      child: Text('مصروف'),
                    ),
                    const DropdownMenuItem(
                      value: 'training',
                      child: Text('تدريب'),
                    ),
                    const DropdownMenuItem(value: 'other', child: Text('أخرى')),
                  ],
                  onChanged: (value) {
                    setState(() => _selectedType = value);
                    _searchApprovals();
                  },
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: DropdownButtonFormField<String>(
                  value: _selectedPriority,
                  decoration: const InputDecoration(
                    labelText: 'الأولوية',
                    border: OutlineInputBorder(),
                  ),
                  items: [
                    const DropdownMenuItem(value: null, child: Text('الكل')),
                    const DropdownMenuItem(
                      value: 'urgent',
                      child: Text('عاجل'),
                    ),
                    const DropdownMenuItem(value: 'high', child: Text('عالي')),
                    const DropdownMenuItem(
                      value: 'normal',
                      child: Text('عادي'),
                    ),
                    const DropdownMenuItem(value: 'low', child: Text('منخفض')),
                  ],
                  onChanged: (value) {
                    setState(() => _selectedPriority = value);
                    _searchApprovals();
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatisticsCards() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Expanded(
            child: _buildStatCard(
              'المعلقة',
              _statistics['pending'] ?? 0,
              RevolutionaryColors.warningAmber,
              Icons.pending_actions,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: _buildStatCard(
              'الموافق عليها',
              _statistics['approved'] ?? 0,
              RevolutionaryColors.successGlow,
              Icons.check_circle,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: _buildStatCard(
              'المرفوضة',
              _statistics['rejected'] ?? 0,
              RevolutionaryColors.errorCoral,
              Icons.cancel,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(String title, int count, Color color, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            count.toString(),
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(title, style: TextStyle(fontSize: 12, color: color)),
        ],
      ),
    );
  }

  Widget _buildApprovalsList(List<Approval> approvals) {
    if (approvals.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.approval, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'لا توجد طلبات',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text(
              'لم يتم العثور على أي طلبات موافقة',
              style: TextStyle(color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadData,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: approvals.length,
        itemBuilder: (context, index) {
          final approval = approvals[index];
          return _buildApprovalCard(approval);
        },
      ),
    );
  }

  Widget _buildApprovalCard(Approval approval) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: () => _showApprovalDetails(approval),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // العنوان والحالة
              Row(
                children: [
                  Expanded(
                    child: Text(
                      approval.title,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  _buildStatusChip(approval.status),
                ],
              ),
              const SizedBox(height: 8),

              // نوع الطلب والأولوية
              Row(
                children: [
                  _buildTypeChip(approval.requestType),
                  const SizedBox(width: 8),
                  _buildPriorityChip(approval.priority),
                ],
              ),

              if (approval.description != null) ...[
                const SizedBox(height: 8),
                Text(
                  approval.description!,
                  style: TextStyle(color: Colors.grey[600], fontSize: 14),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],

              const SizedBox(height: 12),

              // معلومات إضافية
              Row(
                children: [
                  Icon(Icons.person, size: 16, color: Colors.grey[600]),
                  const SizedBox(width: 4),
                  Text(
                    'موظف رقم: ${approval.employeeId}',
                    style: TextStyle(color: Colors.grey[600], fontSize: 12),
                  ),
                  const Spacer(),
                  Icon(Icons.access_time, size: 16, color: Colors.grey[600]),
                  const SizedBox(width: 4),
                  Text(
                    _formatDate(approval.requestedAt),
                    style: TextStyle(color: Colors.grey[600], fontSize: 12),
                  ),
                ],
              ),

              // أزرار الإجراءات للطلبات المعلقة
              if (approval.isPending) ...[
                const SizedBox(height: 12),
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: () => _approveRequest(approval),
                        icon: const Icon(Icons.check, size: 16),
                        label: const Text('موافقة'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: RevolutionaryColors.successGlow,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 8),
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: () => _rejectRequest(approval),
                        icon: const Icon(Icons.close, size: 16),
                        label: const Text('رفض'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: RevolutionaryColors.errorCoral,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 8),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusChip(String status) {
    Color color;
    String text;
    IconData icon;

    switch (status) {
      case 'pending':
        color = RevolutionaryColors.warningAmber;
        text = 'معلق';
        icon = Icons.pending;
        break;
      case 'approved':
        color = RevolutionaryColors.successGlow;
        text = 'موافق';
        icon = Icons.check_circle;
        break;
      case 'rejected':
        color = RevolutionaryColors.errorCoral;
        text = 'مرفوض';
        icon = Icons.cancel;
        break;
      case 'cancelled':
        color = Colors.grey;
        text = 'ملغي';
        icon = Icons.block;
        break;
      default:
        color = Colors.grey;
        text = status;
        icon = Icons.help;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: color),
          const SizedBox(width: 4),
          Text(
            text,
            style: TextStyle(
              color: color,
              fontSize: 12,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTypeChip(String type) {
    String text;
    IconData icon;

    switch (type) {
      case 'leave':
        text = 'إجازة';
        icon = Icons.event_available;
        break;
      case 'loan':
        text = 'قرض';
        icon = Icons.account_balance_wallet;
        break;
      case 'overtime':
        text = 'ساعات إضافية';
        icon = Icons.access_time;
        break;
      case 'expense':
        text = 'مصروف';
        icon = Icons.receipt;
        break;
      case 'training':
        text = 'تدريب';
        icon = Icons.school;
        break;
      default:
        text = 'أخرى';
        icon = Icons.category;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: RevolutionaryColors.damascusSky.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: RevolutionaryColors.damascusSky),
          const SizedBox(width: 4),
          Text(
            text,
            style: const TextStyle(
              color: RevolutionaryColors.damascusSky,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPriorityChip(String priority) {
    Color color;
    String text;

    switch (priority) {
      case 'urgent':
        color = RevolutionaryColors.errorCoral;
        text = 'عاجل';
        break;
      case 'high':
        color = RevolutionaryColors.warningAmber;
        text = 'عالي';
        break;
      case 'normal':
        color = RevolutionaryColors.damascusSky;
        text = 'عادي';
        break;
      case 'low':
        color = Colors.grey;
        text = 'منخفض';
        break;
      default:
        color = Colors.grey;
        text = priority;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(6),
      ),
      child: Text(
        text,
        style: TextStyle(
          color: color,
          fontSize: 10,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  void _showApprovalDetails(Approval approval) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(approval.title),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildDetailRow('النوع', approval.requestTypeInArabic),
              _buildDetailRow('الحالة', approval.statusInArabic),
              _buildDetailRow('الأولوية', approval.priorityInArabic),
              _buildDetailRow('الموظف', 'رقم ${approval.employeeId}'),
              _buildDetailRow('تاريخ الطلب', _formatDate(approval.requestedAt)),

              if (approval.description != null)
                _buildDetailRow('الوصف', approval.description!),

              if (approval.approvedAt != null)
                _buildDetailRow(
                  'تاريخ الموافقة',
                  _formatDate(approval.approvedAt!),
                ),

              if (approval.approvalNotes != null)
                _buildDetailRow('ملاحظات الموافقة', approval.approvalNotes!),

              if (approval.rejectionReason != null)
                _buildDetailRow('سبب الرفض', approval.rejectionReason!),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
          if (approval.isPending) ...[
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                _approveRequest(approval);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: RevolutionaryColors.successGlow,
              ),
              child: const Text('موافقة'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                _rejectRequest(approval);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: RevolutionaryColors.errorCoral,
              ),
              child: const Text('رفض'),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  void _showCreateApprovalDialog() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('إنشاء طلب موافقة جديد - قيد التطوير'),
        backgroundColor: RevolutionaryColors.warningAmber,
      ),
    );
  }

  Future<void> _approveRequest(Approval approval) async {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('الموافقة على الطلب - قيد التطوير'),
        backgroundColor: RevolutionaryColors.successGlow,
      ),
    );
  }

  Future<void> _rejectRequest(Approval approval) async {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('رفض الطلب - قيد التطوير'),
        backgroundColor: RevolutionaryColors.errorCoral,
      ),
    );
  }
}
