/// خدمة إدارة المناصب
/// توفر عمليات إدارة المناصب الوظيفية في نظام الموارد البشرية
library;

import '../database/database_helper.dart';
import '../constants/app_constants.dart';
import '../services/logging_service.dart';
import '../services/audit_service.dart';
import '../exceptions/validation_exception.dart';
import '../models/hr_models.dart';

class PositionService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  /// إنشاء منصب جديد
  Future<Position> createPosition({
    required String title,
    required String code,
    required int departmentId,
    String? description,
    double? minSalary,
    double? maxSalary,
    String? requirements,
    bool isActive = true,
  }) async {
    try {
      // التحقق من صحة البيانات
      if (title.trim().isEmpty) {
        throw ValidationException('عنوان المنصب مطلوب');
      }
      if (code.trim().isEmpty) {
        throw ValidationException('رمز المنصب مطلوب');
      }

      // التحقق من وجود القسم
      final db = await _databaseHelper.database;
      final departmentExists = await db.query(
        AppConstants.departmentsTable,
        where: 'id = ? AND is_active = ?',
        whereArgs: [departmentId, 1],
        limit: 1,
      );

      if (departmentExists.isEmpty) {
        throw ValidationException('القسم المحدد غير موجود أو غير نشط');
      }

      // التحقق من عدم تكرار الرمز
      final existingPosition = await getPositionByCode(code);
      if (existingPosition != null) {
        throw ValidationException('رمز المنصب موجود مسبقاً');
      }

      // التحقق من صحة نطاق الراتب
      if (minSalary != null && maxSalary != null && minSalary > maxSalary) {
        throw ValidationException(
          'الحد الأدنى للراتب لا يمكن أن يكون أكبر من الحد الأقصى',
        );
      }

      final position = Position(
        title: title.trim(),
        code: code.trim().toUpperCase(),
        departmentId: departmentId,
        description: description?.trim(),
        minSalary: minSalary ?? 0,
        maxSalary: maxSalary ?? 0,
        requirements: requirements?.trim(),
        isActive: isActive,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // حفظ المنصب في قاعدة البيانات
      final id = await db.insert(AppConstants.positionsTable, position.toMap());

      final savedPosition = position.copyWith(id: id);

      // تسجيل في سجل المراجعة
      await AuditService.log(
        action: 'CREATE',
        entityType: 'Position',
        entityId: id,
        description: 'إنشاء منصب جديد',
        newValues: savedPosition.toMap(),
      );

      LoggingService.info(
        'تم إنشاء منصب جديد بنجاح',
        category: 'PositionService',
        data: {
          'positionId': id,
          'title': title,
          'code': code,
          'departmentId': departmentId,
        },
      );

      return savedPosition;
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء المنصب',
        category: 'PositionService',
        data: {
          'title': title,
          'code': code,
          'departmentId': departmentId,
          'error': e.toString(),
        },
      );
      rethrow;
    }
  }

  /// الحصول على جميع المناصب
  Future<List<Position>> getAllPositions({
    bool activeOnly = false,
    int? departmentId,
  }) async {
    try {
      final db = await _databaseHelper.database;

      List<String> whereConditions = [];
      List<dynamic> whereArgs = [];

      if (activeOnly) {
        whereConditions.add('is_active = ?');
        whereArgs.add(1);
      }

      if (departmentId != null) {
        whereConditions.add('department_id = ?');
        whereArgs.add(departmentId);
      }

      String? whereClause;
      if (whereConditions.isNotEmpty) {
        whereClause = whereConditions.join(' AND ');
      }

      final result = await db.query(
        AppConstants.positionsTable,
        where: whereClause,
        whereArgs: whereArgs.isNotEmpty ? whereArgs : null,
        orderBy: 'title ASC',
      );

      return result.map((map) => Position.fromMap(map)).toList();
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب المناصب',
        category: 'PositionService',
        data: {'error': e.toString()},
      );
      return [];
    }
  }

  /// الحصول على منصب بالمعرف
  Future<Position?> getPositionById(int id) async {
    try {
      final db = await _databaseHelper.database;
      final result = await db.query(
        AppConstants.positionsTable,
        where: 'id = ?',
        whereArgs: [id],
        limit: 1,
      );

      if (result.isNotEmpty) {
        return Position.fromMap(result.first);
      }
      return null;
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب المنصب',
        category: 'PositionService',
        data: {'positionId': id, 'error': e.toString()},
      );
      return null;
    }
  }

  /// الحصول على منصب بالرمز
  Future<Position?> getPositionByCode(String code) async {
    try {
      final db = await _databaseHelper.database;
      final result = await db.query(
        AppConstants.positionsTable,
        where: 'code = ?',
        whereArgs: [code.toUpperCase()],
        limit: 1,
      );

      if (result.isNotEmpty) {
        return Position.fromMap(result.first);
      }
      return null;
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب المنصب بالرمز',
        category: 'PositionService',
        data: {'code': code, 'error': e.toString()},
      );
      return null;
    }
  }

  /// الحصول على مناصب قسم معين
  Future<List<Position>> getDepartmentPositions(int departmentId) async {
    return getAllPositions(departmentId: departmentId, activeOnly: true);
  }

  /// تحديث منصب
  Future<Position> updatePosition(Position position) async {
    try {
      if (position.id == null) {
        throw ValidationException('معرف المنصب مطلوب للتحديث');
      }

      // التحقق من عدم تكرار الرمز مع مناصب أخرى
      final existingPosition = await getPositionByCode(position.code);
      if (existingPosition != null && existingPosition.id != position.id) {
        throw ValidationException('رمز المنصب موجود مسبقاً');
      }

      // التحقق من وجود القسم
      final db = await _databaseHelper.database;
      final departmentExists = await db.query(
        AppConstants.departmentsTable,
        where: 'id = ? AND is_active = ?',
        whereArgs: [position.departmentId, 1],
        limit: 1,
      );

      if (departmentExists.isEmpty) {
        throw ValidationException('القسم المحدد غير موجود أو غير نشط');
      }

      // التحقق من صحة نطاق الراتب
      if (position.minSalary > position.maxSalary) {
        throw ValidationException(
          'الحد الأدنى للراتب لا يمكن أن يكون أكبر من الحد الأقصى',
        );
      }

      final updatedPosition = position.copyWith(updatedAt: DateTime.now());

      await db.update(
        AppConstants.positionsTable,
        updatedPosition.toMap(),
        where: 'id = ?',
        whereArgs: [position.id],
      );

      // تسجيل في سجل المراجعة
      await AuditService.log(
        action: 'UPDATE',
        entityType: 'Position',
        entityId: position.id!,
        description: 'تحديث بيانات المنصب',
        newValues: updatedPosition.toMap(),
      );

      LoggingService.info(
        'تم تحديث المنصب بنجاح',
        category: 'PositionService',
        data: {'positionId': position.id},
      );

      return updatedPosition;
    } catch (e) {
      LoggingService.error(
        'خطأ في تحديث المنصب',
        category: 'PositionService',
        data: {'positionId': position.id, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// حذف منصب
  Future<void> deletePosition(int id) async {
    try {
      final db = await _databaseHelper.database;

      // التحقق من وجود موظفين في المنصب
      final employeesCount = await db.rawQuery(
        'SELECT COUNT(*) as count FROM ${AppConstants.employeesTable} WHERE position_id = ?',
        [id],
      );

      final count = employeesCount.first['count'] as int;
      if (count > 0) {
        throw ValidationException(
          'لا يمكن حذف المنصب لوجود $count موظف/موظفين مرتبطين به',
        );
      }

      // حذف المنصب
      await db.delete(
        AppConstants.positionsTable,
        where: 'id = ?',
        whereArgs: [id],
      );

      // تسجيل في سجل المراجعة
      await AuditService.log(
        action: 'DELETE',
        entityType: 'Position',
        entityId: id,
        description: 'حذف المنصب',
      );

      LoggingService.info(
        'تم حذف المنصب بنجاح',
        category: 'PositionService',
        data: {'positionId': id},
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في حذف المنصب',
        category: 'PositionService',
        data: {'positionId': id, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// تفعيل/إلغاء تفعيل منصب
  Future<void> togglePositionStatus(int id) async {
    try {
      final position = await getPositionById(id);
      if (position == null) {
        throw ValidationException('المنصب غير موجود');
      }

      final updatedPosition = position.copyWith(
        isActive: !position.isActive,
        updatedAt: DateTime.now(),
      );

      await updatePosition(updatedPosition);

      LoggingService.info(
        'تم تغيير حالة المنصب',
        category: 'PositionService',
        data: {
          'positionId': id,
          'newStatus': updatedPosition.isActive ? 'active' : 'inactive',
        },
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في تغيير حالة المنصب',
        category: 'PositionService',
        data: {'error': e.toString(), 'id': id},
      );
      rethrow;
    }
  }

  /// إنشاء مناصب افتراضية
  Future<void> createDefaultPositions() async {
    try {
      final existingPositions = await getAllPositions();
      if (existingPositions.isNotEmpty) {
        LoggingService.info(
          'المناصب موجودة مسبقاً',
          category: 'PositionService',
        );
        return;
      }

      // الحصول على الأقسام الافتراضية
      final db = await _databaseHelper.database;
      final departmentsResult = await db.query(AppConstants.departmentsTable);

      if (departmentsResult.isEmpty) {
        throw ValidationException('يجب إنشاء الأقسام أولاً');
      }

      final departments = departmentsResult
          .map((map) => Department.fromMap(map))
          .toList();

      final defaultPositions = <Position>[];

      // مناصب الإدارة العامة
      final managementDept = departments.firstWhere(
        (d) => d.name.contains('الإدارة العامة'),
        orElse: () => departments.first,
      );

      defaultPositions.addAll([
        Position(
          title: 'المدير العام',
          code: 'CEO',
          departmentId: managementDept.id!,
          description: 'المسؤول الأول عن إدارة الشركة',
          minSalary: 2000000,
          maxSalary: 5000000,
          requirements: 'خبرة إدارية لا تقل عن 10 سنوات',
          isActive: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        Position(
          title: 'مدير تنفيذي',
          code: 'EXEC_MGR',
          departmentId: managementDept.id!,
          description: 'مساعد المدير العام في الإدارة التنفيذية',
          minSalary: 1500000,
          maxSalary: 3000000,
          requirements: 'خبرة إدارية لا تقل عن 7 سنوات',
          isActive: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
      ]);

      // مناصب المحاسبة
      final accountingDept = departments.firstWhere(
        (d) => d.name.contains('المحاسبة'),
        orElse: () => departments.first,
      );

      defaultPositions.addAll([
        Position(
          title: 'المدير المالي',
          code: 'CFO',
          departmentId: accountingDept.id!,
          description: 'مسؤول عن الإدارة المالية والمحاسبية',
          minSalary: 1200000,
          maxSalary: 2500000,
          requirements: 'شهادة في المحاسبة وخبرة لا تقل عن 8 سنوات',
          isActive: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        Position(
          title: 'محاسب أول',
          code: 'SR_ACC',
          departmentId: accountingDept.id!,
          description: 'محاسب خبير في جميع العمليات المحاسبية',
          minSalary: 800000,
          maxSalary: 1500000,
          requirements: 'شهادة في المحاسبة وخبرة لا تقل عن 5 سنوات',
          isActive: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        Position(
          title: 'محاسب',
          code: 'ACC',
          departmentId: accountingDept.id!,
          description: 'محاسب للعمليات المحاسبية العامة',
          minSalary: 500000,
          maxSalary: 1000000,
          requirements: 'شهادة في المحاسبة وخبرة لا تقل عن سنتين',
          isActive: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
      ]);

      // إنشاء المناصب
      for (final position in defaultPositions) {
        await createPosition(
          title: position.title,
          code: position.code,
          departmentId: position.departmentId!,
          description: position.description,
          minSalary: position.minSalary,
          maxSalary: position.maxSalary,
          requirements: position.requirements,
          isActive: position.isActive,
        );
      }

      LoggingService.info(
        'تم إنشاء ${defaultPositions.length} منصب افتراضي',
        category: 'PositionService',
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء المناصب الافتراضية',
        category: 'PositionService',
        data: {'error': e.toString()},
      );
      rethrow;
    }
  }
}
