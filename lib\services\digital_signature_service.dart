/// خدمة نظام التوقيع الإلكتروني
/// توفر جميع العمليات المطلوبة لإدارة التوقيعات الإلكترونية والشهادات الرقمية
library;

import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'package:crypto/crypto.dart';
import 'package:pointycastle/export.dart';
import '../database/database_helper.dart';
import '../services/logging_service.dart';
import '../services/audit_service.dart';
import '../services/notification_service.dart';
import '../exceptions/validation_exception.dart';
import '../models/digital_signature_models.dart';
import '../constants/app_constants.dart';

class DigitalSignatureService {
  static final DigitalSignatureService _instance =
      DigitalSignatureService._internal();
  factory DigitalSignatureService() => _instance;
  DigitalSignatureService._internal();

  final DatabaseHelper _databaseHelper = DatabaseHelper();
  final NotificationService _notificationService = NotificationService();

  /// إنشاء وثيقة قابلة للتوقيع
  Future<SignableDocument> createSignableDocument({
    required String title,
    required String description,
    required String documentType,
    required String filePath,
    required int createdBy,
    required String creatorName,
    required DateTime expiryDate,
    bool requiresMultipleSignatures = false,
    int requiredSignatures = 1,
    Map<String, dynamic> metadata = const {},
  }) async {
    try {
      // التحقق من صحة البيانات
      await _validateDocumentData(
        title: title,
        description: description,
        filePath: filePath,
        expiryDate: expiryDate,
      );

      // حساب hash الملف للتحقق من سلامته
      final fileHash = await _calculateFileHash(filePath);

      final db = await _databaseHelper.database;
      final now = DateTime.now();

      final document = SignableDocument(
        title: title,
        description: description,
        documentType: documentType,
        filePath: filePath,
        fileHash: fileHash,
        createdBy: createdBy,
        creatorName: creatorName,
        status: DocumentStatus.draft.value,
        expiryDate: expiryDate,
        requiresMultipleSignatures: requiresMultipleSignatures,
        requiredSignatures: requiredSignatures,
        currentSignatures: 0,
        metadata: metadata,
        createdAt: now,
        updatedAt: now,
      );

      final id = await db.insert('signable_documents', document.toMap());

      final newDocument = document.copyWith(id: id);

      // تسجيل في سجل المراجعة
      await AuditService.log(
        action: 'CREATE',
        entityType: 'SignableDocument',
        entityId: id,
        description: 'إنشاء وثيقة قابلة للتوقيع: $title',
        newValues: newDocument.toMap(),
      );

      LoggingService.info(
        'تم إنشاء وثيقة قابلة للتوقيع بنجاح',
        category: 'DigitalSignatureService',
        data: {'id': id, 'title': title, 'type': documentType},
      );

      return newDocument;
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء الوثيقة القابلة للتوقيع',
        category: 'DigitalSignatureService',
        data: {'title': title, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// إنشاء شهادة رقمية للمستخدم
  Future<DigitalCertificate> createDigitalCertificate({
    required int userId,
    required String userName,
    required String certificateType,
    required String password,
    int keySize = 2048,
    int validityYears = 2,
    Map<String, dynamic> extensions = const {},
  }) async {
    try {
      // التحقق من صحة البيانات
      await _validateCertificateData(
        userId: userId,
        userName: userName,
        password: password,
        keySize: keySize,
      );

      // توليد زوج المفاتيح
      final keyPair = await _generateKeyPair(keySize);

      // تشفير المفتاح الخاص
      final privateKeyHash = _hashPrivateKey(keyPair.privateKey, password);

      final db = await _databaseHelper.database;
      final now = DateTime.now();
      final validTo = now.add(Duration(days: validityYears * 365));

      // إنشاء معرف فريد للشهادة
      final serialNumber = _generateSerialNumber();
      final fingerprint = _generateFingerprint(keyPair.publicKey);

      final certificate = DigitalCertificate(
        userId: userId,
        userName: userName,
        certificateType: certificateType,
        publicKey: _encodePublicKey(keyPair.publicKey),
        privateKeyHash: privateKeyHash,
        issuer: 'Smart Ledger CA',
        subject: 'CN=$userName, O=Smart Ledger, C=SY',
        validFrom: now,
        validTo: validTo,
        serialNumber: serialNumber,
        fingerprint: fingerprint,
        algorithm: 'RSA',
        keySize: keySize,
        isActive: true,
        isRevoked: false,
        extensions: extensions,
        createdAt: now,
        updatedAt: now,
      );

      final id = await db.insert('digital_certificates', certificate.toMap());

      final newCertificate = certificate.copyWith(id: id);

      // تسجيل في سجل المراجعة
      await AuditService.log(
        action: 'CREATE',
        entityType: 'DigitalCertificate',
        entityId: id,
        description: 'إنشاء شهادة رقمية للمستخدم: $userName',
        newValues: newCertificate.toMap(),
      );

      LoggingService.info(
        'تم إنشاء شهادة رقمية بنجاح',
        category: 'DigitalSignatureService',
        data: {'id': id, 'userId': userId, 'userName': userName},
      );

      return newCertificate;
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء الشهادة الرقمية',
        category: 'DigitalSignatureService',
        data: {'userId': userId, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// توقيع وثيقة إلكترونياً
  Future<DigitalSignature> signDocument({
    required int documentId,
    required int signerId,
    required String signerName,
    required String signerEmail,
    required String password,
    required String ipAddress,
    required String userAgent,
    String location = 'غير محدد',
    Map<String, dynamic> verificationData = const {},
  }) async {
    try {
      // التحقق من الوثيقة
      final document = await getSignableDocumentById(documentId);
      if (document == null) {
        throw ValidationException('الوثيقة غير موجودة');
      }

      if (document.status == DocumentStatus.signed.value &&
          !document.requiresMultipleSignatures) {
        throw ValidationException('الوثيقة موقعة بالفعل');
      }

      if (document.expiryDate.isBefore(DateTime.now())) {
        throw ValidationException('الوثيقة منتهية الصلاحية');
      }

      // التحقق من الشهادة الرقمية
      final certificate = await getActiveCertificateForUser(signerId);
      if (certificate == null) {
        throw ValidationException('لا توجد شهادة رقمية نشطة للمستخدم');
      }

      // التحقق من كلمة المرور
      if (!await _verifyPassword(certificate, password)) {
        throw ValidationException('كلمة المرور غير صحيحة');
      }

      // التحقق من سلامة الملف
      final currentFileHash = await _calculateFileHash(document.filePath);
      if (currentFileHash != document.fileHash) {
        throw ValidationException('تم تعديل الملف بعد إنشائه');
      }

      // إنشاء التوقيع
      final signatureData = await _createSignature(
        document: document,
        certificate: certificate,
        password: password,
      );

      final db = await _databaseHelper.database;
      final now = DateTime.now();

      final signature = DigitalSignature(
        documentId: documentId,
        signerId: signerId,
        signerName: signerName,
        signerEmail: signerEmail,
        signatureData: signatureData['signature'],
        signatureHash: signatureData['hash'],
        certificateData: _encodeCertificate(certificate),
        signatureMethod: certificate.algorithm,
        ipAddress: ipAddress,
        userAgent: userAgent,
        location: location,
        signedAt: now,
        isValid: true,
        verificationData: verificationData,
        createdAt: now,
      );

      final id = await db.insert('digital_signatures', signature.toMap());

      // تحديث عداد التوقيعات في الوثيقة
      final updatedDocument = await _updateDocumentSignatureCount(document);

      // تسجيل في سجل المراجعة
      await AuditService.log(
        action: 'CREATE',
        entityType: 'DigitalSignature',
        entityId: id,
        description: 'توقيع الوثيقة: ${document.title} من قبل $signerName',
        newValues: signature.toMap(),
      );

      LoggingService.info(
        'تم توقيع الوثيقة بنجاح',
        category: 'DigitalSignatureService',
        data: {
          'documentId': documentId,
          'signerId': signerId,
          'signerName': signerName,
        },
      );

      // إرسال إشعار بالتوقيع
      await _sendSignatureNotification(updatedDocument, signature);

      return signature.copyWith(id: id);
    } catch (e) {
      LoggingService.error(
        'خطأ في توقيع الوثيقة',
        category: 'DigitalSignatureService',
        data: {
          'documentId': documentId,
          'signerId': signerId,
          'error': e.toString(),
        },
      );
      rethrow;
    }
  }

  /// التحقق من صحة التوقيع
  Future<bool> verifySignature({
    required int signatureId,
    bool checkCertificateValidity = true,
    bool checkDocumentIntegrity = true,
  }) async {
    try {
      final signature = await getDigitalSignatureById(signatureId);
      if (signature == null) {
        return false;
      }

      final document = await getSignableDocumentById(signature.documentId);
      if (document == null) {
        return false;
      }

      // التحقق من سلامة الوثيقة
      if (checkDocumentIntegrity) {
        final currentFileHash = await _calculateFileHash(document.filePath);
        if (currentFileHash != document.fileHash) {
          await _invalidateSignature(
            signatureId,
            'تم تعديل الوثيقة بعد التوقيع',
          );
          return false;
        }
      }

      // التحقق من صحة الشهادة
      if (checkCertificateValidity) {
        final certificate = await getDigitalCertificateById(signature.signerId);
        if (certificate == null ||
            certificate.isRevoked ||
            !certificate.isActive) {
          await _invalidateSignature(signatureId, 'الشهادة الرقمية غير صالحة');
          return false;
        }

        if (certificate.validTo.isBefore(DateTime.now())) {
          await _invalidateSignature(
            signatureId,
            'الشهادة الرقمية منتهية الصلاحية',
          );
          return false;
        }
      }

      // التحقق من التوقيع الرقمي
      final isSignatureValid = await _verifyDigitalSignature(
        signature,
        document,
      );
      if (!isSignatureValid) {
        await _invalidateSignature(signatureId, 'التوقيع الرقمي غير صحيح');
        return false;
      }

      LoggingService.info(
        'تم التحقق من صحة التوقيع بنجاح',
        category: 'DigitalSignatureService',
        data: {'signatureId': signatureId},
      );

      return true;
    } catch (e) {
      LoggingService.error(
        'خطأ في التحقق من صحة التوقيع',
        category: 'DigitalSignatureService',
        data: {'signatureId': signatureId, 'error': e.toString()},
      );
      return false;
    }
  }

  /// الحصول على وثيقة قابلة للتوقيع بالمعرف
  Future<SignableDocument?> getSignableDocumentById(int id) async {
    try {
      final db = await _databaseHelper.database;
      final result = await db.query(
        'signable_documents',
        where: 'id = ?',
        whereArgs: [id],
        limit: 1,
      );

      if (result.isNotEmpty) {
        return SignableDocument.fromMap(result.first);
      }
      return null;
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب الوثيقة القابلة للتوقيع',
        category: 'DigitalSignatureService',
        data: {'id': id, 'error': e.toString()},
      );
      return null;
    }
  }

  /// الحصول على التوقيع الرقمي بالمعرف
  Future<DigitalSignature?> getDigitalSignatureById(int id) async {
    try {
      final db = await _databaseHelper.database;
      final result = await db.query(
        'digital_signatures',
        where: 'id = ?',
        whereArgs: [id],
        limit: 1,
      );

      if (result.isNotEmpty) {
        return DigitalSignature.fromMap(result.first);
      }
      return null;
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب التوقيع الرقمي',
        category: 'DigitalSignatureService',
        data: {'id': id, 'error': e.toString()},
      );
      return null;
    }
  }

  /// الحصول على الشهادة الرقمية النشطة للمستخدم
  Future<DigitalCertificate?> getActiveCertificateForUser(int userId) async {
    try {
      final db = await _databaseHelper.database;
      final result = await db.query(
        'digital_certificates',
        where:
            'user_id = ? AND is_active = 1 AND is_revoked = 0 AND valid_to > ?',
        whereArgs: [userId, DateTime.now().toIso8601String()],
        orderBy: 'created_at DESC',
        limit: 1,
      );

      if (result.isNotEmpty) {
        return DigitalCertificate.fromMap(result.first);
      }
      return null;
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب الشهادة الرقمية النشطة',
        category: 'DigitalSignatureService',
        data: {'userId': userId, 'error': e.toString()},
      );
      return null;
    }
  }

  /// الحصول على الشهادة الرقمية بالمعرف
  Future<DigitalCertificate?> getDigitalCertificateById(int userId) async {
    try {
      final db = await _databaseHelper.database;
      final result = await db.query(
        'digital_certificates',
        where: 'user_id = ?',
        whereArgs: [userId],
        orderBy: 'created_at DESC',
        limit: 1,
      );

      if (result.isNotEmpty) {
        return DigitalCertificate.fromMap(result.first);
      }
      return null;
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب الشهادة الرقمية',
        category: 'DigitalSignatureService',
        data: {'userId': userId, 'error': e.toString()},
      );
      return null;
    }
  }

  // ===== الطرق المساعدة =====

  /// التحقق من صحة بيانات الوثيقة
  Future<void> _validateDocumentData({
    required String title,
    required String description,
    required String filePath,
    required DateTime expiryDate,
  }) async {
    if (title.trim().isEmpty) {
      throw ValidationException('عنوان الوثيقة مطلوب');
    }

    if (title.length < 3) {
      throw ValidationException('عنوان الوثيقة يجب أن يكون 3 أحرف على الأقل');
    }

    if (description.trim().isEmpty) {
      throw ValidationException('وصف الوثيقة مطلوب');
    }

    if (!File(filePath).existsSync()) {
      throw ValidationException('الملف غير موجود');
    }

    if (expiryDate.isBefore(DateTime.now())) {
      throw ValidationException(
        'تاريخ انتهاء الصلاحية يجب أن يكون في المستقبل',
      );
    }
  }

  /// التحقق من صحة بيانات الشهادة
  Future<void> _validateCertificateData({
    required int userId,
    required String userName,
    required String password,
    required int keySize,
  }) async {
    if (userId <= 0) {
      throw ValidationException('معرف المستخدم غير صحيح');
    }

    if (userName.trim().isEmpty) {
      throw ValidationException('اسم المستخدم مطلوب');
    }

    if (password.length < 8) {
      throw ValidationException('كلمة المرور يجب أن تكون 8 أحرف على الأقل');
    }

    if (keySize < 1024) {
      throw ValidationException('حجم المفتاح يجب أن يكون 1024 بت على الأقل');
    }

    // التحقق من عدم وجود شهادة نشطة للمستخدم
    final existingCertificate = await getActiveCertificateForUser(userId);
    if (existingCertificate != null) {
      throw ValidationException('يوجد شهادة رقمية نشطة للمستخدم بالفعل');
    }
  }

  /// حساب hash الملف
  Future<String> _calculateFileHash(String filePath) async {
    try {
      final file = File(filePath);
      final bytes = await file.readAsBytes();
      final digest = sha256.convert(bytes);
      return digest.toString();
    } catch (e) {
      throw ValidationException('خطأ في حساب hash الملف: $e');
    }
  }

  /// توليد زوج المفاتيح
  Future<AsymmetricKeyPair<RSAPublicKey, RSAPrivateKey>> _generateKeyPair(
    int keySize,
  ) async {
    try {
      final keyGen = RSAKeyGenerator();
      final secureRandom = FortunaRandom();

      // تهيئة المولد العشوائي
      final seedSource = List<int>.generate(
        32,
        (i) => DateTime.now().millisecondsSinceEpoch + i,
      );
      secureRandom.seed(KeyParameter(Uint8List.fromList(seedSource)));

      keyGen.init(
        ParametersWithRandom(
          RSAKeyGeneratorParameters(BigInt.parse('65537'), keySize, 64),
          secureRandom,
        ),
      );

      return keyGen.generateKeyPair();
    } catch (e) {
      throw ValidationException('خطأ في توليد زوج المفاتيح: $e');
    }
  }

  /// تشفير hash المفتاح الخاص
  String _hashPrivateKey(RSAPrivateKey privateKey, String password) {
    try {
      final keyData = '${privateKey.n}:${privateKey.d}:$password';
      final bytes = utf8.encode(keyData);
      final digest = sha256.convert(bytes);
      return digest.toString();
    } catch (e) {
      throw ValidationException('خطأ في تشفير المفتاح الخاص: $e');
    }
  }

  /// توليد رقم تسلسلي للشهادة
  String _generateSerialNumber() {
    final now = DateTime.now();
    final timestamp = now.millisecondsSinceEpoch;
    final random = timestamp % 1000000;
    return 'SL${timestamp.toString()}${random.toString().padLeft(6, '0')}';
  }

  /// توليد بصمة الشهادة
  String _generateFingerprint(RSAPublicKey publicKey) {
    try {
      final keyData = '${publicKey.n}:${publicKey.exponent}';
      final bytes = utf8.encode(keyData);
      final digest = sha256.convert(bytes);
      return digest.toString().toUpperCase();
    } catch (e) {
      throw ValidationException('خطأ في توليد بصمة الشهادة: $e');
    }
  }

  /// تشفير المفتاح العام
  String _encodePublicKey(RSAPublicKey publicKey) {
    try {
      final keyData = {
        'n': publicKey.n.toString(),
        'e': publicKey.exponent.toString(),
      };
      return base64Encode(utf8.encode(jsonEncode(keyData)));
    } catch (e) {
      throw ValidationException('خطأ في تشفير المفتاح العام: $e');
    }
  }

  /// التحقق من كلمة المرور
  Future<bool> _verifyPassword(
    DigitalCertificate certificate,
    String password,
  ) async {
    try {
      // هذا مثال مبسط - في التطبيق الحقيقي يجب استخدام طرق تشفير أكثر أماناً
      final testHash = _hashPrivateKey(
        _decodePrivateKey(certificate.publicKey), // مثال مبسط
        password,
      );
      return testHash == certificate.privateKeyHash;
    } catch (e) {
      return false;
    }
  }

  /// فك تشفير المفتاح الخاص (مثال مبسط)
  RSAPrivateKey _decodePrivateKey(String encodedPublicKey) {
    // هذا مثال مبسط - في التطبيق الحقيقي يجب تخزين المفتاح الخاص بشكل آمن
    throw UnimplementedError('يجب تنفيذ فك تشفير المفتاح الخاص بشكل آمن');
  }

  /// إنشاء التوقيع الرقمي
  Future<Map<String, String>> _createSignature({
    required SignableDocument document,
    required DigitalCertificate certificate,
    required String password,
  }) async {
    try {
      // إنشاء البيانات المراد توقيعها
      final dataToSign =
          '${document.fileHash}:${document.title}:${DateTime.now().toIso8601String()}';
      final dataBytes = utf8.encode(dataToSign);

      // حساب hash البيانات
      final digest = sha256.convert(dataBytes);

      // في التطبيق الحقيقي، يجب استخدام المفتاح الخاص لإنشاء التوقيع
      final signatureData = base64Encode(digest.bytes);
      final signatureHash = sha256
          .convert(utf8.encode(signatureData))
          .toString();

      return {'signature': signatureData, 'hash': signatureHash};
    } catch (e) {
      throw ValidationException('خطأ في إنشاء التوقيع الرقمي: $e');
    }
  }

  /// تشفير بيانات الشهادة
  String _encodeCertificate(DigitalCertificate certificate) {
    try {
      final certData = {
        'subject': certificate.subject,
        'issuer': certificate.issuer,
        'serialNumber': certificate.serialNumber,
        'validFrom': certificate.validFrom.toIso8601String(),
        'validTo': certificate.validTo.toIso8601String(),
        'publicKey': certificate.publicKey,
        'fingerprint': certificate.fingerprint,
      };
      return base64Encode(utf8.encode(jsonEncode(certData)));
    } catch (e) {
      throw ValidationException('خطأ في تشفير بيانات الشهادة: $e');
    }
  }

  /// تحديث عداد التوقيعات في الوثيقة
  Future<SignableDocument> _updateDocumentSignatureCount(
    SignableDocument document,
  ) async {
    try {
      final db = await _databaseHelper.database;
      final newCount = document.currentSignatures + 1;

      String newStatus = document.status;
      if (newCount >= document.requiredSignatures) {
        newStatus = DocumentStatus.signed.value;
      } else {
        newStatus = DocumentStatus.pendingSignature.value;
      }

      final updatedDocument = document.copyWith(
        currentSignatures: newCount,
        status: newStatus,
        updatedAt: DateTime.now(),
      );

      await db.update(
        'signable_documents',
        updatedDocument.toMap(),
        where: 'id = ?',
        whereArgs: [document.id],
      );

      return updatedDocument;
    } catch (e) {
      throw ValidationException('خطأ في تحديث عداد التوقيعات: $e');
    }
  }

  /// إلغاء صحة التوقيع
  Future<void> _invalidateSignature(int signatureId, String reason) async {
    try {
      final db = await _databaseHelper.database;
      await db.update(
        'digital_signatures',
        {'is_valid': 0, 'invalidation_reason': reason},
        where: 'id = ?',
        whereArgs: [signatureId],
      );

      LoggingService.warning(
        'تم إلغاء صحة التوقيع',
        category: 'DigitalSignatureService',
        data: {'signatureId': signatureId, 'reason': reason},
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في إلغاء صحة التوقيع',
        category: 'DigitalSignatureService',
        data: {'signatureId': signatureId, 'error': e.toString()},
      );
    }
  }

  /// التحقق من التوقيع الرقمي
  Future<bool> _verifyDigitalSignature(
    DigitalSignature signature,
    SignableDocument document,
  ) async {
    try {
      // في التطبيق الحقيقي، يجب التحقق من التوقيع باستخدام المفتاح العام
      // هذا مثال مبسط
      final expectedData = '${document.fileHash}:${document.title}';
      final signatureBytes = base64Decode(signature.signatureData);
      final computedHash = sha256.convert(signatureBytes).toString();

      return computedHash == signature.signatureHash;
    } catch (e) {
      LoggingService.error(
        'خطأ في التحقق من التوقيع الرقمي',
        category: 'DigitalSignatureService',
        data: {'signatureId': signature.id, 'error': e.toString()},
      );
      return false;
    }
  }

  /// إرسال إشعار التوقيع
  Future<void> _sendSignatureNotification(
    SignableDocument document,
    DigitalSignature signature,
  ) async {
    try {
      // إشعار منشئ الوثيقة
      await _notificationService.sendNotification(
        userId: document.createdBy,
        title: 'تم توقيع الوثيقة',
        message:
            'تم توقيع الوثيقة "${document.title}" من قبل ${signature.signerName}',
        type: 'document_signed',
        data: {
          'documentId': document.id,
          'signatureId': signature.id,
          'signerName': signature.signerName,
        },
      );

      // إذا اكتملت جميع التوقيعات المطلوبة
      if (document.status == DocumentStatus.signed.value) {
        await _notificationService.sendNotification(
          userId: document.createdBy,
          title: 'اكتملت جميع التوقيعات',
          message:
              'تم توقيع الوثيقة "${document.title}" من جميع الأطراف المطلوبة',
          type: 'document_fully_signed',
          data: {
            'documentId': document.id,
            'totalSignatures': document.currentSignatures,
          },
        );
      }
    } catch (e) {
      LoggingService.error(
        'خطأ في إرسال إشعار التوقيع',
        category: 'DigitalSignatureService',
        data: {'documentId': document.id, 'error': e.toString()},
      );
    }
  }
}
