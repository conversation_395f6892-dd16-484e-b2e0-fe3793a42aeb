/// خدمة إدارة الأقسام
/// توفر عمليات إدارة الأقسام في نظام الموارد البشرية
library;

import '../database/database_helper.dart';
import '../constants/app_constants.dart';
import '../services/logging_service.dart';
import '../services/audit_service.dart';
import '../exceptions/validation_exception.dart';
import '../models/hr_models.dart';

class DepartmentService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  /// إنشاء قسم جديد
  Future<Department> createDepartment({
    required String name,
    required String code,
    String? description,
    int? managerId,
    bool isActive = true,
  }) async {
    try {
      // التحقق من صحة البيانات
      if (name.trim().isEmpty) {
        throw ValidationException('اسم القسم مطلوب');
      }
      if (code.trim().isEmpty) {
        throw ValidationException('رمز القسم مطلوب');
      }

      // التحقق من عدم تكرار الرمز
      final existingDepartment = await getDepartmentByCode(code);
      if (existingDepartment != null) {
        throw ValidationException('رمز القسم موجود مسبقاً');
      }

      final department = Department(
        name: name.trim(),
        code: code.trim().toUpperCase(),
        description: description?.trim(),
        managerId: managerId,
        isActive: isActive,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // حفظ القسم في قاعدة البيانات
      final db = await _databaseHelper.database;
      final id = await db.insert(
        AppConstants.departmentsTable,
        department.toMap(),
      );

      final savedDepartment = department.copyWith(id: id);

      // تسجيل في سجل المراجعة
      await AuditService.log(
        action: 'CREATE',
        entityType: 'Department',
        entityId: id,
        description: 'إنشاء قسم جديد',
        newValues: savedDepartment.toMap(),
      );

      LoggingService.info(
        'تم إنشاء قسم جديد بنجاح',
        category: 'DepartmentService',
        data: {'departmentId': id, 'name': name, 'code': code},
      );

      return savedDepartment;
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء القسم',
        category: 'DepartmentService',
        data: {'name': name, 'code': code, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// الحصول على جميع الأقسام
  Future<List<Department>> getAllDepartments({bool activeOnly = false}) async {
    try {
      final db = await _databaseHelper.database;

      String? whereClause;
      List<dynamic>? whereArgs;

      if (activeOnly) {
        whereClause = 'is_active = ?';
        whereArgs = [1];
      }

      final result = await db.query(
        AppConstants.departmentsTable,
        where: whereClause,
        whereArgs: whereArgs,
        orderBy: 'name ASC',
      );

      return result.map((map) => Department.fromMap(map)).toList();
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب الأقسام',
        category: 'DepartmentService',
        data: {'error': e.toString()},
      );
      return [];
    }
  }

  /// الحصول على قسم بالمعرف
  Future<Department?> getDepartmentById(int id) async {
    try {
      final db = await _databaseHelper.database;
      final result = await db.query(
        AppConstants.departmentsTable,
        where: 'id = ?',
        whereArgs: [id],
        limit: 1,
      );

      if (result.isNotEmpty) {
        return Department.fromMap(result.first);
      }
      return null;
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب القسم',
        category: 'DepartmentService',
        data: {'departmentId': id, 'error': e.toString()},
      );
      return null;
    }
  }

  /// الحصول على قسم بالرمز
  Future<Department?> getDepartmentByCode(String code) async {
    try {
      final db = await _databaseHelper.database;
      final result = await db.query(
        AppConstants.departmentsTable,
        where: 'code = ?',
        whereArgs: [code.toUpperCase()],
        limit: 1,
      );

      if (result.isNotEmpty) {
        return Department.fromMap(result.first);
      }
      return null;
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب القسم بالرمز',
        category: 'DepartmentService',
        data: {'code': code, 'error': e.toString()},
      );
      return null;
    }
  }

  /// تحديث قسم
  Future<Department> updateDepartment(Department department) async {
    try {
      if (department.id == null) {
        throw ValidationException('معرف القسم مطلوب للتحديث');
      }

      // التحقق من عدم تكرار الرمز مع أقسام أخرى
      final existingDepartment = await getDepartmentByCode(department.code);
      if (existingDepartment != null &&
          existingDepartment.id != department.id) {
        throw ValidationException('رمز القسم موجود مسبقاً');
      }

      final updatedDepartment = department.copyWith(updatedAt: DateTime.now());

      final db = await _databaseHelper.database;
      await db.update(
        AppConstants.departmentsTable,
        updatedDepartment.toMap(),
        where: 'id = ?',
        whereArgs: [department.id],
      );

      // تسجيل في سجل المراجعة
      await AuditService.log(
        action: 'UPDATE',
        entityType: 'Department',
        entityId: department.id!,
        description: 'تحديث بيانات القسم',
        newValues: updatedDepartment.toMap(),
      );

      LoggingService.info(
        'تم تحديث القسم بنجاح',
        category: 'DepartmentService',
        data: {'departmentId': department.id},
      );

      return updatedDepartment;
    } catch (e) {
      LoggingService.error(
        'خطأ في تحديث القسم',
        category: 'DepartmentService',
        data: {'departmentId': department.id, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// حذف قسم
  Future<void> deleteDepartment(int id) async {
    try {
      final db = await _databaseHelper.database;

      // التحقق من وجود موظفين في القسم
      final employeesCount = await db.rawQuery(
        'SELECT COUNT(*) as count FROM ${AppConstants.employeesTable} WHERE department_id = ?',
        [id],
      );

      final count = employeesCount.first['count'] as int;
      if (count > 0) {
        throw ValidationException(
          'لا يمكن حذف القسم لوجود $count موظف/موظفين مرتبطين به',
        );
      }

      // التحقق من وجود مناصب في القسم
      final positionsCount = await db.rawQuery(
        'SELECT COUNT(*) as count FROM ${AppConstants.positionsTable} WHERE department_id = ?',
        [id],
      );

      final posCount = positionsCount.first['count'] as int;
      if (posCount > 0) {
        throw ValidationException(
          'لا يمكن حذف القسم لوجود $posCount منصب/مناصب مرتبطة به',
        );
      }

      // حذف القسم
      await db.delete(
        AppConstants.departmentsTable,
        where: 'id = ?',
        whereArgs: [id],
      );

      // تسجيل في سجل المراجعة
      await AuditService.log(
        action: 'DELETE',
        entityType: 'Department',
        entityId: id,
        description: 'حذف القسم',
      );

      LoggingService.info(
        'تم حذف القسم بنجاح',
        category: 'DepartmentService',
        data: {'departmentId': id},
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في حذف القسم',
        category: 'DepartmentService',
        data: {'departmentId': id, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// تفعيل/إلغاء تفعيل قسم
  Future<void> toggleDepartmentStatus(int id) async {
    try {
      final department = await getDepartmentById(id);
      if (department == null) {
        throw ValidationException('القسم غير موجود');
      }

      final updatedDepartment = department.copyWith(
        isActive: !department.isActive,
        updatedAt: DateTime.now(),
      );

      await updateDepartment(updatedDepartment);

      LoggingService.info(
        'تم تغيير حالة القسم بنجاح',
        category: 'DepartmentService',
        data: {'departmentId': id, 'newStatus': updatedDepartment.isActive},
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في تغيير حالة القسم',
        category: 'DepartmentService',
        data: {'departmentId': id, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// إنشاء أقسام افتراضية
  Future<void> createDefaultDepartments() async {
    try {
      final existingDepartments = await getAllDepartments();
      if (existingDepartments.isNotEmpty) {
        LoggingService.info(
          'الأقسام موجودة مسبقاً',
          category: 'DepartmentService',
        );
        return;
      }

      final defaultDepartments = [
        Department(
          name: 'الإدارة العامة',
          code: 'MGMT',
          description: 'الإدارة العليا والتخطيط الاستراتيجي',
          isActive: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        Department(
          name: 'المحاسبة والمالية',
          code: 'FIN',
          description: 'إدارة الحسابات والشؤون المالية',
          isActive: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        Department(
          name: 'المبيعات والتسويق',
          code: 'SALES',
          description: 'إدارة المبيعات والأنشطة التسويقية',
          isActive: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        Department(
          name: 'الموارد البشرية',
          code: 'HR',
          description: 'إدارة شؤون الموظفين والتطوير',
          isActive: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        Department(
          name: 'تقنية المعلومات',
          code: 'IT',
          description: 'إدارة الأنظمة التقنية والدعم الفني',
          isActive: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
      ];

      for (final department in defaultDepartments) {
        await createDepartment(
          name: department.name,
          code: department.code,
          description: department.description,
          isActive: department.isActive,
        );
      }

      LoggingService.info(
        'تم إنشاء ${defaultDepartments.length} قسم افتراضي',
        category: 'DepartmentService',
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء الأقسام الافتراضية',
        category: 'DepartmentService',
        data: {'error': e.toString()},
      );
      rethrow;
    }
  }
}
