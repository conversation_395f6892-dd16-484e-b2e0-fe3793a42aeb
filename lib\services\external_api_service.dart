/// خدمة API للتكامل الخارجي
/// توفر جميع العمليات المطلوبة لإدارة API والمصادقة والتوثيق
library;

import 'dart:convert';
import 'dart:math';
import 'package:crypto/crypto.dart';
import '../database/database_helper.dart';
import '../services/logging_service.dart';
import '../services/audit_service.dart';
import '../services/employee_service.dart';
import '../services/payroll_service.dart';
import '../exceptions/validation_exception.dart';
import '../models/api_models.dart';
import '../constants/app_constants.dart';

class ExternalApiService {
  static final ExternalApiService _instance = ExternalApiService._internal();
  factory ExternalApiService() => _instance;
  ExternalApiService._internal();

  final DatabaseHelper _databaseHelper = DatabaseHelper();
  final EmployeeService _employeeService = EmployeeService();
  final PayrollService _payrollService = PayrollService();

  /// إنشاء مفتاح API جديد
  Future<ApiKey> createApiKey({
    required String keyName,
    required int userId,
    required String userName,
    required List<String> permissions,
    required List<String> allowedEndpoints,
    DateTime? expiresAt,
    int requestLimit = -1, // -1 for unlimited
    String? ipWhitelist,
    Map<String, dynamic> metadata = const {},
  }) async {
    try {
      // التحقق من صحة البيانات
      await _validateApiKeyData(
        keyName: keyName,
        permissions: permissions,
        allowedEndpoints: allowedEndpoints,
      );

      // توليد مفتاح API وسر
      final keyValue = _generateApiKey();
      final keySecret = _generateApiSecret();

      final db = await _databaseHelper.database;
      final now = DateTime.now();

      final apiKey = ApiKey(
        keyName: keyName,
        keyValue: keyValue,
        keySecret: keySecret,
        userId: userId,
        userName: userName,
        permissions: permissions,
        allowedEndpoints: allowedEndpoints,
        status: ApiKeyStatus.active.value,
        expiresAt: expiresAt,
        requestCount: 0,
        requestLimit: requestLimit,
        ipWhitelist: ipWhitelist,
        metadata: metadata,
        createdAt: now,
        updatedAt: now,
      );

      final id = await db.insert('api_keys', apiKey.toMap());

      final newApiKey = apiKey.copyWith(id: id);

      // تسجيل في سجل المراجعة
      await AuditService.log(
        action: 'CREATE',
        entityType: 'ApiKey',
        entityId: id,
        description: 'إنشاء مفتاح API جديد: $keyName',
        newValues: newApiKey.toMap(),
      );

      LoggingService.info(
        'تم إنشاء مفتاح API جديد بنجاح',
        category: 'ExternalApiService',
        data: {'id': id, 'keyName': keyName, 'userId': userId},
      );

      return newApiKey;
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء مفتاح API',
        category: 'ExternalApiService',
        data: {'keyName': keyName, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// التحقق من صحة مفتاح API
  Future<ApiKey?> validateApiKey(String keyValue, String keySecret) async {
    try {
      final db = await _databaseHelper.database;
      final result = await db.query(
        'api_keys',
        where: 'key_value = ? AND key_secret = ? AND status = ?',
        whereArgs: [keyValue, keySecret, ApiKeyStatus.active.value],
        limit: 1,
      );

      if (result.isEmpty) {
        return null;
      }

      final apiKey = ApiKey.fromMap(result.first);

      // التحقق من انتهاء الصلاحية
      if (apiKey.expiresAt != null &&
          apiKey.expiresAt!.isBefore(DateTime.now())) {
        await _updateApiKeyStatus(apiKey.id!, ApiKeyStatus.expired.value);
        return null;
      }

      // التحقق من حد الطلبات
      if (apiKey.requestLimit > 0 &&
          apiKey.requestCount >= apiKey.requestLimit) {
        return null;
      }

      return apiKey;
    } catch (e) {
      LoggingService.error(
        'خطأ في التحقق من مفتاح API',
        category: 'ExternalApiService',
        data: {'keyValue': keyValue, 'error': e.toString()},
      );
      return null;
    }
  }

  /// معالجة طلب API
  Future<ApiResponse<dynamic>> handleApiRequest({
    required String endpoint,
    required String method,
    required Map<String, dynamic> headers,
    required Map<String, dynamic> queryParams,
    required Map<String, dynamic> body,
    required String ipAddress,
    required String userAgent,
    required ApiKey apiKey,
  }) async {
    final startTime = DateTime.now();

    try {
      // التحقق من الصلاحيات
      if (!_hasPermissionForEndpoint(apiKey, endpoint, method)) {
        return ApiResponse.error(
          message: 'ليس لديك صلاحية للوصول إلى هذه النقطة',
          statusCode: 403,
        );
      }

      // التحقق من القائمة البيضاء للـ IP
      if (!_isIpAllowed(apiKey, ipAddress)) {
        return ApiResponse.error(
          message: 'عنوان IP غير مسموح',
          statusCode: 403,
        );
      }

      // معالجة الطلب حسب النقطة
      final response = await _processEndpoint(
        endpoint: endpoint,
        method: method,
        queryParams: queryParams,
        body: body,
      );

      // تحديث عداد الطلبات
      await _incrementRequestCount(apiKey.id!);

      // تسجيل الطلب
      await _logApiRequest(
        apiKey: apiKey,
        endpoint: endpoint,
        method: method,
        headers: headers,
        queryParams: queryParams,
        body: body,
        ipAddress: ipAddress,
        userAgent: userAgent,
        responseStatus: response.statusCode,
        responseBody: jsonEncode(response.toJson()),
        responseTime: DateTime.now().difference(startTime).inMilliseconds,
        isSuccessful: response.success,
      );

      return response;
    } catch (e) {
      final errorResponse = ApiResponse.error(
        message: 'خطأ في معالجة الطلب: $e',
        statusCode: 500,
      );

      // تسجيل الطلب الفاشل
      await _logApiRequest(
        apiKey: apiKey,
        endpoint: endpoint,
        method: method,
        headers: headers,
        queryParams: queryParams,
        body: body,
        ipAddress: ipAddress,
        userAgent: userAgent,
        responseStatus: 500,
        responseTime: DateTime.now().difference(startTime).inMilliseconds,
        isSuccessful: false,
        errorMessage: e.toString(),
      );

      LoggingService.error(
        'خطأ في معالجة طلب API',
        category: 'ExternalApiService',
        data: {
          'endpoint': endpoint,
          'method': method,
          'apiKeyId': apiKey.id,
          'error': e.toString(),
        },
      );

      return errorResponse;
    }
  }

  /// الحصول على مفاتيح API للمستخدم
  Future<List<ApiKey>> getApiKeysForUser(int userId) async {
    try {
      final db = await _databaseHelper.database;
      final result = await db.query(
        'api_keys',
        where: 'user_id = ?',
        whereArgs: [userId],
        orderBy: 'created_at DESC',
      );

      return result.map((map) => ApiKey.fromMap(map)).toList();
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب مفاتيح API للمستخدم',
        category: 'ExternalApiService',
        data: {'userId': userId, 'error': e.toString()},
      );
      return [];
    }
  }

  /// إلغاء مفتاح API
  Future<void> revokeApiKey(int apiKeyId) async {
    try {
      await _updateApiKeyStatus(apiKeyId, ApiKeyStatus.revoked.value);

      // تسجيل في سجل المراجعة
      await AuditService.log(
        action: 'UPDATE',
        entityType: 'ApiKey',
        entityId: apiKeyId,
        description: 'إلغاء مفتاح API',
      );

      LoggingService.info(
        'تم إلغاء مفتاح API بنجاح',
        category: 'ExternalApiService',
        data: {'apiKeyId': apiKeyId},
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في إلغاء مفتاح API',
        category: 'ExternalApiService',
        data: {'apiKeyId': apiKeyId, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// الحصول على إحصائيات API
  Future<Map<String, dynamic>> getApiStatistics({
    int? userId,
    DateTime? fromDate,
    DateTime? toDate,
  }) async {
    try {
      final db = await _databaseHelper.database;

      String whereClause = '1=1';
      List<dynamic> whereArgs = [];

      if (userId != null) {
        whereClause +=
            ' AND api_key_id IN (SELECT id FROM api_keys WHERE user_id = ?)';
        whereArgs.add(userId);
      }

      if (fromDate != null) {
        whereClause += ' AND request_time >= ?';
        whereArgs.add(fromDate.toIso8601String());
      }

      if (toDate != null) {
        whereClause += ' AND request_time <= ?';
        whereArgs.add(toDate.toIso8601String());
      }

      // إجمالي الطلبات
      final totalRequestsResult = await db.rawQuery(
        'SELECT COUNT(*) as total FROM api_requests WHERE $whereClause',
        whereArgs,
      );
      final totalRequests = totalRequestsResult.first['total'] as int;

      // الطلبات الناجحة
      final successfulRequestsResult = await db.rawQuery(
        'SELECT COUNT(*) as total FROM api_requests WHERE $whereClause AND is_successful = 1',
        whereArgs,
      );
      final successfulRequests = successfulRequestsResult.first['total'] as int;

      // متوسط وقت الاستجابة
      final avgResponseTimeResult = await db.rawQuery(
        'SELECT AVG(response_time) as avg_time FROM api_requests WHERE $whereClause',
        whereArgs,
      );
      final avgResponseTime =
          avgResponseTimeResult.first['avg_time'] as double? ?? 0.0;

      // أكثر النقاط استخداماً
      final topEndpointsResult = await db.rawQuery(
        'SELECT endpoint, COUNT(*) as count FROM api_requests WHERE $whereClause GROUP BY endpoint ORDER BY count DESC LIMIT 10',
        whereArgs,
      );

      return {
        'totalRequests': totalRequests,
        'successfulRequests': successfulRequests,
        'failedRequests': totalRequests - successfulRequests,
        'successRate': totalRequests > 0
            ? (successfulRequests / totalRequests * 100)
            : 0,
        'avgResponseTime': avgResponseTime,
        'topEndpoints': topEndpointsResult,
      };
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب إحصائيات API',
        category: 'ExternalApiService',
        data: {'error': e.toString()},
      );
      return {
        'totalRequests': 0,
        'successfulRequests': 0,
        'failedRequests': 0,
        'successRate': 0,
        'avgResponseTime': 0,
        'topEndpoints': [],
      };
    }
  }

  /// الحصول على نقاط النهاية المتاحة
  List<ApiEndpoint> getAvailableEndpoints() {
    return [
      ApiEndpoint(
        path: '/api/v1/employees',
        method: 'GET',
        description: 'الحصول على قائمة الموظفين',
        requiredPermissions: [ApiPermission.readEmployees.value],
        parameters: {
          'page': {'type': 'integer', 'description': 'رقم الصفحة'},
          'limit': {'type': 'integer', 'description': 'عدد العناصر في الصفحة'},
          'search': {'type': 'string', 'description': 'البحث في الأسماء'},
        },
        responseSchema: {
          'type': 'object',
          'properties': {
            'data': {
              'type': 'array',
              'items': {'type': 'object'},
            },
            'meta': {'type': 'object'},
          },
        },
        isPublic: false,
        rateLimitPerMinute: 60,
      ),
      ApiEndpoint(
        path: '/api/v1/employees',
        method: 'POST',
        description: 'إضافة موظف جديد',
        requiredPermissions: [ApiPermission.writeEmployees.value],
        parameters: {
          'employee_number': {'type': 'string', 'required': true},
          'full_name': {'type': 'string', 'required': true},
          'email': {'type': 'string', 'required': false},
          'phone': {'type': 'string', 'required': false},
        },
        responseSchema: {
          'type': 'object',
          'properties': {
            'data': {'type': 'object'},
          },
        },
        isPublic: false,
        rateLimitPerMinute: 30,
      ),
      ApiEndpoint(
        path: '/api/v1/payroll',
        method: 'GET',
        description: 'الحصول على بيانات الرواتب',
        requiredPermissions: [ApiPermission.readPayroll.value],
        parameters: {
          'month': {'type': 'integer', 'description': 'الشهر'},
          'year': {'type': 'integer', 'description': 'السنة'},
          'employee_id': {'type': 'integer', 'description': 'معرف الموظف'},
        },
        responseSchema: {
          'type': 'object',
          'properties': {
            'data': {
              'type': 'array',
              'items': {'type': 'object'},
            },
          },
        },
        isPublic: false,
        rateLimitPerMinute: 60,
      ),
    ];
  }

  // ===== الطرق المساعدة =====

  /// التحقق من صحة بيانات مفتاح API
  Future<void> _validateApiKeyData({
    required String keyName,
    required List<String> permissions,
    required List<String> allowedEndpoints,
  }) async {
    if (keyName.trim().isEmpty) {
      throw ValidationException('اسم مفتاح API مطلوب');
    }

    if (keyName.length < 3) {
      throw ValidationException('اسم مفتاح API يجب أن يكون 3 أحرف على الأقل');
    }

    if (permissions.isEmpty) {
      throw ValidationException('يجب تحديد صلاحية واحدة على الأقل');
    }

    final validPermissions = ApiPermission.values.map((e) => e.value).toList();
    for (final permission in permissions) {
      if (!validPermissions.contains(permission)) {
        throw ValidationException('صلاحية غير صحيحة: $permission');
      }
    }

    if (allowedEndpoints.isEmpty) {
      throw ValidationException('يجب تحديد نقطة نهاية واحدة على الأقل');
    }
  }

  /// توليد مفتاح API
  String _generateApiKey() {
    const chars =
        'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    final random = Random.secure();
    return List.generate(
      32,
      (index) => chars[random.nextInt(chars.length)],
    ).join();
  }

  /// توليد سر مفتاح API
  String _generateApiSecret() {
    const chars =
        'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#\$%^&*';
    final random = Random.secure();
    final secret = List.generate(
      64,
      (index) => chars[random.nextInt(chars.length)],
    ).join();

    // تشفير السر
    final bytes = utf8.encode(secret);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  /// التحقق من الصلاحيات للنقطة
  bool _hasPermissionForEndpoint(
    ApiKey apiKey,
    String endpoint,
    String method,
  ) {
    // التحقق من النقاط المسموحة
    if (!apiKey.allowedEndpoints.contains('*') &&
        !apiKey.allowedEndpoints.contains(endpoint)) {
      return false;
    }

    // التحقق من الصلاحيات المطلوبة
    final requiredPermissions = _getRequiredPermissions(endpoint, method);

    // إذا كان لديه صلاحية admin، يمكنه الوصول لكل شيء
    if (apiKey.permissions.contains(ApiPermission.admin.value)) {
      return true;
    }

    // التحقق من الصلاحيات المحددة
    for (final permission in requiredPermissions) {
      if (!apiKey.permissions.contains(permission)) {
        return false;
      }
    }

    return true;
  }

  /// الحصول على الصلاحيات المطلوبة للنقطة
  List<String> _getRequiredPermissions(String endpoint, String method) {
    if (endpoint.startsWith('/api/v1/employees')) {
      if (method == 'GET') {
        return [ApiPermission.readEmployees.value];
      } else if (['POST', 'PUT', 'PATCH', 'DELETE'].contains(method)) {
        return [ApiPermission.writeEmployees.value];
      }
    } else if (endpoint.startsWith('/api/v1/payroll')) {
      if (method == 'GET') {
        return [ApiPermission.readPayroll.value];
      } else if (['POST', 'PUT', 'PATCH', 'DELETE'].contains(method)) {
        return [ApiPermission.writePayroll.value];
      }
    } else if (endpoint.startsWith('/api/v1/reports')) {
      if (method == 'GET') {
        return [ApiPermission.readReports.value];
      } else if (['POST', 'PUT', 'PATCH', 'DELETE'].contains(method)) {
        return [ApiPermission.writeReports.value];
      }
    } else if (endpoint.startsWith('/api/v1/attendance')) {
      if (method == 'GET') {
        return [ApiPermission.readAttendance.value];
      } else if (['POST', 'PUT', 'PATCH', 'DELETE'].contains(method)) {
        return [ApiPermission.writeAttendance.value];
      }
    }

    return [];
  }

  /// التحقق من عنوان IP المسموح
  bool _isIpAllowed(ApiKey apiKey, String ipAddress) {
    if (apiKey.ipWhitelist == null || apiKey.ipWhitelist!.isEmpty) {
      return true; // لا توجد قيود IP
    }

    final allowedIps = apiKey.ipWhitelist!
        .split(',')
        .map((ip) => ip.trim())
        .toList();
    return allowedIps.contains(ipAddress) || allowedIps.contains('*');
  }

  /// معالجة نقطة النهاية
  Future<ApiResponse<dynamic>> _processEndpoint({
    required String endpoint,
    required String method,
    required Map<String, dynamic> queryParams,
    required Map<String, dynamic> body,
  }) async {
    try {
      if (endpoint.startsWith('/api/v1/employees')) {
        return await _handleEmployeesEndpoint(method, queryParams, body);
      } else if (endpoint.startsWith('/api/v1/payroll')) {
        return await _handlePayrollEndpoint(method, queryParams, body);
      } else {
        return ApiResponse.error(
          message: 'نقطة النهاية غير موجودة',
          statusCode: 404,
        );
      }
    } catch (e) {
      return ApiResponse.error(
        message: 'خطأ في معالجة النقطة: $e',
        statusCode: 500,
      );
    }
  }

  /// معالجة نقطة الموظفين
  Future<ApiResponse<dynamic>> _handleEmployeesEndpoint(
    String method,
    Map<String, dynamic> queryParams,
    Map<String, dynamic> body,
  ) async {
    switch (method) {
      case 'GET':
        final employees = await _employeeService.getAllEmployees();

        // تطبيق الفلاتر
        var filteredEmployees = employees;

        if (queryParams.containsKey('search')) {
          final search = queryParams['search'].toString().toLowerCase();
          filteredEmployees = employees
              .where(
                (emp) =>
                    emp.fullName.toLowerCase().contains(search) ||
                    emp.employeeNumber.toLowerCase().contains(search),
              )
              .toList();
        }

        // تطبيق التصفح
        final page = int.tryParse(queryParams['page']?.toString() ?? '1') ?? 1;
        final limit =
            int.tryParse(queryParams['limit']?.toString() ?? '20') ?? 20;
        final offset = (page - 1) * limit;

        final paginatedEmployees = filteredEmployees
            .skip(offset)
            .take(limit)
            .toList();

        return ApiResponse.success(
          message: 'تم جلب بيانات الموظفين بنجاح',
          data: paginatedEmployees.map((emp) => emp.toMap()).toList(),
          meta: {
            'total': filteredEmployees.length,
            'page': page,
            'limit': limit,
            'totalPages': (filteredEmployees.length / limit).ceil(),
          },
        );

      case 'POST':
        // إضافة موظف جديد
        if (!body.containsKey('employee_number') ||
            !body.containsKey('full_name')) {
          return ApiResponse.error(
            message: 'رقم الموظف والاسم الكامل مطلوبان',
            statusCode: 400,
          );
        }

        // هنا يجب استخدام EmployeeService لإضافة الموظف
        // هذا مثال مبسط
        return ApiResponse.success(
          message: 'تم إضافة الموظف بنجاح',
          data: body,
          statusCode: 201,
        );

      default:
        return ApiResponse.error(
          message: 'طريقة HTTP غير مدعومة',
          statusCode: 405,
        );
    }
  }

  /// معالجة نقطة الرواتب
  Future<ApiResponse<dynamic>> _handlePayrollEndpoint(
    String method,
    Map<String, dynamic> queryParams,
    Map<String, dynamic> body,
  ) async {
    switch (method) {
      case 'GET':
        final month = int.tryParse(queryParams['month']?.toString() ?? '');
        final year = int.tryParse(queryParams['year']?.toString() ?? '');
        final employeeId = int.tryParse(
          queryParams['employee_id']?.toString() ?? '',
        );

        if (month == null || year == null) {
          return ApiResponse.error(
            message: 'الشهر والسنة مطلوبان',
            statusCode: 400,
          );
        }

        // جلب بيانات الرواتب
        final payrollRecords = await _payrollService.getPayrollRecords(
          month: month,
          year: year,
          employeeId: employeeId,
        );

        return ApiResponse.success(
          message: 'تم جلب بيانات الرواتب بنجاح',
          data: payrollRecords.map((record) => record.toMap()).toList(),
        );

      default:
        return ApiResponse.error(
          message: 'طريقة HTTP غير مدعومة',
          statusCode: 405,
        );
    }
  }

  /// تحديث حالة مفتاح API
  Future<void> _updateApiKeyStatus(int apiKeyId, String status) async {
    try {
      final db = await _databaseHelper.database;
      await db.update(
        'api_keys',
        {'status': status, 'updated_at': DateTime.now().toIso8601String()},
        where: 'id = ?',
        whereArgs: [apiKeyId],
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في تحديث حالة مفتاح API',
        category: 'ExternalApiService',
        data: {'apiKeyId': apiKeyId, 'status': status, 'error': e.toString()},
      );
    }
  }

  /// زيادة عداد الطلبات
  Future<void> _incrementRequestCount(int apiKeyId) async {
    try {
      final db = await _databaseHelper.database;
      await db.rawUpdate(
        'UPDATE api_keys SET request_count = request_count + 1, last_used_at = ? WHERE id = ?',
        [DateTime.now().toIso8601String(), apiKeyId],
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في زيادة عداد الطلبات',
        category: 'ExternalApiService',
        data: {'apiKeyId': apiKeyId, 'error': e.toString()},
      );
    }
  }

  /// تسجيل طلب API
  Future<void> _logApiRequest({
    required ApiKey apiKey,
    required String endpoint,
    required String method,
    required Map<String, dynamic> headers,
    required Map<String, dynamic> queryParams,
    required Map<String, dynamic> body,
    required String ipAddress,
    required String userAgent,
    required int responseStatus,
    String? responseBody,
    required int responseTime,
    required bool isSuccessful,
    String? errorMessage,
  }) async {
    try {
      final db = await _databaseHelper.database;

      final request = ApiRequest(
        apiKeyId: apiKey.keyValue,
        endpoint: endpoint,
        method: method,
        headers: headers,
        queryParams: queryParams,
        body: body,
        ipAddress: ipAddress,
        userAgent: userAgent,
        responseStatus: responseStatus,
        responseBody: responseBody,
        responseTime: responseTime,
        requestTime: DateTime.now(),
        isSuccessful: isSuccessful,
        errorMessage: errorMessage,
      );

      await db.insert('api_requests', request.toMap());
    } catch (e) {
      LoggingService.error(
        'خطأ في تسجيل طلب API',
        category: 'ExternalApiService',
        data: {'endpoint': endpoint, 'error': e.toString()},
      );
    }
  }
}
